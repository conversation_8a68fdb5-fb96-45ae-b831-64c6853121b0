# UBLOOD Clone

A community-driven blood donation platform that connects blood donors and recipients in real-time through geo-location matching and instant notifications.

## 🩸 About

UBLOOD Clone is a free mobile app and online platform designed to connect blood donors and recipients in real-time. This geo-search network matches registered donors and receivers based on location and blood type, enabling rapid response to urgent blood needs. The platform acts as a connector that turns strangers into saviors by facilitating direct communication between donors and recipients.

## 🏗️ Project Structure

This is a monorepo containing both the backend API and mobile application:

```
ublood-clone/
├── backend/                 # Node.js/Express API server
│   ├── src/                # Source code
│   ├── tests/              # Backend tests
│   ├── package.json        # Backend dependencies
│   └── README.md           # Backend documentation
├── mobile/                 # Flutter mobile application
│   ├── lib/                # Flutter source code
│   ├── test/               # Mobile app tests
│   ├── android/            # Android platform files
│   ├── ios/                # iOS platform files
│   ├── pubspec.yaml        # Flutter dependencies
│   └── README.md           # Mobile app documentation
├── docs/                   # Project documentation
├── .kiro/                  # Kiro AI assistant configuration
├── package.json            # Workspace configuration
├── .gitignore              # Git ignore rules
└── README.md               # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js (v18.0.0 or higher)
- npm (v8.0.0 or higher)
- Flutter SDK (latest stable version)
- Firebase CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ublood-clone
   ```

2. **Install workspace dependencies**
   ```bash
   npm install
   ```

3. **Set up backend**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Configure your environment variables
   ```

4. **Set up mobile app**
   ```bash
   cd mobile
   flutter pub get
   # Configure Firebase for your project
   ```

### Development

#### Backend Development
```bash
# Start backend development server
npm run backend:dev

# Run backend tests
npm run backend:test

# Build backend for production
npm run backend:build
```

#### Mobile Development
```bash
# Start mobile app in development
npm run mobile:dev

# Run mobile tests
npm run mobile:test

# Build mobile app
npm run mobile:build
```

#### Full Stack Development
```bash
# Start both backend and mobile in development mode
npm run dev
```

## 🏛️ Architecture

### Backend (Node.js/Express)
- **API Server**: RESTful API with WebSocket support
- **Database**: Firebase Firestore for real-time data
- **Authentication**: Firebase Authentication (phone-based)
- **Notifications**: Firebase Cloud Messaging (FCM)
- **Caching**: Redis for session management

### Mobile (Flutter)
- **Cross-platform**: Single codebase for iOS and Android
- **State Management**: Provider/Riverpod pattern
- **Real-time Updates**: WebSocket and FCM integration
- **Offline Support**: Local caching and sync

### Key Features
- 📱 **Real-time Matching**: Instant geo-location based donor matching
- 🔔 **Push Notifications**: Immediate alerts for blood requests
- 🗺️ **Location Services**: GPS-based proximity matching
- 🩸 **Blood Compatibility**: Automated blood type compatibility checking
- 👤 **User Profiles**: Donor and recipient profile management
- 📊 **Analytics**: Donation tracking and impact metrics
- 🔒 **Security**: Phone verification and secure data handling

## 🛠️ Available Scripts

### Workspace Scripts
- `npm run dev` - Start development servers
- `npm run build` - Build all projects
- `npm run test` - Run all tests
- `npm run lint` - Lint all projects
- `npm run clean` - Clean all build artifacts

### Backend Scripts
- `npm run backend:dev` - Start backend development server
- `npm run backend:build` - Build backend for production
- `npm run backend:test` - Run backend tests

### Mobile Scripts
- `npm run mobile:dev` - Start mobile development
- `npm run mobile:build` - Build mobile app
- `npm run mobile:test` - Run mobile tests

## 📋 Development Workflow

1. **Feature Development**: Create feature branches from `main`
2. **Testing**: Ensure all tests pass before committing
3. **Code Review**: Submit pull requests for review
4. **Deployment**: Automated deployment through CI/CD pipeline

## 🔧 Configuration

### Environment Variables

Create `.env` files in both `backend/` and `mobile/` directories:

**Backend (.env)**
```
NODE_ENV=development
PORT=3000
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY=your-private-key
REDIS_URL=redis://localhost:6379
```

**Mobile (Flutter environment configuration)**
- Configure Firebase for iOS and Android platforms
- Set up platform-specific configurations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in the `docs/` folder
- Review the individual README files in `backend/` and `mobile/` directories

## ⚠️ Important Safety Notice

This platform facilitates connections between blood donors and recipients but does not handle blood collection, testing, storage, or transport. All blood donations should be conducted through certified medical facilities and blood banks following proper medical protocols and safety guidelines.

---

**Built with ❤️ to help save lives through community connection**