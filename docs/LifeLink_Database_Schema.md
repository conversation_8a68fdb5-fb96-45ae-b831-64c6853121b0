# 🗄️ LifeLink Firestore Database Schema

## 📋 Collections Overview

### 1. **users** Collection
```javascript
{
  id: "user_12345", // Auto-generated document ID
  phoneNumber: "+*************",
  userType: "hospital" | "donor" | "blood_bank" | "oxygen_supplier" | "admin",
  profile: {
    name: "Lagos University Teaching Hospital",
    email: "<EMAIL>",
    address: {
      street: "Idi-Araba, Surulere",
      city: "Lagos",
      state: "Lagos",
      country: "Nigeria",
      coordinates: {
        latitude: 6.5244,
        longitude: 3.3792
      }
    },
    verificationStatus: "pending" | "verified" | "rejected",
    verificationDocuments: ["cac_certificate.pdf", "medical_license.pdf"],
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
    isActive: true
  },
  // Hospital-specific fields
  hospitalInfo: {
    licenseNumber: "LUTH/2024/001",
    capacity: 500,
    departments: ["emergency", "surgery", "pediatrics"],
    operatingHours: {
      monday: { open: "00:00", close: "23:59" },
      // ... other days
    }
  },
  // Blood bank specific fields
  bloodBankInfo: {
    licenseNumber: "BB/LAG/2024/001",
    storageCapacity: 1000,
    certifications: ["WHO", "NAFDAC"],
    operatingHours: {
      monday: { open: "08:00", close: "18:00" }
    }
  },
  // Donor specific fields
  donorInfo: {
    bloodType: "O+",
    dateOfBirth: "1990-05-15",
    weight: 70,
    lastDonationDate: "2024-01-01T00:00:00Z",
    medicalHistory: {
      hasChronicIllness: false,
      medications: [],
      allergies: []
    },
    isAvailable: true,
    donationCount: 5
  },
  fcmToken: "fcm_token_string",
  whatsappOptIn: true
}
```

### 2. **blood_requests** Collection
```javascript
{
  id: "req_12345",
  requesterId: "user_12345", // Reference to users collection
  requesterType: "hospital",
  bloodType: "O+",
  quantity: 5, // Number of units
  urgency: "critical" | "urgent" | "normal",
  purpose: "surgery" | "emergency" | "treatment" | "research",
  patientInfo: {
    age: 35,
    gender: "male",
    condition: "Road traffic accident",
    isEmergency: true
  },
  location: {
    address: "Lagos University Teaching Hospital",
    coordinates: {
      latitude: 6.5244,
      longitude: 3.3792
    },
    state: "Lagos",
    city: "Lagos"
  },
  contactInfo: {
    primaryContact: "+*************",
    alternateContact: "+*************",
    contactPerson: "Dr. John Doe"
  },
  status: "active" | "fulfilled" | "expired" | "cancelled",
  expiresAt: "2024-01-16T10:30:00Z",
  createdAt: "2024-01-15T10:30:00Z",
  updatedAt: "2024-01-15T10:30:00Z",
  responses: [], // Array of response IDs
  matchedDonors: [], // Array of matched donor IDs
  fulfillmentDetails: {
    fulfilledBy: "user_67890",
    fulfilledAt: "2024-01-15T15:30:00Z",
    quantityFulfilled: 5
  }
}
```

### 3. **blood_inventory** Collection
```javascript
{
  id: "inv_12345",
  supplierId: "user_67890", // Blood bank or hospital ID
  bloodType: "A+",
  quantity: 25,
  expiryDate: "2024-02-15T00:00:00Z",
  collectionDate: "2024-01-10T00:00:00Z",
  donorId: "user_11111", // Optional: if from specific donor
  status: "available" | "reserved" | "expired" | "used",
  location: {
    facilityName: "Lagos Blood Bank",
    coordinates: {
      latitude: 6.5244,
      longitude: 3.3792
    }
  },
  testResults: {
    hivNegative: true,
    hepatitisBNegative: true,
    hepatitisCNegative: true,
    syphilisNegative: true,
    malariaScreened: true
  },
  createdAt: "2024-01-10T08:00:00Z",
  updatedAt: "2024-01-15T10:30:00Z"
}
```

### 4. **oxygen_inventory** Collection
```javascript
{
  id: "oxy_12345",
  supplierId: "user_78901",
  cylinderType: "medical" | "industrial",
  size: "small" | "medium" | "large", // 5L, 10L, 40L
  quantity: 50,
  pressure: 150, // Bar
  purity: 99.5, // Percentage
  location: {
    facilityName: "Lagos Oxygen Supplies",
    coordinates: {
      latitude: 6.5244,
      longitude: 3.3792
    }
  },
  pricePerUnit: 15000, // Naira
  status: "available" | "reserved" | "rented",
  certifications: ["SON", "NAFDAC"],
  createdAt: "2024-01-15T10:30:00Z",
  updatedAt: "2024-01-15T10:30:00Z"
}
```

### 5. **oxygen_requests** Collection
```javascript
{
  id: "oxy_req_12345",
  requesterId: "user_12345",
  requesterType: "hospital",
  cylinderType: "medical",
  size: "large",
  quantity: 10,
  urgency: "critical" | "urgent" | "normal",
  duration: 7, // Days needed
  location: {
    deliveryAddress: "Lagos University Teaching Hospital",
    coordinates: {
      latitude: 6.5244,
      longitude: 3.3792
    }
  },
  contactInfo: {
    primaryContact: "+*************",
    contactPerson: "Dr. Jane Smith"
  },
  status: "active" | "fulfilled" | "expired" | "cancelled",
  budget: {
    maxPricePerUnit: 20000,
    totalBudget: 200000
  },
  expiresAt: "2024-01-20T10:30:00Z",
  createdAt: "2024-01-15T10:30:00Z",
  responses: []
}
```

### 6. **responses** Collection
```javascript
{
  id: "resp_12345",
  requestId: "req_12345",
  requestType: "blood" | "oxygen",
  responderId: "user_67890",
  responderType: "donor" | "blood_bank" | "oxygen_supplier",
  responseType: "offer" | "referral" | "information",
  details: {
    quantityOffered: 3,
    availabilityTime: "2024-01-15T14:00:00Z",
    conditions: "Donor needs transportation",
    pricePerUnit: 15000 // For suppliers
  },
  status: "pending" | "accepted" | "declined" | "expired",
  contactInfo: {
    phone: "+*************",
    preferredContact: "whatsapp" | "call" | "sms"
  },
  createdAt: "2024-01-15T11:00:00Z",
  updatedAt: "2024-01-15T11:00:00Z"
}
```

### 7. **notifications** Collection
```javascript
{
  id: "notif_12345",
  userId: "user_12345",
  type: "blood_request" | "oxygen_request" | "response" | "system",
  title: "New Blood Request Nearby",
  message: "Critical O+ blood needed at LUTH - 2km away",
  data: {
    requestId: "req_12345",
    actionType: "view_request",
    deepLink: "/requests/req_12345"
  },
  channels: ["push", "whatsapp", "sms"],
  status: "sent" | "delivered" | "read" | "failed",
  sentAt: "2024-01-15T10:35:00Z",
  readAt: "2024-01-15T10:40:00Z"
}
```

### 8. **analytics** Collection
```javascript
{
  id: "analytics_daily_2024_01_15",
  date: "2024-01-15",
  type: "daily" | "weekly" | "monthly",
  metrics: {
    totalRequests: 45,
    fulfilledRequests: 38,
    activeUsers: 1250,
    newRegistrations: 15,
    responseTime: 25, // Average minutes
    byBloodType: {
      "O+": { requests: 15, fulfilled: 12 },
      "A+": { requests: 10, fulfilled: 9 }
    },
    byState: {
      "Lagos": { requests: 30, fulfilled: 25 },
      "Abuja": { requests: 15, fulfilled: 13 }
    }
  },
  createdAt: "2024-01-16T00:00:00Z"
}
```

## 🔍 Firestore Indexes

### Composite Indexes Required:
```javascript
// Blood requests by location and urgency
blood_requests: [
  ["status", "urgency", "createdAt"],
  ["bloodType", "status", "location.state"],
  ["location.coordinates", "urgency", "expiresAt"]
]

// User queries
users: [
  ["userType", "profile.verificationStatus", "profile.isActive"],
  ["donorInfo.bloodType", "donorInfo.isAvailable", "profile.address.state"]
]

// Inventory management
blood_inventory: [
  ["bloodType", "status", "expiryDate"],
  ["supplierId", "status", "bloodType"]
]
```

## 🔒 Security Rules Example
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Blood requests readable by all authenticated users
    match /blood_requests/{requestId} {
      allow read: if request.auth != null;
      allow create, update: if request.auth != null && 
        request.auth.uid == resource.data.requesterId;
    }
  }
}
```