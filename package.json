{"name": "ublood-clone", "version": "1.0.0", "description": "A community-driven blood donation platform that connects donors and recipients in real-time", "private": true, "workspaces": ["backend", "mobile"], "scripts": {"dev": "npm run dev --workspace=backend", "build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules", "backend:dev": "npm run dev --workspace=backend", "backend:build": "npm run build --workspace=backend", "backend:test": "npm run test --workspace=backend", "mobile:dev": "npm run dev --workspace=mobile", "mobile:build": "npm run build --workspace=mobile", "mobile:test": "npm run test --workspace=mobile"}, "keywords": ["blood-donation", "healthcare", "mobile-app", "community", "emergency-response"], "author": "UBLOOD Clone Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2", "prettier": "^3.1.1", "eslint": "^8.56.0"}}