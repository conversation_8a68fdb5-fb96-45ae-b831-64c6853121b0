# Database Setup Guide

This guide covers setting up both Firebase and Appwrite backends for the UBLOOD Clone application.

## Firebase Setup

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project named "ublood-clone"
3. Enable Google Analytics (optional)

### 2. Enable Authentication

1. Go to Authentication > Sign-in method
2. Enable "Phone" authentication
3. Configure your phone number for testing (optional)

### 3. Create Firestore Database

1. Go to Firestore Database
2. Create database in production mode
3. Choose your preferred location
4. Deploy the security rules from `firestore.rules`

### 4. Enable Cloud Messaging

1. Go to Cloud Messaging
2. Generate a new private key for your service account
3. Download the service account JSON file

### 5. Environment Variables

Add these to your `.env` file:

```env
# Firebase Configuration
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"ublood-clone",...}
FIREBASE_DATABASE_URL=https://ublood-clone-default-rtdb.firebaseio.com/
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json
```

### 6. Deploy Security Rules

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase in your project
firebase init firestore

# Deploy rules
firebase deploy --only firestore:rules
```

## Appwrite Setup

### 1. Create Appwrite Project

#### Option A: Appwrite Cloud
1. Go to [Appwrite Cloud](https://cloud.appwrite.io/)
2. Create a new project named "ublood-clone"
3. Note your Project ID

#### Option B: Self-hosted
1. Install Appwrite using Docker:
```bash
docker run -it --rm \
    --volume /var/run/docker.sock:/var/run/docker.sock \
    --volume "$(pwd)"/appwrite:/usr/src/code/appwrite:rw \
    --entrypoint="install" \
    appwrite/appwrite:1.4.13
```

### 2. Configure Authentication

1. Go to Auth > Settings
2. Enable Phone authentication
3. Configure your preferred SMS provider (Twilio, etc.)

### 3. Create Database and Collections

1. Go to Databases
2. Create a new database named "ublood_db"
3. Run the setup script:

```bash
cd backend
npm run setup-appwrite
```

Or manually create collections using the Appwrite Console with the following structure:

#### Users Collection
- **Collection ID**: `users`
- **Permissions**: Read: Any, Create/Update/Delete: Users
- **Attributes**:
  - `phoneNumber` (string, required, size: 20)
  - `profile.name` (string, required, size: 100)
  - `profile.bloodType` (enum, required, values: A+, A-, B+, B-, AB+, AB-, O+, O-)
  - `profile.userType` (enum, required, values: donor, recipient, both)
  - `profile.isAvailable` (boolean, required)
  - `profile.location.coordinates.latitude` (float, required)
  - `profile.location.coordinates.longitude` (float, required)
  - `profile.location.address` (string, optional, size: 255)
  - `createdAt` (datetime, required)
  - `updatedAt` (datetime, required)

#### Blood Requests Collection
- **Collection ID**: `blood_requests`
- **Permissions**: Read: Any, Create/Update/Delete: Users
- **Attributes**:
  - `requesterId` (string, required, size: 50)
  - `bloodType` (enum, required, values: A+, A-, B+, B-, AB+, AB-, O+, O-)
  - `quantity` (string, required, size: 50)
  - `urgency` (enum, required, values: normal, urgent, critical)
  - `location.coordinates.latitude` (float, required)
  - `location.coordinates.longitude` (float, required)
  - `location.address` (string, required, size: 255)
  - `contactInfo.name` (string, required, size: 100)
  - `contactInfo.phone` (string, required, size: 20)
  - `description` (string, optional, size: 500)
  - `status` (enum, required, values: active, fulfilled, cancelled, expired)
  - `createdAt` (datetime, required)
  - `updatedAt` (datetime, required)

#### Notifications Collection
- **Collection ID**: `notifications`
- **Permissions**: Read/Create/Update: Users
- **Attributes**:
  - `userId` (string, required, size: 50)
  - `type` (enum, required, values: blood_request, donor_response, request_fulfilled, request_cancelled)
  - `title` (string, required, size: 100)
  - `message` (string, required, size: 255)
  - `status` (enum, required, values: sent, delivered, read, responded)
  - `createdAt` (datetime, required)

### 4. Configure Messaging

1. Go to Messaging
2. Add your push notification providers:
   - **FCM**: Add your Firebase server key
   - **APNS**: Add your Apple certificates

### 5. Environment Variables

Add these to your `.env` file:

```env
# Appwrite Configuration
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=your-project-id
APPWRITE_API_KEY=your-api-key
APPWRITE_DATABASE_ID=ublood_db
APPWRITE_USERS_COLLECTION_ID=users
APPWRITE_BLOOD_REQUESTS_COLLECTION_ID=blood_requests
APPWRITE_NOTIFICATIONS_COLLECTION_ID=notifications
APPWRITE_USER_RESPONSES_COLLECTION_ID=user_responses
APPWRITE_ANALYTICS_COLLECTION_ID=analytics
```

### 6. Create API Key

1. Go to Overview > Integrations
2. Create a new API Key with the following scopes:
   - `databases.read`
   - `databases.write`
   - `users.read`
   - `users.write`
   - `messaging.read`
   - `messaging.write`

## Database Indexes

### Firebase Firestore Indexes

Create these composite indexes in Firestore Console:

```javascript
// Users collection
{
  collectionGroup: "users",
  queryScope: "COLLECTION",
  fields: [
    { fieldPath: "profile.bloodType", order: "ASCENDING" },
    { fieldPath: "profile.isAvailable", order: "ASCENDING" },
    { fieldPath: "profile.userType", order: "ASCENDING" }
  ]
}

// Blood requests collection
{
  collectionGroup: "blood_requests",
  queryScope: "COLLECTION", 
  fields: [
    { fieldPath: "status", order: "ASCENDING" },
    { fieldPath: "urgency", order: "ASCENDING" },
    { fieldPath: "createdAt", order: "DESCENDING" }
  ]
}
```

### Appwrite Database Indexes

Create these indexes in Appwrite Console:

1. **Users Collection**:
   - Index: `phone_availability` - Fields: `phoneNumber` (ASC), `profile.isAvailable` (ASC)
   - Index: `blood_type_available` - Fields: `profile.bloodType` (ASC), `profile.isAvailable` (ASC)

2. **Blood Requests Collection**:
   - Index: `status_urgency` - Fields: `status` (ASC), `urgency` (ASC)
   - Index: `blood_type_status` - Fields: `bloodType` (ASC), `status` (ASC)
   - Index: `created_desc` - Fields: `createdAt` (DESC)

3. **Notifications Collection**:
   - Index: `user_status` - Fields: `userId` (ASC), `status` (ASC)
   - Index: `user_created` - Fields: `userId` (ASC), `createdAt` (DESC)

## Testing the Setup

### Firebase Test

```bash
# Test Firebase connection
curl -X GET http://localhost:3000/health
```

### Appwrite Test

```bash
# Test Appwrite connection
curl -X GET http://localhost:3000/health
```

## Security Considerations

### Firebase Security Rules

The `firestore.rules` file includes:
- User data protection (users can only access their own data)
- Blood request visibility (active requests visible to all authenticated users)
- Notification privacy (users can only see their own notifications)
- Admin-only analytics access

### Appwrite Permissions

Collections are configured with:
- **Users**: Read/Write access only to document owner
- **Blood Requests**: Read access to any authenticated user, Write access to owner
- **Notifications**: Read/Write access only to the target user
- **Analytics**: Admin team access only

## Monitoring and Maintenance

### Firebase

1. Monitor usage in Firebase Console
2. Set up billing alerts
3. Review security rules regularly
4. Monitor Firestore usage and costs

### Appwrite

1. Monitor resource usage in Appwrite Console
2. Review database performance
3. Check messaging delivery rates
4. Monitor API usage and rate limits

## Troubleshooting

### Common Firebase Issues

1. **Authentication fails**: Check service account key and permissions
2. **Firestore rules deny access**: Verify security rules and user authentication
3. **Cloud Messaging not working**: Check FCM server key and client tokens

### Common Appwrite Issues

1. **API key invalid**: Verify API key scopes and project ID
2. **Collection not found**: Ensure collections are created with correct IDs
3. **Permission denied**: Check collection permissions and user authentication
4. **Messaging fails**: Verify push notification provider configuration

## Migration Between Backends

The application supports both Firebase and Appwrite. To switch between them:

1. Update environment variables
2. Use appropriate service (`firebaseService` or `appwriteService`)
3. Update authentication middleware
4. Ensure data models are compatible

Both services implement the same interface, making migration straightforward.