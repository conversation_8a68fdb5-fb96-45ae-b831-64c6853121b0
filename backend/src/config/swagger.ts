import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'UBLOOD Clone API',
      version: '1.0.0',
      description: 'A comprehensive blood donation platform API supporting both Firebase and Appwrite backends',
      contact: {
        name: 'UBLOOD Clone API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      },
      {
        url: 'https://api.ublood-clone.com',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Firebase ID Token or Appwrite JWT Token'
        },
        AppwriteSession: {
          type: 'apiKey',
          in: 'header',
          name: 'x-appwrite-session',
          description: 'Appwrite session token'
        },
        AppwriteUserId: {
          type: 'apiKey',
          in: 'header',
          name: 'x-appwrite-user-id',
          description: 'Appwrite user ID'
        }
      },
      schemas: {
        BloodRequest: {
          type: 'object',
          required: ['requesterId', 'bloodType', 'quantity', 'urgency', 'location', 'contactInfo'],
          properties: {
            id: {
              type: 'string',
              description: 'Unique identifier for the blood request'
            },
            requesterId: {
              type: 'string',
              description: 'ID of the user making the request'
            },
            bloodType: {
              type: 'string',
              enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
              description: 'Required blood type'
            },
            quantity: {
              type: 'string',
              description: 'Amount of blood needed (e.g., "2 units", "500ml")'
            },
            urgency: {
              type: 'string',
              enum: ['critical', 'urgent', 'normal'],
              description: 'Urgency level of the request'
            },
            location: {
              $ref: '#/components/schemas/Location'
            },
            contactInfo: {
              $ref: '#/components/schemas/ContactInfo'
            },
            description: {
              type: 'string',
              maxLength: 500,
              description: 'Additional details about the request'
            },
            status: {
              type: 'string',
              enum: ['active', 'fulfilled', 'cancelled', 'expired'],
              description: 'Current status of the request'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the request was created'
            },
            expiresAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the request expires'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the request was last updated'
            },
            responses: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/DonorResponse'
              },
              description: 'Donor responses to this request'
            },
            notifiedDonors: {
              type: 'array',
              items: {
                type: 'string'
              },
              description: 'List of donor IDs who were notified'
            },
            fulfillmentDetails: {
              $ref: '#/components/schemas/FulfillmentDetails'
            }
          }
        },
        Location: {
          type: 'object',
          required: ['latitude', 'longitude', 'address'],
          properties: {
            latitude: {
              type: 'number',
              minimum: -90,
              maximum: 90,
              description: 'Latitude coordinate'
            },
            longitude: {
              type: 'number',
              minimum: -180,
              maximum: 180,
              description: 'Longitude coordinate'
            },
            address: {
              type: 'string',
              description: 'Human-readable address'
            },
            city: {
              type: 'string',
              description: 'City name'
            },
            state: {
              type: 'string',
              description: 'State or province'
            },
            country: {
              type: 'string',
              description: 'Country name'
            },
            postalCode: {
              type: 'string',
              description: 'Postal or ZIP code'
            }
          }
        },
        ContactInfo: {
          type: 'object',
          required: ['name', 'phone', 'preferredContact'],
          properties: {
            name: {
              type: 'string',
              minLength: 2,
              description: 'Contact person name'
            },
            phone: {
              type: 'string',
              pattern: '^\\+[1-9]\\d{1,14}$',
              description: 'Phone number in international format'
            },
            preferredContact: {
              type: 'string',
              enum: ['call', 'text'],
              description: 'Preferred contact method'
            }
          }
        },
        DonorResponse: {
          type: 'object',
          required: ['donorId', 'response', 'respondedAt'],
          properties: {
            donorId: {
              type: 'string',
              description: 'ID of the responding donor'
            },
            response: {
              type: 'string',
              enum: ['yes', 'no'],
              description: 'Donor response to the request'
            },
            respondedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the donor responded'
            },
            donorContact: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                  description: 'Donor name'
                },
                phone: {
                  type: 'string',
                  description: 'Donor phone number'
                }
              },
              description: 'Contact information (only for positive responses)'
            }
          }
        },
        FulfillmentDetails: {
          type: 'object',
          required: ['fulfilledAt', 'fulfilledBy'],
          properties: {
            fulfilledAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the request was fulfilled'
            },
            fulfilledBy: {
              type: 'string',
              description: 'ID of the donor who fulfilled the request'
            },
            recipientFeedback: {
              type: 'object',
              properties: {
                rating: {
                  type: 'number',
                  minimum: 1,
                  maximum: 5,
                  description: 'Rating from 1 to 5'
                },
                comment: {
                  type: 'string',
                  description: 'Feedback comment'
                }
              },
              description: 'Optional feedback from recipient'
            }
          }
        },
        CreateBloodRequestData: {
          type: 'object',
          required: ['bloodType', 'quantity', 'urgency', 'location', 'contactInfo'],
          properties: {
            bloodType: {
              type: 'string',
              enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
            },
            quantity: {
              type: 'string'
            },
            urgency: {
              type: 'string',
              enum: ['critical', 'urgent', 'normal']
            },
            location: {
              $ref: '#/components/schemas/Location'
            },
            contactInfo: {
              $ref: '#/components/schemas/ContactInfo'
            },
            description: {
              type: 'string',
              maxLength: 500
            }
          }
        },
        UpdateBloodRequestData: {
          type: 'object',
          properties: {
            quantity: {
              type: 'string'
            },
            urgency: {
              type: 'string',
              enum: ['critical', 'urgent', 'normal']
            },
            location: {
              $ref: '#/components/schemas/Location'
            },
            contactInfo: {
              $ref: '#/components/schemas/ContactInfo'
            },
            description: {
              type: 'string',
              maxLength: 500
            },
            status: {
              type: 'string',
              enum: ['active', 'fulfilled', 'cancelled', 'expired']
            }
          }
        },
        DonorResponseRequest: {
          type: 'object',
          required: ['response'],
          properties: {
            response: {
              type: 'string',
              enum: ['yes', 'no'],
              description: 'Donor response to the blood request'
            }
          }
        },
        FulfillRequest: {
          type: 'object',
          properties: {
            fulfilledBy: {
              type: 'string',
              description: 'ID of the donor who fulfilled the request'
            },
            recipientFeedback: {
              type: 'object',
              properties: {
                rating: {
                  type: 'number',
                  minimum: 1,
                  maximum: 5
                },
                comment: {
                  type: 'string'
                }
              }
            }
          }
        },
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean'
            },
            data: {
              type: 'object'
            },
            message: {
              type: 'string'
            },
            error: {
              type: 'object',
              properties: {
                code: {
                  type: 'string'
                },
                message: {
                  type: 'string'
                },
                details: {
                  type: 'array',
                  items: {
                    type: 'string'
                  }
                }
              }
            }
          }
        },
        PaginationResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean'
            },
            data: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/BloodRequest'
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: {
                  type: 'number'
                },
                limit: {
                  type: 'number'
                },
                offset: {
                  type: 'number'
                },
                hasMore: {
                  type: 'boolean'
                }
              }
            }
          }
        },
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Unique user identifier'
            },
            phoneNumber: {
              type: 'string',
              pattern: '^\\+[1-9]\\d{1,14}$',
              description: 'Phone number in international format'
            },
            profile: {
              $ref: '#/components/schemas/UserProfile'
            },
            donorStats: {
              $ref: '#/components/schemas/DonorStats'
            },
            notificationSettings: {
              $ref: '#/components/schemas/NotificationSettings'
            },
            backend: {
              type: 'string',
              enum: ['firebase', 'appwrite'],
              description: 'Backend service used'
            }
          }
        },
        UserProfile: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: 'User display name'
            },
            bloodType: {
              type: 'string',
              enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
              description: 'User blood type'
            },
            userType: {
              type: 'string',
              enum: ['donor', 'recipient', 'both'],
              description: 'User role in the platform'
            },
            location: {
              $ref: '#/components/schemas/UserLocation'
            },
            isAvailable: {
              type: 'boolean',
              description: 'Whether donor is currently available'
            },
            joinedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When user joined the platform'
            },
            lastActive: {
              type: 'string',
              format: 'date-time',
              description: 'Last activity timestamp'
            }
          }
        },
        UserLocation: {
          type: 'object',
          properties: {
            coordinates: {
              type: 'object',
              properties: {
                latitude: {
                  type: 'number',
                  minimum: -90,
                  maximum: 90
                },
                longitude: {
                  type: 'number',
                  minimum: -180,
                  maximum: 180
                }
              }
            },
            address: {
              type: 'string',
              description: 'Human-readable address'
            },
            lastUpdated: {
              type: 'string',
              format: 'date-time',
              description: 'When location was last updated'
            }
          }
        },
        DonorStats: {
          type: 'object',
          properties: {
            totalDonations: {
              type: 'number',
              minimum: 0,
              description: 'Total number of donations made'
            },
            lastDonationDate: {
              type: 'string',
              format: 'date-time',
              nullable: true,
              description: 'Date of last donation'
            },
            rating: {
              type: 'number',
              minimum: 0,
              maximum: 5,
              description: 'Average donor rating'
            },
            reviewCount: {
              type: 'number',
              minimum: 0,
              description: 'Number of reviews received'
            },
            badges: {
              type: 'array',
              items: {
                type: 'string'
              },
              description: 'Achievement badges earned'
            }
          }
        },
        NotificationSettings: {
          type: 'object',
          properties: {
            fcmToken: {
              type: 'string',
              description: 'Firebase Cloud Messaging token'
            },
            pushEnabled: {
              type: 'boolean',
              description: 'Whether push notifications are enabled'
            },
            maxDistance: {
              type: 'number',
              minimum: 1,
              maximum: 100,
              description: 'Maximum distance for notifications (km)'
            },
            urgencyLevels: {
              type: 'array',
              items: {
                type: 'string',
                enum: ['critical', 'urgent', 'normal']
              },
              description: 'Urgency levels to receive notifications for'
            }
          }
        },
        RegisterRequest: {
          type: 'object',
          required: ['phoneNumber'],
          properties: {
            phoneNumber: {
              type: 'string',
              pattern: '^\\+[1-9]\\d{1,14}$',
              description: 'Phone number in international format'
            },
            displayName: {
              type: 'string',
              description: 'User display name'
            },
            password: {
              type: 'string',
              minLength: 8,
              description: 'Password (required for Appwrite)'
            }
          }
        },
        LoginRequest: {
          type: 'object',
          required: ['phoneNumber'],
          properties: {
            phoneNumber: {
              type: 'string',
              pattern: '^\\+[1-9]\\d{1,14}$',
              description: 'Phone number in international format'
            },
            password: {
              type: 'string',
              description: 'Password (required for Appwrite)'
            },
            idToken: {
              type: 'string',
              description: 'Firebase ID token (required for Firebase)'
            }
          }
        },
        UpdateProfileRequest: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: 'User display name'
            },
            bloodType: {
              type: 'string',
              enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
              description: 'User blood type'
            },
            userType: {
              type: 'string',
              enum: ['donor', 'recipient', 'both'],
              description: 'User role in the platform'
            },
            location: {
              type: 'object',
              properties: {
                coordinates: {
                  type: 'object',
                  properties: {
                    latitude: {
                      type: 'number',
                      minimum: -90,
                      maximum: 90
                    },
                    longitude: {
                      type: 'number',
                      minimum: -180,
                      maximum: 180
                    }
                  }
                },
                address: {
                  type: 'string'
                }
              }
            }
          }
        },
        UpdateLocationRequest: {
          type: 'object',
          properties: {
            coordinates: {
              type: 'object',
              required: ['latitude', 'longitude'],
              properties: {
                latitude: {
                  type: 'number',
                  minimum: -90,
                  maximum: 90
                },
                longitude: {
                  type: 'number',
                  minimum: -180,
                  maximum: 180
                }
              }
            },
            address: {
              type: 'string',
              description: 'Human-readable address'
            }
          }
        },
        UpdateAvailabilityRequest: {
          type: 'object',
          required: ['isAvailable'],
          properties: {
            isAvailable: {
              type: 'boolean',
              description: 'Whether donor is currently available'
            }
          }
        },
        UpdateNotificationSettingsRequest: {
          type: 'object',
          properties: {
            fcmToken: {
              type: 'string',
              description: 'Firebase Cloud Messaging token'
            },
            pushEnabled: {
              type: 'boolean',
              description: 'Whether push notifications are enabled'
            },
            maxDistance: {
              type: 'number',
              minimum: 1,
              maximum: 100,
              description: 'Maximum distance for notifications (km)'
            },
            urgencyLevels: {
              type: 'array',
              items: {
                type: 'string',
                enum: ['critical', 'urgent', 'normal']
              },
              description: 'Urgency levels to receive notifications for'
            }
          }
        }
      }
    }
  },
  apis: [
    './src/routes/**/*.ts',
    './src/server.ts'
  ]
};

const specs = swaggerJsdoc(options);

export const setupSwagger = (app: Express): void => {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'UBLOOD Clone API Documentation',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      showExtensions: true,
      showCommonExtensions: true
    }
  }));

  // Serve raw OpenAPI spec
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });
};

export default specs;