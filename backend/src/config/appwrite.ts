import { Client, Databases, Account, Users, Messaging } from 'node-appwrite';

let appwriteClient: Client;
let databases: Databases;
let account: Account;
let users: Users;
let messaging: Messaging;

export const initializeAppwrite = (): Client => {
  if (appwriteClient) {
    return appwriteClient;
  }

  try {
    // Initialize Appwrite Client
    appwriteClient = new Client();

    const endpoint = process.env.APPWRITE_ENDPOINT;
    const projectId = process.env.APPWRITE_PROJECT_ID;
    const apiKey = process.env.APPWRITE_API_KEY;

    if (!endpoint || !projectId || !apiKey) {
      console.warn('⚠️  Appwrite credentials not found. Some features may not work.');
      console.warn('   Set APPWRITE_ENDPOINT, APPWRITE_PROJECT_ID, and APPWRITE_API_KEY');
      
      // Return a dummy client for basic setup
      return appwriteClient;
    }

    appwriteClient
      .setEndpoint(endpoint)
      .setProject(projectId)
      .setKey(apiKey);

    // Initialize services
    databases = new Databases(appwriteClient);
    account = new Account(appwriteClient);
    users = new Users(appwriteClient);
    messaging = new Messaging(appwriteClient);

    console.log('📱 Appwrite SDK initialized successfully');
    return appwriteClient;
  } catch (error) {
    console.error('❌ Failed to initialize Appwrite SDK:', error);
    throw error;
  }
};

export const getDatabases = (): Databases => {
  if (!databases) {
    throw new Error('Appwrite not initialized. Call initializeAppwrite() first.');
  }
  return databases;
};

export const getAccount = (): Account => {
  if (!account) {
    throw new Error('Appwrite not initialized. Call initializeAppwrite() first.');
  }
  return account;
};

export const getUsers = (): Users => {
  if (!users) {
    throw new Error('Appwrite not initialized. Call initializeAppwrite() first.');
  }
  return users;
};

export const getMessaging = (): Messaging => {
  if (!messaging) {
    throw new Error('Appwrite not initialized. Call initializeAppwrite() first.');
  }
  return messaging;
};

// Database and Collection IDs (to be set in environment variables)
export const APPWRITE_DATABASE_ID = process.env.APPWRITE_DATABASE_ID || 'ublood_db';
export const COLLECTIONS = {
  USERS: process.env.APPWRITE_USERS_COLLECTION_ID || 'users',
  BLOOD_REQUESTS: process.env.APPWRITE_BLOOD_REQUESTS_COLLECTION_ID || 'blood_requests',
  NOTIFICATIONS: process.env.APPWRITE_NOTIFICATIONS_COLLECTION_ID || 'notifications',
  USER_RESPONSES: process.env.APPWRITE_USER_RESPONSES_COLLECTION_ID || 'user_responses',
  ANALYTICS: process.env.APPWRITE_ANALYTICS_COLLECTION_ID || 'analytics'
};

export { appwriteClient };