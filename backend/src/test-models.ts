/**
 * Simple test script to verify the models work correctly
 */

import {
  BloodType,
  UrgencyLevel,
  UserType,
  ResponseType,
  ContactPreference,
  isBloodTypeCompatible,
  getCompatibleDonorTypes,
  validateBloodType,
  calculateDistance,
  validateCoordinates,
  createUser,
  createBloodRequest,
  createResponse,
  validateCreateUserData,
  validateCreateBloodRequestData,
  validateCreateResponseData
} from './models';

// Test blood type compatibility
console.log('=== Blood Type Compatibility Tests ===');
console.log('O- can donate to A+:', isBloodTypeCompatible(BloodType.O_NEGATIVE, BloodType.A_POSITIVE)); // true
console.log('A+ can donate to O+:', isBloodTypeCompatible(BloodType.A_POSITIVE, BloodType.O_POSITIVE)); // false
console.log('Compatible donors for AB+:', getCompatibleDonorTypes(BloodType.AB_POSITIVE));

// Test blood type validation
console.log('\n=== Blood Type Validation Tests ===');
console.log('O+ is valid:', validateBloodType('O+')); // true
console.log('X+ is valid:', validateBloodType('X+')); // false

// Test location functions
console.log('\n=== Location Tests ===');
const coord1 = { latitude: 40.7128, longitude: -74.0060 }; // NYC
const coord2 = { latitude: 34.0522, longitude: -118.2437 }; // LA
console.log('NYC coordinates valid:', validateCoordinates(coord1)); // true
console.log('Distance NYC to LA:', calculateDistance(coord1, coord2), 'km');

// Test user creation
console.log('\n=== User Creation Tests ===');
const userData = {
  phoneNumber: '+1234567890',
  name: 'John Doe',
  bloodType: 'O+',
  location: {
    coordinates: coord1,
    address: 'New York, NY'
  },
  userType: 'donor'
};

const userValidation = validateCreateUserData(userData);
console.log('User data valid:', userValidation.isValid);
if (!userValidation.isValid) {
  console.log('Errors:', userValidation.errors);
}

const user = createUser(userData);
console.log('Created user:', {
  phoneNumber: user.phoneNumber,
  name: user.profile.name,
  bloodType: user.profile.bloodType,
  userType: user.profile.userType
});

// Test blood request creation
console.log('\n=== Blood Request Creation Tests ===');
const requestData = {
  requesterId: 'user123',
  bloodType: 'A+',
  quantity: '2 units',
  urgency: 'critical',
  location: {
    coordinates: coord2,
    address: 'Los Angeles, CA'
  },
  contactInfo: {
    name: 'Jane Smith',
    phone: '+1987654321',
    preferredContact: ContactPreference.CALL
  },
  description: 'Needed for emergency surgery'
};

const requestValidation = validateCreateBloodRequestData(requestData);
console.log('Request data valid:', requestValidation.isValid);
if (!requestValidation.isValid) {
  console.log('Errors:', requestValidation.errors);
}

const bloodRequest = createBloodRequest(requestData);
console.log('Created request:', {
  bloodType: bloodRequest.bloodType,
  urgency: bloodRequest.urgency,
  status: bloodRequest.status,
  expiresAt: bloodRequest.expiresAt
});

// Test response creation
console.log('\n=== Response Creation Tests ===');
const responseData = {
  requestId: 'req123',
  donorId: 'donor456',
  response: 'yes',
  donorContact: {
    name: 'Mike Johnson',
    phone: '+1555666777'
  },
  message: 'I can help immediately'
};

const responseValidation = validateCreateResponseData(responseData);
console.log('Response data valid:', responseValidation.isValid);
if (!responseValidation.isValid) {
  console.log('Errors:', responseValidation.errors);
}

const response = createResponse(responseData);
console.log('Created response:', {
  response: response.response,
  donorContact: response.donorContact,
  message: response.message
});

console.log('\n=== All Tests Completed ===');