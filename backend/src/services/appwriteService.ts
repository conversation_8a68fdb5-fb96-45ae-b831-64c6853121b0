import { getDatabases, getUsers, getMessaging, APPWRITE_DATABASE_ID, COLLECTIONS } from '../config/appwrite';
import { ID, Query, Permission, Role } from 'node-appwrite';

export class AppwriteService {
  private databases: any;
  private users: any;
  private messaging: any;

  constructor() {
    // Don't initialize in constructor to avoid initialization order issues
    // Services will be initialized when first used
  }

  private initializeServices() {
    try {
      this.databases = getDatabases();
      this.users = getUsers();
      this.messaging = getMessaging();
    } catch (error) {
      // Services will be initialized when first used
      console.warn('Appwrite services not initialized yet');
      throw error; // Re-throw to handle properly
    }
  }

  private ensureInitialized() {
    if (!this.databases) {
      this.initializeServices();
    }
  }

  // User Management
  async createUser(userData: any): Promise<string> {
    this.ensureInitialized();
    try {
      // Create user document in database
      const document = await this.databases.createDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        ID.unique(),
        {
          ...userData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        [
          Permission.read(Role.user(userData.userId)),
          Permission.update(Role.user(userData.userId)),
          Permission.delete(Role.user(userData.userId))
        ]
      );
      return document.$id;
    } catch (error) {
      console.error('Error creating user in Appwrite:', error);
      throw error;
    }
  }

  async getUserById(userId: string): Promise<any | null> {
    this.ensureInitialized();
    try {
      const document = await this.databases.getDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userId
      );
      return document;
    } catch (error) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 404) {
        return null;
      }
      console.error('Error getting user from Appwrite:', error);
      throw error;
    }
  }

  async updateUser(userId: string, updateData: any): Promise<void> {
    this.ensureInitialized();
    try {
      await this.databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userId,
        {
          ...updateData,
          updatedAt: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Error updating user in Appwrite:', error);
      throw error;
    }
  }

  async deleteUser(userId: string): Promise<void> {
    this.ensureInitialized();
    try {
      await this.databases.deleteDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userId
      );
    } catch (error) {
      console.error('Error deleting user from Appwrite:', error);
      throw error;
    }
  }

  async getUserByPhoneNumber(phoneNumber: string): Promise<any | null> {
    this.ensureInitialized();
    try {
      const documents = await this.databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        [
          Query.equal('phoneNumber', phoneNumber),
          Query.limit(1)
        ]
      );
      
      return documents.documents.length > 0 ? documents.documents[0] : null;
    } catch (error) {
      console.error('Error getting user by phone from Appwrite:', error);
      throw error;
    }
  }

  // Blood Request Management
  async createBloodRequest(requestData: any): Promise<string> {
    this.ensureInitialized();
    try {
      const document = await this.databases.createDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.BLOOD_REQUESTS,
        ID.unique(),
        {
          ...requestData,
          status: 'active',
          responses: [],
          notifiedDonors: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        [
          Permission.read(Role.any()),
          Permission.update(Role.user(requestData.requesterId)),
          Permission.delete(Role.user(requestData.requesterId))
        ]
      );
      return document.$id;
    } catch (error) {
      console.error('Error creating blood request in Appwrite:', error);
      throw error;
    }
  }

  async getBloodRequestById(requestId: string): Promise<any | null> {
    this.ensureInitialized();
    try {
      const document = await this.databases.getDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.BLOOD_REQUESTS,
        requestId
      );
      return document;
    } catch (error) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 404) {
        return null;
      }
      console.error('Error getting blood request from Appwrite:', error);
      throw error;
    }
  }

  // Alias for getBloodRequestById to match the interface expected by matching routes
  async getBloodRequest(requestId: string): Promise<any | null> {
    return this.getBloodRequestById(requestId);
  }

  async getAllDonors(): Promise<any[]> {
    this.ensureInitialized();
    try {
      const documents = await this.databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        [
          Query.equal('userType', ['donor', 'both'])
        ]
      );
      
      return documents.documents;
    } catch (error) {
      console.error('Error getting all donors from Appwrite:', error);
      throw error;
    }
  }

  async updateBloodRequest(requestId: string, updateData: any): Promise<void> {
    this.ensureInitialized();
    try {
      await this.databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.BLOOD_REQUESTS,
        requestId,
        {
          ...updateData,
          updatedAt: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Error updating blood request in Appwrite:', error);
      throw error;
    }
  }

  async getActiveBloodRequests(filters?: {
    bloodType?: string;
    urgency?: string;
    limit?: number;
  }): Promise<any[]> {
    this.ensureInitialized();
    try {
      const queries = [
        Query.equal('status', 'active'),
        Query.orderDesc('createdAt')
      ];

      if (filters?.bloodType) {
        queries.push(Query.equal('bloodType', filters.bloodType));
      }

      if (filters?.urgency) {
        queries.push(Query.equal('urgency', filters.urgency));
      }

      if (filters?.limit) {
        queries.push(Query.limit(filters.limit));
      }

      const documents = await this.databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.BLOOD_REQUESTS,
        queries
      );

      return documents.documents;
    } catch (error) {
      console.error('Error getting active blood requests from Appwrite:', error);
      throw error;
    }
  }

  // Donor Matching
  async findNearbyDonors(params: {
    bloodType: string;
    location: { latitude: number; longitude: number };
    radius: number;
    isAvailable?: boolean;
  }): Promise<any[]> {
    this.ensureInitialized();
    try {
      const queries = [
        Query.equal('userType', ['donor', 'both'])
      ];

      if (params.isAvailable !== undefined) {
        queries.push(Query.equal('isAvailable', params.isAvailable));
      }

      const documents = await this.databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        queries
      );

      // Filter by distance (simplified - in production use proper geo-indexing)
      return documents.documents.filter((donor: any) => {
        if (!donor.latitude || !donor.longitude) return false;
        
        const distance = this.calculateDistance(
          params.location,
          { latitude: donor.latitude, longitude: donor.longitude }
        );
        
        return distance <= params.radius;
      });
    } catch (error) {
      console.error('Error finding nearby donors in Appwrite:', error);
      throw error;
    }
  }

  // Authentication
  async createUserAccount(email: string, password: string, name: string): Promise<any> {
    this.ensureInitialized();
    try {
      const user = await this.users.create(
        ID.unique(),
        email,
        undefined, // phone (optional)
        password,
        name
      );
      return user;
    } catch (error) {
      console.error('Error creating user account in Appwrite:', error);
      throw error;
    }
  }

  async getUserAccount(userId: string): Promise<any> {
    this.ensureInitialized();
    try {
      const user = await this.users.get(userId);
      return user;
    } catch (error) {
      console.error('Error getting user account from Appwrite:', error);
      throw error;
    }
  }

  async updateUserAccount(userId: string, updateData: any): Promise<any> {
    this.ensureInitialized();
    try {
      const user = await this.users.updateName(userId, updateData.name);
      return user;
    } catch (error) {
      console.error('Error updating user account in Appwrite:', error);
      throw error;
    }
  }

  // Notifications
  async sendNotification(params: {
    userId: string;
    title: string;
    body: string;
    data?: { [key: string]: string };
  }): Promise<any> {
    this.ensureInitialized();
    try {
      // Create notification document
      const notification = await this.databases.createDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        ID.unique(),
        {
          userId: params.userId,
          type: 'blood_request', // Default notification type
          title: params.title,
          message: params.body, // Use 'message' instead of 'body' to match schema
          requestId: params.data?.requestId || null,
          bloodType: params.data?.bloodType || null,
          distance: params.data?.distance || null,
          urgency: params.data?.urgency || null,
          status: 'sent',
          createdAt: new Date().toISOString()
        },
        [
          Permission.read(Role.user(params.userId)),
          Permission.update(Role.user(params.userId))
        ]
      );

      // Send push notification using Appwrite Messaging
      // Note: This requires proper setup of push notification providers in Appwrite console
      try {
        const message = await this.messaging.createPush(
          ID.unique(),
          params.title,
          params.body,
          undefined, // topics
          [params.userId], // users
          undefined, // targets
          params.data,
          undefined, // action
          undefined, // image
          undefined, // icon
          undefined, // sound
          undefined, // color
          undefined, // tag
          undefined, // badge
          undefined // scheduledAt - send immediately
        );
        
        return { notification, message };
      } catch (pushError) {
        console.warn('Push notification failed, but notification saved:', pushError);
        return { notification };
      }
    } catch (error) {
      console.error('Error sending notification via Appwrite:', error);
      throw error;
    }
  }

  async sendBatchNotifications(notifications: Array<{
    userId: string;
    title: string;
    body: string;
    data?: { [key: string]: string };
  }>): Promise<any[]> {
    try {
      const results = await Promise.allSettled(
        notifications.map(notification => this.sendNotification(notification))
      );
      
      return results.map((result, index) => ({
        index,
        status: result.status,
        value: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason : null
      }));
    } catch (error) {
      console.error('Error sending batch notifications via Appwrite:', error);
      throw error;
    }
  }

  async deleteNotification(notificationId: string): Promise<void> {
    this.ensureInitialized();
    try {
      await this.databases.deleteDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        notificationId
      );
    } catch (error) {
      console.error('Error deleting notification from Appwrite:', error);
      throw error;
    }
  }

  // Utility Methods
  private calculateDistance(
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
  ): number {
    // Haversine formula
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(point1.latitude)) * 
      Math.cos(this.toRadians(point2.latitude)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  // Collection Management (for setup)
  async createCollections(): Promise<void> {
    try {
      // This method would be used to programmatically create collections
      // In practice, collections are usually created via Appwrite Console
      console.log('Collections should be created via Appwrite Console');
      console.log('Required collections:', Object.values(COLLECTIONS));
    } catch (error) {
      console.error('Error creating collections:', error);
      throw error;
    }
  }
}

export const appwriteService = new AppwriteService();