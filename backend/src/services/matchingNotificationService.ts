/**
 * Service that integrates geo-matching with notifications
 * Handles automatic donor matching and notification sending for blood requests
 */

import { BloodRequest } from '../models/BloodRequest';
import { User } from '../models/User';
import { geoMatchingService, DonorMatch } from './geoMatchingService';
import { firebaseNotificationService } from './notificationService';
import { appwriteNotificationService } from './appwriteNotificationService';
import { RequestStatus } from '../types/enums';

export interface MatchingNotificationResult {
  matchedDonors: number;
  notificationsSent: number;
  notificationsFailed: number;
  matchingStats: {
    totalMatches: number;
    exactMatches: number;
    compatibleMatches: number;
    averageDistance: number;
  };
}

export interface NotificationIntegrationOptions {
  backend: 'firebase' | 'appwrite';
  expandRadius?: boolean;
  maxRetries?: number;
}

export class MatchingNotificationService {
  /**
   * Process a new blood request: find donors and send notifications
   * @param request - The blood request to process
   * @param availableDonors - Array of available donors to search through
   * @param options - Integration options
   * @returns Result of matching and notification process
   */
  async processBloodRequest(
    request: BloodRequest,
    availableDonors: User[],
    options: NotificationIntegrationOptions
  ): Promise<MatchingNotificationResult> {
    try {
      console.log(`Processing blood request ${request.id} for ${request.bloodType} blood`);

      // Step 1: Find compatible donors using geo-matching
      let donorMatches = await geoMatchingService.findDonorsForRequest(request, availableDonors);

      // Step 2: If no donors found and expand radius is enabled, try expanded search
      if (donorMatches.length === 0 && options.expandRadius) {
        console.log(`No donors found in initial radius, expanding search for request ${request.id}`);
        donorMatches = await geoMatchingService.expandSearchRadius(
          {
            bloodType: request.bloodType,
            location: request.location.coordinates,
            urgency: request.urgency
          },
          availableDonors
        );
      }

      if (donorMatches.length === 0) {
        console.log(`No compatible donors found for request ${request.id}`);
        return {
          matchedDonors: 0,
          notificationsSent: 0,
          notificationsFailed: 0,
          matchingStats: {
            totalMatches: 0,
            exactMatches: 0,
            compatibleMatches: 0,
            averageDistance: 0
          }
        };
      }

      // Step 3: Send notifications to matched donors
      const notificationResult = await this.sendBloodRequestNotifications(
        request,
        donorMatches,
        options
      );

      // Step 4: Get matching statistics
      const matchingStats = geoMatchingService.getMatchingStats(donorMatches);

      console.log(`Processed request ${request.id}: ${donorMatches.length} donors matched, ${notificationResult.successCount} notifications sent`);

      return {
        matchedDonors: donorMatches.length,
        notificationsSent: notificationResult.successCount,
        notificationsFailed: notificationResult.failureCount,
        matchingStats
      };

    } catch (error) {
      console.error(`Error processing blood request ${request.id}:`, error);
      throw error;
    }
  }

  /**
   * Handle donor response to blood request
   * @param requestId - ID of the blood request
   * @param donor - The donor who responded
   * @param response - The donor's response ('yes' or 'no')
   * @param recipientFcmToken - FCM token of the recipient
   * @param options - Integration options
   */
  async processDonorResponse(
    requestId: string,
    donor: User,
    response: 'yes' | 'no',
    recipientFcmToken: string,
    options: NotificationIntegrationOptions
  ): Promise<void> {
    try {
      console.log(`Processing donor response: ${donor.id} responded "${response}" to request ${requestId}`);

      // Send notification to recipient about donor response
      const notificationService = this.getNotificationService(options.backend);
      
      const result = await notificationService.notifyRecipientOfDonorResponse(
        recipientFcmToken,
        donor,
        response,
        requestId
      );

      if (result.success) {
        console.log(`Recipient notified of donor response for request ${requestId}`);
      } else {
        console.error(`Failed to notify recipient of donor response: ${result.error}`);
        
        // Retry if configured
        if (options.maxRetries && options.maxRetries > 0) {
          await this.retryNotification(
            () => notificationService.notifyRecipientOfDonorResponse(recipientFcmToken, donor, response, requestId),
            options.maxRetries
          );
        }
      }

    } catch (error) {
      console.error(`Error processing donor response for request ${requestId}:`, error);
      throw error;
    }
  }

  /**
   * Handle blood request cancellation
   * @param request - The cancelled blood request
   * @param notifiedDonorTokens - FCM tokens of donors who were notified
   * @param options - Integration options
   */
  async processRequestCancellation(
    request: BloodRequest,
    notifiedDonorTokens: string[],
    options: NotificationIntegrationOptions
  ): Promise<void> {
    try {
      console.log(`Processing cancellation of request ${request.id}`);

      if (notifiedDonorTokens.length === 0) {
        console.log(`No donors to notify about cancellation of request ${request.id}`);
        return;
      }

      // Create cancellation notification
      const cancellationPayload = {
        title: '❌ Blood Request Cancelled',
        body: `The ${request.bloodType} blood request you received has been cancelled.`,
        data: {
          type: 'request_cancelled',
          requestId: request.id,
          bloodType: request.bloodType,
          urgency: request.urgency
        }
      };

      // Send cancellation notifications to all notified donors
      const notificationService = this.getNotificationService(options.backend);
      const result = await notificationService.sendBatchNotifications(
        notifiedDonorTokens,
        cancellationPayload
      );

      console.log(`Cancellation notifications sent: ${result.successCount} successful, ${result.failureCount} failed`);

    } catch (error) {
      console.error(`Error processing request cancellation for ${request.id}:`, error);
      throw error;
    }
  }

  /**
   * Handle blood request fulfillment
   * @param request - The fulfilled blood request
   * @param fulfilledBy - ID of the donor who fulfilled the request
   * @param notifiedDonorTokens - FCM tokens of donors who were notified
   * @param options - Integration options
   */
  async processRequestFulfillment(
    request: BloodRequest,
    fulfilledBy: string,
    notifiedDonorTokens: string[],
    options: NotificationIntegrationOptions
  ): Promise<void> {
    try {
      console.log(`Processing fulfillment of request ${request.id} by donor ${fulfilledBy}`);

      // Filter out the fulfilling donor from notification list
      const otherDonorTokens = notifiedDonorTokens.filter(token => {
        // This would need to be enhanced to properly filter by donor ID
        // For now, we'll send to all notified donors
        return true;
      });

      if (otherDonorTokens.length === 0) {
        console.log(`No other donors to notify about fulfillment of request ${request.id}`);
        return;
      }

      // Create fulfillment notification
      const fulfillmentPayload = {
        title: '✅ Blood Request Fulfilled',
        body: `The ${request.bloodType} blood request has been fulfilled. Thank you for your willingness to help!`,
        data: {
          type: 'request_fulfilled',
          requestId: request.id,
          bloodType: request.bloodType,
          fulfilledBy: fulfilledBy
        }
      };

      // Send fulfillment notifications to other donors
      const notificationService = this.getNotificationService(options.backend);
      const result = await notificationService.sendBatchNotifications(
        otherDonorTokens,
        fulfillmentPayload
      );

      console.log(`Fulfillment notifications sent: ${result.successCount} successful, ${result.failureCount} failed`);

    } catch (error) {
      console.error(`Error processing request fulfillment for ${request.id}:`, error);
      throw error;
    }
  }

  /**
   * Send blood request notifications to matched donors
   * @private
   */
  private async sendBloodRequestNotifications(
    request: BloodRequest,
    donorMatches: DonorMatch[],
    options: NotificationIntegrationOptions
  ) {
    const notificationService = this.getNotificationService(options.backend);

    // Convert donor matches to the format expected by notification service
    const donorsWithDistance = donorMatches.map(match => ({
      user: match.donor,
      distance: match.distance
    }));

    return await notificationService.notifyDonorsOfBloodRequest(request, donorsWithDistance);
  }

  /**
   * Get the appropriate notification service based on backend
   * @private
   */
  private getNotificationService(backend: 'firebase' | 'appwrite') {
    switch (backend) {
      case 'firebase':
        return firebaseNotificationService;
      case 'appwrite':
        return appwriteNotificationService;
      default:
        throw new Error(`Unsupported notification backend: ${backend}`);
    }
  }

  /**
   * Retry notification with exponential backoff
   * @private
   */
  private async retryNotification(
    notificationFn: () => Promise<any>,
    maxRetries: number,
    currentRetry: number = 0
  ): Promise<void> {
    if (currentRetry >= maxRetries) {
      throw new Error(`Max retries (${maxRetries}) exceeded for notification`);
    }

    try {
      await notificationFn();
    } catch (error) {
      const delay = Math.pow(2, currentRetry) * 1000; // Exponential backoff
      console.log(`Notification retry ${currentRetry + 1}/${maxRetries} in ${delay}ms`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      return this.retryNotification(notificationFn, maxRetries, currentRetry + 1);
    }
  }

  /**
   * Get comprehensive statistics about matching and notifications
   */
  async getIntegrationStats(
    startDate: Date,
    endDate: Date,
    backend: 'firebase' | 'appwrite'
  ): Promise<{
    totalRequestsProcessed: number;
    totalDonorsMatched: number;
    totalNotificationsSent: number;
    averageMatchesPerRequest: number;
    notificationDeliveryRate: number;
  }> {
    try {
      const notificationService = this.getNotificationService(backend);
      const notificationStats = await notificationService.getNotificationStats(startDate, endDate);

      // These would typically come from a database or analytics service
      // For now, return mock data based on notification stats
      const totalRequestsProcessed = Math.floor(notificationStats.totalSent / 10); // Assume avg 10 donors per request
      const totalDonorsMatched = notificationStats.totalSent;
      const averageMatchesPerRequest = totalRequestsProcessed > 0 ? totalDonorsMatched / totalRequestsProcessed : 0;

      return {
        totalRequestsProcessed,
        totalDonorsMatched,
        totalNotificationsSent: notificationStats.totalSent,
        averageMatchesPerRequest: Math.round(averageMatchesPerRequest * 100) / 100,
        notificationDeliveryRate: notificationStats.deliveryRate
      };

    } catch (error) {
      console.error('Error getting integration stats:', error);
      return {
        totalRequestsProcessed: 0,
        totalDonorsMatched: 0,
        totalNotificationsSent: 0,
        averageMatchesPerRequest: 0,
        notificationDeliveryRate: 0
      };
    }
  }
}

// Export singleton instance
export const matchingNotificationService = new MatchingNotificationService();