/**
 * Geo-matching service for finding compatible blood donors
 */

import { BloodType, UrgencyLevel, UserType } from '../types/enums';
import { Coordinates, calculateDistance } from '../types/location';
import { getCompatibleDonorTypes, isBloodTypeCompatible } from '../types/bloodCompatibility';
import { User } from '../models/User';
import { BloodRequest } from '../models/BloodRequest';

/**
 * Donor match result interface
 */
export interface DonorMatch {
  donor: User;
  distance: number;
  compatibility: 'exact' | 'compatible';
  lastActive: Date;
  rating: number;
  responseTime?: number; // Average response time in minutes
}

/**
 * Geo-matching search parameters
 */
export interface GeoMatchingParams {
  bloodType: BloodType;
  location: Coordinates;
  urgency: UrgencyLevel;
  maxRadius?: number;
  maxResults?: number;
}

/**
 * Geo-matching service class
 */
export class GeoMatchingService {
  private readonly DEFAULT_RADIUS = 25; // km
  private readonly CRITICAL_RADIUS = 50; // km
  private readonly MAX_RESULTS = 50;

  /**
   * Finds nearby compatible donors for a blood request
   * @param params - Search parameters
   * @param availableDonors - Array of available donors to search through
   * @returns Array of matched donors sorted by priority
   */
  public async findNearbyDonors(
    params: GeoMatchingParams,
    availableDonors: User[]
  ): Promise<DonorMatch[]> {
    const { bloodType, location, urgency, maxRadius, maxResults } = params;
    
    // Determine search radius based on urgency
    const searchRadius = this.getSearchRadius(urgency, maxRadius);
    
    // Get compatible donor blood types
    const compatibleDonorTypes = getCompatibleDonorTypes(bloodType);
    
    // Filter and match donors
    const matches: DonorMatch[] = [];
    
    for (const donor of availableDonors) {
      // Skip if not a donor or not available
      if (!this.isDonorEligible(donor)) {
        continue;
      }
      
      // Calculate distance
      const distance = calculateDistance(location, donor.profile.location.coordinates);
      
      // Skip if outside search radius
      if (distance > searchRadius) {
        continue;
      }
      
      // Check blood type compatibility
      if (!compatibleDonorTypes.includes(donor.profile.bloodType)) {
        continue;
      }
      
      // Create donor match
      const match: DonorMatch = {
        donor,
        distance,
        compatibility: donor.profile.bloodType === bloodType ? 'exact' : 'compatible',
        lastActive: new Date(donor.profile.lastActive),
        rating: donor.donorStats?.rating || 0,
        responseTime: this.calculateAverageResponseTime(donor)
      };
      
      matches.push(match);
    }
    
    // Sort matches by priority and return limited results
    const sortedMatches = this.sortMatchesByPriority(matches, urgency);
    const resultLimit = maxResults || this.MAX_RESULTS;
    
    return sortedMatches.slice(0, resultLimit);
  }

  /**
   * Finds donors for a specific blood request
   * @param request - The blood request
   * @param availableDonors - Array of available donors
   * @returns Array of matched donors
   */
  public async findDonorsForRequest(
    request: BloodRequest,
    availableDonors: User[]
  ): Promise<DonorMatch[]> {
    const params: GeoMatchingParams = {
      bloodType: request.bloodType,
      location: request.location.coordinates,
      urgency: request.urgency
    };
    
    return this.findNearbyDonors(params, availableDonors);
  }

  /**
   * Expands search radius for critical requests when no donors found
   * @param params - Original search parameters
   * @param availableDonors - Array of available donors
   * @returns Array of matched donors with expanded radius
   */
  public async expandSearchRadius(
    params: GeoMatchingParams,
    availableDonors: User[]
  ): Promise<DonorMatch[]> {
    const expandedParams = {
      ...params,
      maxRadius: this.CRITICAL_RADIUS
    };
    
    return this.findNearbyDonors(expandedParams, availableDonors);
  }

  /**
   * Filters donors by availability and eligibility
   * @param donors - Array of donors to filter
   * @returns Array of eligible donors
   */
  public filterEligibleDonors(donors: User[]): User[] {
    return donors.filter(donor => this.isDonorEligible(donor));
  }

  /**
   * Validates blood type compatibility between donor and recipient
   * @param donorBloodType - Donor's blood type
   * @param recipientBloodType - Recipient's blood type
   * @returns boolean indicating compatibility
   */
  public validateCompatibility(donorBloodType: BloodType, recipientBloodType: BloodType): boolean {
    return isBloodTypeCompatible(donorBloodType, recipientBloodType);
  }

  /**
   * Calculates optimal search radius based on urgency and location density
   * @param urgency - Request urgency level
   * @param customRadius - Custom radius override
   * @returns Search radius in kilometers
   */
  private getSearchRadius(urgency: UrgencyLevel, customRadius?: number): number {
    if (customRadius) {
      return customRadius;
    }
    
    switch (urgency) {
      case UrgencyLevel.CRITICAL:
        return this.CRITICAL_RADIUS;
      case UrgencyLevel.URGENT:
        return Math.min(this.DEFAULT_RADIUS * 1.5, this.CRITICAL_RADIUS);
      case UrgencyLevel.NORMAL:
        return this.DEFAULT_RADIUS;
      default:
        return this.DEFAULT_RADIUS;
    }
  }

  /**
   * Checks if a donor is eligible for matching
   * @param donor - The donor to check
   * @returns boolean indicating eligibility
   */
  private isDonorEligible(donor: User): boolean {
    // Must be a donor or both
    if (donor.profile.userType !== UserType.DONOR && donor.profile.userType !== UserType.BOTH) {
      return false;
    }
    
    // Must be available
    if (!donor.profile.isAvailable) {
      return false;
    }
    
    // Must have valid location
    if (!donor.profile.location?.coordinates) {
      return false;
    }
    
    // Must have been active recently (within 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const lastActive = new Date(donor.profile.lastActive);
    
    if (lastActive < thirtyDaysAgo) {
      return false;
    }
    
    return true;
  }

  /**
   * Sorts donor matches by priority based on multiple factors
   * @param matches - Array of donor matches
   * @param urgency - Request urgency level
   * @returns Sorted array of donor matches
   */
  private sortMatchesByPriority(matches: DonorMatch[], urgency: UrgencyLevel): DonorMatch[] {
    return matches.sort((a, b) => {
      // For critical requests, prioritize distance above all
      if (urgency === UrgencyLevel.CRITICAL) {
        if (Math.abs(a.distance - b.distance) > 1) { // 1km difference threshold
          return a.distance - b.distance;
        }
      }
      
      // Prioritize exact blood type matches
      if (a.compatibility !== b.compatibility) {
        return a.compatibility === 'exact' ? -1 : 1;
      }
      
      // Prioritize by distance (closer first)
      const distanceDiff = a.distance - b.distance;
      if (Math.abs(distanceDiff) > 0.5) { // 500m difference threshold
        return distanceDiff;
      }
      
      // Prioritize by recent activity
      const activityDiff = b.lastActive.getTime() - a.lastActive.getTime();
      if (Math.abs(activityDiff) > 24 * 60 * 60 * 1000) { // 24 hour difference
        return activityDiff > 0 ? 1 : -1;
      }
      
      // Prioritize by donor rating
      const ratingDiff = b.rating - a.rating;
      if (Math.abs(ratingDiff) > 0.1) {
        return ratingDiff;
      }
      
      // Finally, prioritize by response time (faster responders first)
      if (a.responseTime && b.responseTime) {
        return a.responseTime - b.responseTime;
      }
      
      return 0;
    });
  }

  /**
   * Calculates average response time for a donor (mock implementation)
   * @param donor - The donor
   * @returns Average response time in minutes
   */
  private calculateAverageResponseTime(donor: User): number | undefined {
    // This would typically be calculated from historical response data
    // For now, return a mock value based on donor stats
    if (!donor.donorStats || donor.donorStats.totalDonations === 0) {
      return undefined;
    }
    
    // Mock calculation: assume better rated donors respond faster
    const baseTime = 15; // 15 minutes base
    const ratingFactor = Math.max(0, 5 - donor.donorStats.rating); // 0-5 scale
    return baseTime + (ratingFactor * 5); // Add up to 25 minutes for lower ratings
  }

  /**
   * Gets statistics about the matching process
   * @param matches - Array of donor matches
   * @returns Matching statistics
   */
  public getMatchingStats(matches: DonorMatch[]): {
    totalMatches: number;
    exactMatches: number;
    compatibleMatches: number;
    averageDistance: number;
    averageRating: number;
  } {
    if (matches.length === 0) {
      return {
        totalMatches: 0,
        exactMatches: 0,
        compatibleMatches: 0,
        averageDistance: 0,
        averageRating: 0
      };
    }
    
    const exactMatches = matches.filter(m => m.compatibility === 'exact').length;
    const compatibleMatches = matches.filter(m => m.compatibility === 'compatible').length;
    const averageDistance = matches.reduce((sum, m) => sum + m.distance, 0) / matches.length;
    const averageRating = matches.reduce((sum, m) => sum + m.rating, 0) / matches.length;
    
    return {
      totalMatches: matches.length,
      exactMatches,
      compatibleMatches,
      averageDistance: Math.round(averageDistance * 100) / 100,
      averageRating: Math.round(averageRating * 100) / 100
    };
  }
}

// Export singleton instance
export const geoMatchingService = new GeoMatchingService();