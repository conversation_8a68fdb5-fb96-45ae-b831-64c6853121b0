/**
 * Notification templates for blood requests and responses
 */

export interface NotificationTemplate {
  id: string;
  name: string;
  title: string;
  body: string;
  data?: Record<string, string>;
}

export const NOTIFICATION_TEMPLATES: Record<string, NotificationTemplate> = {
  BLOOD_REQUEST_CRITICAL: {
    id: 'blood_request_critical',
    name: 'Critical Blood Request',
    title: '🚨 URGENT: Blood Needed - {{bloodType}}',
    body: 'Critical blood request {{distance}}km away! {{quantity}} of {{bloodType}} needed immediately.',
    data: {
      type: 'blood_request',
      urgency: 'critical',
    },
  },

  BLOOD_REQUEST_URGENT: {
    id: 'blood_request_urgent',
    name: 'Urgent Blood Request',
    title: '⚡ Blood Needed: {{bloodType}}',
    body: 'Someone {{distance}}km away needs {{quantity}} of {{bloodType}} blood.',
    data: {
      type: 'blood_request',
      urgency: 'urgent',
    },
  },

  BLOOD_REQUEST_NORMAL: {
    id: 'blood_request_normal',
    name: 'Blood Request',
    title: '🩸 Blood Donation Request: {{bloodType}}',
    body: 'A patient {{distance}}km away is looking for {{quantity}} of {{bloodType}} blood.',
    data: {
      type: 'blood_request',
      urgency: 'normal',
    },
  },

  DONOR_RESPONSE_YES: {
    id: 'donor_response_yes',
    name: 'Positive Donor Response',
    title: '✅ Great News! Donor Available',
    body: '{{donorName}} can help with your blood request. Contact: {{donorPhone}}',
    data: {
      type: 'donor_response',
      response: 'yes',
    },
  },

  DONOR_RESPONSE_NO: {
    id: 'donor_response_no',
    name: 'Negative Donor Response',
    title: '❌ Donor Not Available',
    body: '{{donorName}} is currently not available to donate.',
    data: {
      type: 'donor_response',
      response: 'no',
    },
  },

  REQUEST_FULFILLED: {
    id: 'request_fulfilled',
    name: 'Request Fulfilled',
    title: '🎉 Blood Request Fulfilled',
    body: 'Your blood request has been marked as fulfilled. Thank you to all donors who responded!',
    data: {
      type: 'request_update',
      status: 'fulfilled',
    },
  },

  REQUEST_CANCELLED: {
    id: 'request_cancelled',
    name: 'Request Cancelled',
    title: '📋 Blood Request Cancelled',
    body: 'The blood request you responded to has been cancelled by the recipient.',
    data: {
      type: 'request_update',
      status: 'cancelled',
    },
  },

  REQUEST_EXPIRED: {
    id: 'request_expired',
    name: 'Request Expired',
    title: '⏰ Blood Request Expired',
    body: 'Your blood request has expired. You can create a new request if still needed.',
    data: {
      type: 'request_update',
      status: 'expired',
    },
  },

  DONOR_MILESTONE: {
    id: 'donor_milestone',
    name: 'Donor Milestone Achievement',
    title: '🏆 Congratulations! Milestone Reached',
    body: 'You\'ve helped save {{livesCount}} lives through blood donation. Thank you for being a hero!',
    data: {
      type: 'achievement',
      category: 'milestone',
    },
  },

  WEEKLY_SUMMARY: {
    id: 'weekly_summary',
    name: 'Weekly Activity Summary',
    title: '📊 Your Weekly Impact',
    body: 'This week: {{requestsResponded}} requests responded, {{donationsCompleted}} donations completed.',
    data: {
      type: 'summary',
      period: 'weekly',
    },
  },

  LOCATION_UPDATE_REMINDER: {
    id: 'location_update_reminder',
    name: 'Location Update Reminder',
    title: '📍 Update Your Location',
    body: 'Your location hasn\'t been updated in a while. Update it to receive relevant blood requests.',
    data: {
      type: 'reminder',
      category: 'location',
    },
  },

  AVAILABILITY_REMINDER: {
    id: 'availability_reminder',
    name: 'Availability Status Reminder',
    title: '🔔 Check Your Availability',
    body: 'Don\'t forget to update your availability status if your donation status has changed.',
    data: {
      type: 'reminder',
      category: 'availability',
    },
  },
};

/**
 * Replace template placeholders with actual values
 */
export function interpolateTemplate(
  template: NotificationTemplate,
  variables: Record<string, string>
): { title: string; body: string; data: Record<string, string> } {
  let title = template.title;
  let body = template.body;

  // Replace placeholders in title and body
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    title = title.replace(new RegExp(placeholder, 'g'), value);
    body = body.replace(new RegExp(placeholder, 'g'), value);
  });

  // Merge template data with provided variables
  const data = {
    ...template.data,
    ...variables,
  };

  return { title, body, data };
}

/**
 * Get template by urgency level for blood requests
 */
export function getBloodRequestTemplate(urgency: 'critical' | 'urgent' | 'normal'): NotificationTemplate {
  switch (urgency) {
    case 'critical':
      return NOTIFICATION_TEMPLATES.BLOOD_REQUEST_CRITICAL;
    case 'urgent':
      return NOTIFICATION_TEMPLATES.BLOOD_REQUEST_URGENT;
    case 'normal':
      return NOTIFICATION_TEMPLATES.BLOOD_REQUEST_NORMAL;
    default:
      return NOTIFICATION_TEMPLATES.BLOOD_REQUEST_NORMAL;
  }
}

/**
 * Get template for donor response
 */
export function getDonorResponseTemplate(response: 'yes' | 'no'): NotificationTemplate {
  return response === 'yes' 
    ? NOTIFICATION_TEMPLATES.DONOR_RESPONSE_YES 
    : NOTIFICATION_TEMPLATES.DONOR_RESPONSE_NO;
}

/**
 * Validate template variables
 */
export function validateTemplateVariables(
  template: NotificationTemplate,
  variables: Record<string, string>
): { isValid: boolean; missingVariables: string[] } {
  const placeholderRegex = /\{\{(\w+)\}\}/g;
  const requiredVariables = new Set<string>();
  
  // Extract required variables from title and body
  let match;
  while ((match = placeholderRegex.exec(template.title + ' ' + template.body)) !== null) {
    requiredVariables.add(match[1]);
  }

  const missingVariables = Array.from(requiredVariables).filter(
    variable => !(variable in variables)
  );

  return {
    isValid: missingVariables.length === 0,
    missingVariables,
  };
}