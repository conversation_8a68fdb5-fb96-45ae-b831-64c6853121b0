# Geo-Matching Service

## Overview

The Geo-Matching Service is a core component of the UBLOOD Clone backend that handles finding compatible blood donors based on location proximity and blood type compatibility.

## Features

### Core Algorithm
- **Haversine Formula**: Calculates accurate distances between geographic coordinates
- **Blood Type Compatibility Matrix**: Validates donor-recipient blood type compatibility
- **Radius-based Filtering**: Filters donors within specified distance ranges
- **Urgency-based Radius Expansion**: Automatically expands search radius for critical requests

### Key Components

#### GeoMatchingService Class
- `findNearbyDonors()`: Main method to find compatible donors
- `findDonorsForRequest()`: Find donors for a specific blood request
- `filterEligibleDonors()`: Filter donors by availability and eligibility
- `validateCompatibility()`: Check blood type compatibility
- `getMatchingStats()`: Generate matching statistics

#### Matching Criteria
1. **Blood Type Compatibility**: Uses medical compatibility rules
2. **Geographic Proximity**: Distance-based filtering
3. **Donor Availability**: Only includes available donors
4. **Recent Activity**: Prioritizes recently active donors
5. **Donor Rating**: Considers donor ratings and history

#### Search Radius Logic
- **Normal Requests**: 25km radius
- **Urgent Requests**: 37.5km radius (25km × 1.5)
- **Critical Requests**: 50km radius

## API Endpoints

### Firebase Routes
- `POST /api/matching/firebase/find-donors`: Find compatible donors
- `GET /api/matching/firebase/:requestId`: Get matches for specific request

### Appwrite Routes
- `POST /api/matching/appwrite/find-donors`: Find compatible donors
- `GET /api/matching/appwrite/:requestId`: Get matches for specific request

## Request/Response Format

### Find Donors Request
```json
{
  "bloodType": "A+",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060
  },
  "urgency": "critical",
  "maxRadius": 50,
  "maxResults": 25
}
```

### Response Format
```json
{
  "success": true,
  "data": {
    "matches": [
      {
        "donorId": "donor123",
        "name": "John Doe",
        "bloodType": "O-",
        "distance": 3.2,
        "compatibility": "compatible",
        "rating": 4.8,
        "totalDonations": 5,
        "lastActive": "2024-01-15T10:30:00Z"
      }
    ],
    "stats": {
      "totalMatches": 12,
      "exactMatches": 3,
      "compatibleMatches": 9,
      "averageDistance": 8.5,
      "averageRating": 4.6
    },
    "searchParams": {
      "bloodType": "A+",
      "urgency": "critical",
      "radius": 50,
      "maxResults": 25
    }
  }
}
```

## Blood Type Compatibility Matrix

| Donor | Can Donate To |
|-------|---------------|
| O-    | All blood types (Universal donor) |
| O+    | O+, A+, B+, AB+ |
| A-    | A-, A+, AB-, AB+ |
| A+    | A+, AB+ |
| B-    | B-, B+, AB-, AB+ |
| B+    | B+, AB+ |
| AB-   | AB-, AB+ |
| AB+   | AB+ only |

## Testing

The service includes comprehensive unit tests covering:
- Distance calculations
- Blood type compatibility validation
- Donor filtering logic
- Search radius expansion
- Statistics generation
- Edge cases and error handling

Run tests with:
```bash
npm test geoMatchingService.test.ts
```

## Performance Considerations

- **Efficient Filtering**: Multi-stage filtering reduces computation
- **Distance Optimization**: Uses Haversine formula for accuracy
- **Result Limiting**: Configurable result limits prevent large responses
- **Caching Ready**: Service methods are stateless for easy caching

## Security Features

- **Authentication Required**: All endpoints require valid authentication
- **Input Validation**: Comprehensive validation of all input parameters
- **Rate Limiting**: Protected by global rate limiting middleware
- **Error Handling**: Graceful error handling with detailed error responses

## Future Enhancements

- **Geo-indexing**: Implement spatial database indexing for better performance
- **Machine Learning**: Add ML-based donor ranking
- **Real-time Updates**: WebSocket support for real-time matching updates
- **Advanced Filtering**: Additional filters like donor preferences, medical history