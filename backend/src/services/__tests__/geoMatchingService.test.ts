/**
 * Unit tests for GeoMatchingService
 */

import { GeoMatchingService, DonorMatch, GeoMatchingParams } from '../geoMatchingService';
import { BloodType, UrgencyLevel, UserType } from '../../types/enums';
import { User } from '../../models/User';
import { BloodRequest } from '../../models/BloodRequest';

describe('GeoMatchingService', () => {
  let geoMatchingService: GeoMatchingService;
  let mockDonors: User[];

  beforeEach(() => {
    geoMatchingService = new GeoMatchingService();
    
    // Create mock donors for testing
    mockDonors = [
      {
        id: 'donor1',
        phoneNumber: '+1234567890',
        profile: {
          name: '<PERSON>',
          bloodType: BloodType.O_NEGATIVE,
          location: {
            coordinates: { latitude: 40.7128, longitude: -74.0060 }, // NYC
            address: 'New York, NY'
          },
          isAvailable: true,
          userType: UserType.DONOR,
          joinedAt: '2024-01-01T00:00:00Z',
          lastActive: new Date().toISOString()
        },
        donorStats: {
          totalDonations: 5,
          rating: 4.8,
          reviewCount: 10,
          badges: ['lifesaver']
        },
        notificationSettings: {
          pushEnabled: true,
          maxDistance: 25,
          urgencyLevels: ['critical', 'urgent']
        }
      },
      {
        id: 'donor2',
        phoneNumber: '+1234567891',
        profile: {
          name: 'Jane Smith',
          bloodType: BloodType.A_POSITIVE,
          location: {
            coordinates: { latitude: 40.7589, longitude: -73.9851 }, // Manhattan
            address: 'Manhattan, NY'
          },
          isAvailable: true,
          userType: UserType.DONOR,
          joinedAt: '2024-01-01T00:00:00Z',
          lastActive: new Date().toISOString()
        },
        donorStats: {
          totalDonations: 3,
          rating: 4.5,
          reviewCount: 6,
          badges: []
        },
        notificationSettings: {
          pushEnabled: true,
          maxDistance: 25,
          urgencyLevels: ['critical', 'urgent', 'normal']
        }
      },
      {
        id: 'donor3',
        phoneNumber: '+1234567892',
        profile: {
          name: 'Bob Wilson',
          bloodType: BloodType.O_POSITIVE,
          location: {
            coordinates: { latitude: 40.6782, longitude: -73.9442 }, // Brooklyn
            address: 'Brooklyn, NY'
          },
          isAvailable: false, // Not available
          userType: UserType.DONOR,
          joinedAt: '2024-01-01T00:00:00Z',
          lastActive: new Date().toISOString()
        },
        donorStats: {
          totalDonations: 2,
          rating: 4.2,
          reviewCount: 4,
          badges: []
        },
        notificationSettings: {
          pushEnabled: true,
          maxDistance: 25,
          urgencyLevels: ['critical']
        }
      },
      {
        id: 'donor4',
        phoneNumber: '+1234567893',
        profile: {
          name: 'Alice Johnson',
          bloodType: BloodType.AB_POSITIVE,
          location: {
            coordinates: { latitude: 34.0522, longitude: -118.2437 }, // LA (far away)
            address: 'Los Angeles, CA'
          },
          isAvailable: true,
          userType: UserType.DONOR,
          joinedAt: '2024-01-01T00:00:00Z',
          lastActive: new Date().toISOString()
        },
        donorStats: {
          totalDonations: 1,
          rating: 4.0,
          reviewCount: 2,
          badges: []
        },
        notificationSettings: {
          pushEnabled: true,
          maxDistance: 25,
          urgencyLevels: ['critical', 'urgent', 'normal']
        }
      }
    ];
  });

  describe('findNearbyDonors', () => {
    it('should find compatible donors within radius', async () => {
      const params: GeoMatchingParams = {
        bloodType: BloodType.A_POSITIVE,
        location: { latitude: 40.7128, longitude: -74.0060 }, // NYC
        urgency: UrgencyLevel.URGENT
      };

      const matches = await geoMatchingService.findNearbyDonors(params, mockDonors);

      // Should find O- (universal donor) and A+ (exact match)
      expect(matches.length).toBe(2);
      expect(matches.some(m => m.donor.profile.bloodType === BloodType.O_NEGATIVE)).toBe(true);
      expect(matches.some(m => m.donor.profile.bloodType === BloodType.A_POSITIVE)).toBe(true);
    });

    it('should exclude unavailable donors', async () => {
      const params: GeoMatchingParams = {
        bloodType: BloodType.O_POSITIVE,
        location: { latitude: 40.6782, longitude: -73.9442 }, // Brooklyn
        urgency: UrgencyLevel.NORMAL
      };

      const matches = await geoMatchingService.findNearbyDonors(params, mockDonors);

      // Should not include donor3 who is unavailable
      expect(matches.every(m => m.donor.id !== 'donor3')).toBe(true);
    });

    it('should respect distance limits', async () => {
      const params: GeoMatchingParams = {
        bloodType: BloodType.AB_POSITIVE,
        location: { latitude: 40.7128, longitude: -74.0060 }, // NYC
        urgency: UrgencyLevel.NORMAL,
        maxRadius: 25
      };

      const matches = await geoMatchingService.findNearbyDonors(params, mockDonors);

      // Should not include LA donor (too far)
      expect(matches.every(m => m.donor.id !== 'donor4')).toBe(true);
      expect(matches.every(m => m.distance <= 25)).toBe(true);
    });

    it('should expand radius for critical requests', async () => {
      const params: GeoMatchingParams = {
        bloodType: BloodType.AB_POSITIVE,
        location: { latitude: 40.7128, longitude: -74.0060 }, // NYC
        urgency: UrgencyLevel.CRITICAL
      };

      const matches = await geoMatchingService.findNearbyDonors(params, mockDonors);

      // Critical requests should use 50km radius
      expect(matches.every(m => m.distance <= 50)).toBe(true);
    });

    it('should prioritize exact blood type matches', async () => {
      const params: GeoMatchingParams = {
        bloodType: BloodType.A_POSITIVE,
        location: { latitude: 40.7128, longitude: -74.0060 }, // NYC
        urgency: UrgencyLevel.NORMAL
      };

      const matches = await geoMatchingService.findNearbyDonors(params, mockDonors);

      if (matches.length > 1) {
        // Exact matches should come first
        const exactMatches = matches.filter(m => m.compatibility === 'exact');
        const compatibleMatches = matches.filter(m => m.compatibility === 'compatible');
        
        if (exactMatches.length > 0 && compatibleMatches.length > 0) {
          expect(matches.indexOf(exactMatches[0])).toBeLessThan(matches.indexOf(compatibleMatches[0]));
        }
      }
    });
  });

  describe('validateCompatibility', () => {
    it('should validate O- as universal donor', () => {
      expect(geoMatchingService.validateCompatibility(BloodType.O_NEGATIVE, BloodType.A_POSITIVE)).toBe(true);
      expect(geoMatchingService.validateCompatibility(BloodType.O_NEGATIVE, BloodType.B_NEGATIVE)).toBe(true);
      expect(geoMatchingService.validateCompatibility(BloodType.O_NEGATIVE, BloodType.AB_POSITIVE)).toBe(true);
      expect(geoMatchingService.validateCompatibility(BloodType.O_NEGATIVE, BloodType.O_POSITIVE)).toBe(true);
    });

    it('should validate AB+ as universal recipient only', () => {
      expect(geoMatchingService.validateCompatibility(BloodType.AB_POSITIVE, BloodType.AB_POSITIVE)).toBe(true);
      expect(geoMatchingService.validateCompatibility(BloodType.AB_POSITIVE, BloodType.A_POSITIVE)).toBe(false);
      expect(geoMatchingService.validateCompatibility(BloodType.AB_POSITIVE, BloodType.O_NEGATIVE)).toBe(false);
    });

    it('should validate same blood type compatibility', () => {
      expect(geoMatchingService.validateCompatibility(BloodType.A_POSITIVE, BloodType.A_POSITIVE)).toBe(true);
      expect(geoMatchingService.validateCompatibility(BloodType.B_NEGATIVE, BloodType.B_NEGATIVE)).toBe(true);
    });

    it('should reject incompatible blood types', () => {
      expect(geoMatchingService.validateCompatibility(BloodType.A_POSITIVE, BloodType.B_POSITIVE)).toBe(false);
      expect(geoMatchingService.validateCompatibility(BloodType.B_NEGATIVE, BloodType.A_NEGATIVE)).toBe(false);
    });
  });

  describe('filterEligibleDonors', () => {
    it('should filter out non-donors', () => {
      const mixedUsers = [...mockDonors];
      mixedUsers[0].profile.userType = UserType.RECIPIENT;

      const eligible = geoMatchingService.filterEligibleDonors(mixedUsers);

      expect(eligible.every(d => d.profile.userType === UserType.DONOR || d.profile.userType === UserType.BOTH)).toBe(true);
    });

    it('should filter out unavailable donors', () => {
      const eligible = geoMatchingService.filterEligibleDonors(mockDonors);

      expect(eligible.every(d => d.profile.isAvailable)).toBe(true);
      expect(eligible.some(d => d.id === 'donor3')).toBe(false); // donor3 is unavailable
    });
  });

  describe('getMatchingStats', () => {
    it('should calculate correct statistics', async () => {
      const params: GeoMatchingParams = {
        bloodType: BloodType.A_POSITIVE,
        location: { latitude: 40.7128, longitude: -74.0060 },
        urgency: UrgencyLevel.NORMAL
      };

      const matches = await geoMatchingService.findNearbyDonors(params, mockDonors);
      const stats = geoMatchingService.getMatchingStats(matches);

      expect(stats.totalMatches).toBe(matches.length);
      expect(stats.exactMatches + stats.compatibleMatches).toBe(stats.totalMatches);
      expect(stats.averageDistance).toBeGreaterThan(0);
      expect(stats.averageRating).toBeGreaterThanOrEqual(0);
      expect(stats.averageRating).toBeLessThanOrEqual(5);
    });

    it('should handle empty matches', () => {
      const stats = geoMatchingService.getMatchingStats([]);

      expect(stats.totalMatches).toBe(0);
      expect(stats.exactMatches).toBe(0);
      expect(stats.compatibleMatches).toBe(0);
      expect(stats.averageDistance).toBe(0);
      expect(stats.averageRating).toBe(0);
    });
  });

  describe('findDonorsForRequest', () => {
    it('should find donors for a blood request', async () => {
      const mockRequest: BloodRequest = {
        id: 'req1',
        requesterId: 'user1',
        bloodType: BloodType.A_POSITIVE,
        quantity: '2 units',
        urgency: UrgencyLevel.URGENT,
        location: {
          coordinates: { latitude: 40.7128, longitude: -74.0060 },
          address: 'New York, NY'
        },
        contactInfo: {
          name: 'Test User',
          phone: '+1234567890',
          preferredContact: 'call' as any
        },
        status: 'active' as any,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        responses: [],
        notifiedDonors: []
      };

      const matches = await geoMatchingService.findDonorsForRequest(mockRequest, mockDonors);

      expect(matches.length).toBeGreaterThan(0);
      expect(matches.every(m => m.distance <= 50)).toBe(true); // Within reasonable distance
    });
  });
});