/**
 * Unit tests for MatchingNotificationService
 */

import {
  MatchingNotificationService,
  matchingNotificationService,
  MatchingNotificationResult,
  NotificationIntegrationOptions
} from '../matchingNotificationService';
import { geoMatchingService, DonorMatch } from '../geoMatchingService';
import { firebaseNotificationService } from '../notificationService';
import { appwriteNotificationService } from '../appwriteNotificationService';
import { BloodRequest } from '../../models/BloodRequest';
import { User } from '../../models/User';
import { BloodType, UrgencyLevel, RequestStatus, UserType, ContactPreference } from '../../types/enums';

// Mock the services
jest.mock('../geoMatchingService');
jest.mock('../notificationService');
jest.mock('../appwriteNotificationService');

const mockGeoMatchingService = geoMatchingService as jest.Mocked<typeof geoMatchingService>;
const mockFirebaseNotificationService = firebaseNotificationService as jest.Mocked<typeof firebaseNotificationService>;
const mockAppwriteNotificationService = appwriteNotificationService as jest.Mocked<typeof appwriteNotificationService>;

describe('MatchingNotificationService', () => {
  let service: MatchingNotificationService;
  let mockBloodRequest: BloodRequest;
  let mockDonors: User[];
  let mockDonorMatches: DonorMatch[];

  beforeEach(() => {
    service = new MatchingNotificationService();
    
    // Mock blood request
    mockBloodRequest = {
      id: 'req_123',
      requesterId: 'user_456',
      bloodType: BloodType.O_POSITIVE,
      quantity: '2 units',
      urgency: UrgencyLevel.CRITICAL,
      location: {
        coordinates: { latitude: 40.7128, longitude: -74.0060 },
        address: 'New York, NY',
      },
      contactInfo: {
        name: 'John Doe',
        phone: '+1234567890',
        preferredContact: ContactPreference.CALL,
      },
      description: 'Emergency surgery needed',
      status: RequestStatus.ACTIVE,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
      responses: [],
      notifiedDonors: [],
    };

    // Mock donors
    mockDonors = [
      {
        id: 'donor_1',
        phoneNumber: '+1111111111',
        profile: {
          name: 'Alice Smith',
          bloodType: BloodType.O_NEGATIVE,
          location: {
            coordinates: { latitude: 40.7589, longitude: -73.9851 },
            address: 'Manhattan, NY',
            lastUpdated: new Date().toISOString(),
          },
          isAvailable: true,
          userType: UserType.DONOR,
          joinedAt: new Date().toISOString(),
          lastActive: new Date().toISOString(),
        },
        donorStats: {
          totalDonations: 5,
          lastDonationDate: new Date().toISOString(),
          rating: 4.8,
          reviewCount: 10,
          badges: ['lifesaver'],
        },
        notificationSettings: {
          fcmToken: 'fcm_token_1',
          pushEnabled: true,
          maxDistance: 25,
          urgencyLevels: ['critical', 'urgent'],
        },
      },
      {
        id: 'donor_2',
        phoneNumber: '+2222222222',
        profile: {
          name: 'Bob Johnson',
          bloodType: BloodType.O_POSITIVE,
          location: {
            coordinates: { latitude: 40.6782, longitude: -73.9442 },
            address: 'Brooklyn, NY',
            lastUpdated: new Date().toISOString(),
          },
          isAvailable: true,
          userType: UserType.DONOR,
          joinedAt: new Date().toISOString(),
          lastActive: new Date().toISOString(),
        },
        donorStats: {
          totalDonations: 3,
          lastDonationDate: new Date().toISOString(),
          rating: 4.5,
          reviewCount: 6,
          badges: [],
        },
        notificationSettings: {
          fcmToken: 'fcm_token_2',
          pushEnabled: true,
          maxDistance: 30,
          urgencyLevels: ['critical', 'urgent', 'normal'],
        },
      },
    ];

    // Mock donor matches
    mockDonorMatches = [
      {
        donor: mockDonors[0],
        distance: 5.2,
        compatibility: 'compatible',
        lastActive: new Date(),
        rating: 4.8,
        responseTime: 10,
      },
      {
        donor: mockDonors[1],
        distance: 8.1,
        compatibility: 'exact',
        lastActive: new Date(),
        rating: 4.5,
        responseTime: 15,
      },
    ];

    jest.clearAllMocks();
  });

  describe('processBloodRequest', () => {
    it('should successfully process blood request with Firebase backend', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
        expandRadius: false,
      };

      // Mock geo-matching service
      mockGeoMatchingService.findDonorsForRequest.mockResolvedValue(mockDonorMatches);
      mockGeoMatchingService.getMatchingStats.mockReturnValue({
        totalMatches: 2,
        exactMatches: 1,
        compatibleMatches: 1,
        averageDistance: 6.65,
        averageRating: 4.65,
      });

      // Mock notification service
      mockFirebaseNotificationService.notifyDonorsOfBloodRequest.mockResolvedValue({
        successCount: 2,
        failureCount: 0,
        results: [
          { success: true, messageId: 'msg_1', token: 'fcm_token_1' },
          { success: true, messageId: 'msg_2', token: 'fcm_token_2' },
        ],
      });

      const result = await service.processBloodRequest(mockBloodRequest, mockDonors, options);

      expect(result.matchedDonors).toBe(2);
      expect(result.notificationsSent).toBe(2);
      expect(result.notificationsFailed).toBe(0);
      expect(result.matchingStats.totalMatches).toBe(2);
      expect(result.matchingStats.exactMatches).toBe(1);
      expect(result.matchingStats.compatibleMatches).toBe(1);

      expect(mockGeoMatchingService.findDonorsForRequest).toHaveBeenCalledWith(mockBloodRequest, mockDonors);
      expect(mockFirebaseNotificationService.notifyDonorsOfBloodRequest).toHaveBeenCalled();
    });

    it('should successfully process blood request with Appwrite backend', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'appwrite',
        expandRadius: false,
      };

      mockGeoMatchingService.findDonorsForRequest.mockResolvedValue(mockDonorMatches);
      mockGeoMatchingService.getMatchingStats.mockReturnValue({
        totalMatches: 2,
        exactMatches: 1,
        compatibleMatches: 1,
        averageDistance: 6.65,
        averageRating: 4.65,
      });

      mockAppwriteNotificationService.notifyDonorsOfBloodRequest.mockResolvedValue({
        successCount: 2,
        failureCount: 0,
        results: [
          { success: true, messageId: 'msg_1', token: 'fcm_token_1' },
          { success: true, messageId: 'msg_2', token: 'fcm_token_2' },
        ],
      });

      const result = await service.processBloodRequest(mockBloodRequest, mockDonors, options);

      expect(result.matchedDonors).toBe(2);
      expect(result.notificationsSent).toBe(2);
      expect(result.notificationsFailed).toBe(0);

      expect(mockAppwriteNotificationService.notifyDonorsOfBloodRequest).toHaveBeenCalled();
    });

    it('should expand radius when no donors found initially', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
        expandRadius: true,
      };

      // First call returns no matches, second call (expanded) returns matches
      mockGeoMatchingService.findDonorsForRequest.mockResolvedValue([]);
      mockGeoMatchingService.expandSearchRadius.mockResolvedValue(mockDonorMatches);
      mockGeoMatchingService.getMatchingStats.mockReturnValue({
        totalMatches: 2,
        exactMatches: 1,
        compatibleMatches: 1,
        averageDistance: 6.65,
        averageRating: 4.65,
      });

      mockFirebaseNotificationService.notifyDonorsOfBloodRequest.mockResolvedValue({
        successCount: 2,
        failureCount: 0,
        results: [
          { success: true, messageId: 'msg_1', token: 'fcm_token_1' },
          { success: true, messageId: 'msg_2', token: 'fcm_token_2' },
        ],
      });

      const result = await service.processBloodRequest(mockBloodRequest, mockDonors, options);

      expect(result.matchedDonors).toBe(2);
      expect(mockGeoMatchingService.findDonorsForRequest).toHaveBeenCalled();
      expect(mockGeoMatchingService.expandSearchRadius).toHaveBeenCalled();
    });

    it('should handle case when no donors are found', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
        expandRadius: true,
      };

      mockGeoMatchingService.findDonorsForRequest.mockResolvedValue([]);
      mockGeoMatchingService.expandSearchRadius.mockResolvedValue([]);

      const result = await service.processBloodRequest(mockBloodRequest, mockDonors, options);

      expect(result.matchedDonors).toBe(0);
      expect(result.notificationsSent).toBe(0);
      expect(result.notificationsFailed).toBe(0);
      expect(result.matchingStats.totalMatches).toBe(0);
    });

    it('should handle notification failures', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      mockGeoMatchingService.findDonorsForRequest.mockResolvedValue(mockDonorMatches);
      mockGeoMatchingService.getMatchingStats.mockReturnValue({
        totalMatches: 2,
        exactMatches: 1,
        compatibleMatches: 1,
        averageDistance: 6.65,
        averageRating: 4.65,
      });

      mockFirebaseNotificationService.notifyDonorsOfBloodRequest.mockResolvedValue({
        successCount: 1,
        failureCount: 1,
        results: [
          { success: true, messageId: 'msg_1', token: 'fcm_token_1' },
          { success: false, error: 'Invalid FCM token', token: 'fcm_token_2' },
        ],
      });

      const result = await service.processBloodRequest(mockBloodRequest, mockDonors, options);

      expect(result.matchedDonors).toBe(2);
      expect(result.notificationsSent).toBe(1);
      expect(result.notificationsFailed).toBe(1);
    });

    it('should handle errors gracefully', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      mockGeoMatchingService.findDonorsForRequest.mockRejectedValue(new Error('Database error'));

      await expect(service.processBloodRequest(mockBloodRequest, mockDonors, options))
        .rejects.toThrow('Database error');
    });
  });

  describe('processDonorResponse', () => {
    it('should process positive donor response with Firebase', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      mockFirebaseNotificationService.notifyRecipientOfDonorResponse.mockResolvedValue({
        success: true,
        messageId: 'response_msg_1',
      });

      await service.processDonorResponse(
        'req_123',
        mockDonors[0],
        'yes',
        'recipient_fcm_token',
        options
      );

      expect(mockFirebaseNotificationService.notifyRecipientOfDonorResponse).toHaveBeenCalledWith(
        'recipient_fcm_token',
        mockDonors[0],
        'yes',
        'req_123'
      );
    });

    it('should process negative donor response with Appwrite', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'appwrite',
      };

      mockAppwriteNotificationService.notifyRecipientOfDonorResponse.mockResolvedValue({
        success: true,
        messageId: 'response_msg_1',
      });

      await service.processDonorResponse(
        'req_123',
        mockDonors[0],
        'no',
        'recipient_fcm_token',
        options
      );

      expect(mockAppwriteNotificationService.notifyRecipientOfDonorResponse).toHaveBeenCalledWith(
        'recipient_fcm_token',
        mockDonors[0],
        'no',
        'req_123'
      );
    });

    it('should retry failed notifications when maxRetries is set', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
        maxRetries: 2,
      };

      // First call fails, second call succeeds
      mockFirebaseNotificationService.notifyRecipientOfDonorResponse
        .mockResolvedValueOnce({ success: false, error: 'Network error' })
        .mockResolvedValueOnce({ success: true, messageId: 'retry_msg_1' });

      await service.processDonorResponse(
        'req_123',
        mockDonors[0],
        'yes',
        'recipient_fcm_token',
        options
      );

      expect(mockFirebaseNotificationService.notifyRecipientOfDonorResponse).toHaveBeenCalledTimes(2);
    });

    it('should handle errors in donor response processing', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      mockFirebaseNotificationService.notifyRecipientOfDonorResponse.mockRejectedValue(
        new Error('Notification service error')
      );

      await expect(service.processDonorResponse(
        'req_123',
        mockDonors[0],
        'yes',
        'recipient_fcm_token',
        options
      )).rejects.toThrow('Notification service error');
    });
  });

  describe('processRequestCancellation', () => {
    it('should send cancellation notifications to all notified donors', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      const notifiedDonorTokens = ['token_1', 'token_2', 'token_3'];

      mockFirebaseNotificationService.sendBatchNotifications.mockResolvedValue({
        successCount: 3,
        failureCount: 0,
        results: [
          { success: true, messageId: 'cancel_1' },
          { success: true, messageId: 'cancel_2' },
          { success: true, messageId: 'cancel_3' },
        ],
      });

      await service.processRequestCancellation(mockBloodRequest, notifiedDonorTokens, options);

      expect(mockFirebaseNotificationService.sendBatchNotifications).toHaveBeenCalledWith(
        notifiedDonorTokens,
        expect.objectContaining({
          title: '❌ Blood Request Cancelled',
          body: expect.stringContaining('O+ blood request'),
          data: expect.objectContaining({
            type: 'request_cancelled',
            requestId: 'req_123',
          }),
        })
      );
    });

    it('should handle empty donor token list', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      await service.processRequestCancellation(mockBloodRequest, [], options);

      expect(mockFirebaseNotificationService.sendBatchNotifications).not.toHaveBeenCalled();
    });

    it('should handle cancellation notification errors', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      const notifiedDonorTokens = ['token_1', 'token_2'];

      mockFirebaseNotificationService.sendBatchNotifications.mockRejectedValue(
        new Error('Batch notification failed')
      );

      await expect(service.processRequestCancellation(
        mockBloodRequest,
        notifiedDonorTokens,
        options
      )).rejects.toThrow('Batch notification failed');
    });
  });

  describe('processRequestFulfillment', () => {
    it('should send fulfillment notifications to other donors', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      const notifiedDonorTokens = ['token_1', 'token_2', 'token_3'];
      const fulfilledBy = 'donor_1';

      mockFirebaseNotificationService.sendBatchNotifications.mockResolvedValue({
        successCount: 3,
        failureCount: 0,
        results: [
          { success: true, messageId: 'fulfill_1' },
          { success: true, messageId: 'fulfill_2' },
          { success: true, messageId: 'fulfill_3' },
        ],
      });

      await service.processRequestFulfillment(
        mockBloodRequest,
        fulfilledBy,
        notifiedDonorTokens,
        options
      );

      expect(mockFirebaseNotificationService.sendBatchNotifications).toHaveBeenCalledWith(
        notifiedDonorTokens,
        expect.objectContaining({
          title: '✅ Blood Request Fulfilled',
          body: expect.stringContaining('O+ blood request has been fulfilled'),
          data: expect.objectContaining({
            type: 'request_fulfilled',
            requestId: 'req_123',
            fulfilledBy: 'donor_1',
          }),
        })
      );
    });

    it('should handle empty donor token list for fulfillment', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
      };

      await service.processRequestFulfillment(mockBloodRequest, 'donor_1', [], options);

      expect(mockFirebaseNotificationService.sendBatchNotifications).not.toHaveBeenCalled();
    });
  });

  describe('getIntegrationStats', () => {
    it('should return integration statistics', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      mockFirebaseNotificationService.getNotificationStats.mockResolvedValue({
        totalSent: 100,
        totalDelivered: 95,
        totalFailed: 5,
        deliveryRate: 0.95,
      });

      const stats = await service.getIntegrationStats(startDate, endDate, 'firebase');

      expect(stats.totalRequestsProcessed).toBe(10); // 100 notifications / 10 avg per request
      expect(stats.totalDonorsMatched).toBe(100);
      expect(stats.totalNotificationsSent).toBe(100);
      expect(stats.averageMatchesPerRequest).toBe(10);
      expect(stats.notificationDeliveryRate).toBe(0.95);
    });

    it('should handle errors in stats retrieval', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      mockFirebaseNotificationService.getNotificationStats.mockRejectedValue(
        new Error('Stats service error')
      );

      const stats = await service.getIntegrationStats(startDate, endDate, 'firebase');

      expect(stats.totalRequestsProcessed).toBe(0);
      expect(stats.totalDonorsMatched).toBe(0);
      expect(stats.totalNotificationsSent).toBe(0);
      expect(stats.averageMatchesPerRequest).toBe(0);
      expect(stats.notificationDeliveryRate).toBe(0);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should throw error for unsupported notification backend', async () => {
      const options = {
        backend: 'unsupported' as any,
      };

      await expect(service.processBloodRequest(mockBloodRequest, mockDonors, options))
        .rejects.toThrow('Unsupported notification backend: unsupported');
    });

    it('should handle retry mechanism with exponential backoff', async () => {
      const options: NotificationIntegrationOptions = {
        backend: 'firebase',
        maxRetries: 3,
      };

      // All retry attempts fail
      mockFirebaseNotificationService.notifyRecipientOfDonorResponse.mockResolvedValue({
        success: false,
        error: 'Persistent error',
      });

      // Mock setTimeout to avoid actual delays in tests
      jest.spyOn(global, 'setTimeout').mockImplementation((callback: any) => {
        callback();
        return {} as any;
      });

      await service.processDonorResponse(
        'req_123',
        mockDonors[0],
        'yes',
        'recipient_fcm_token',
        options
      );

      // Should have been called multiple times due to retries
      expect(mockFirebaseNotificationService.notifyRecipientOfDonorResponse).toHaveBeenCalledTimes(4); // Initial + 3 retries

      jest.restoreAllMocks();
    });
  });

  describe('Singleton Instance', () => {
    it('should export a singleton instance', () => {
      expect(matchingNotificationService).toBeInstanceOf(MatchingNotificationService);
    });
  });
});