import { firebaseNotificationService } from '../notificationService';
import { appwriteNotificationService } from '../appwriteNotificationService';
import { 
  NOTIFICATION_TEMPLATES, 
  interpolateTemplate, 
  validateTemplateVariables,
  getBloodRequestTemplate,
  getDonorResponseTemplate
} from '../notificationTemplates';
import { BloodRequest } from '../../models/BloodRequest';
import { User } from '../../models/User';
import { BloodType, UrgencyLevel, RequestStatus, UserType, ContactPreference } from '../../types/enums';

// Mock Firebase and Appwrite
jest.mock('../../config/firebase');
jest.mock('../../config/appwrite');

describe('Notification Services', () => {
  describe('Firebase Notification Service', () => {
    const mockFCMToken = 'mock-fcm-token';
    const mockPayload = {
      title: 'Test Notification',
      body: 'This is a test notification',
      data: { type: 'test' },
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('should create blood request notification payload', () => {
      const mockRequest: BloodRequest = {
        id: 'req_123',
        requesterId: 'user_456',
        bloodType: BloodType.O_POSITIVE,
        quantity: '2 units',
        urgency: UrgencyLevel.CRITICAL,
        location: {
          coordinates: { latitude: 40.7128, longitude: -74.0060 },
          address: 'New York, NY',
        },
        contactInfo: {
          name: 'John Doe',
          phone: '+1234567890',
          preferredContact: ContactPreference.CALL,
        },
        description: 'Emergency surgery needed',
        status: RequestStatus.ACTIVE,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
        responses: [],
        notifiedDonors: [],
      };

      const distance = 5.2;
      const { payload, data } = firebaseNotificationService.createBloodRequestNotification(
        mockRequest,
        distance
      );

      expect(payload.title).toBe('🚨 Blood Needed: O+');
      expect(payload.body).toContain('5.2km away');
      expect(payload.body).toContain('2 units needed');
      expect(payload.data?.type).toBe('blood_request');
      expect(payload.data?.requestId).toBe('req_123');
      expect(payload.data?.urgency).toBe('critical');
      expect(data.recipientName).toBe('John Doe');
      expect(data.recipientPhone).toBe('+1234567890');
    });

    test('should create donor response notification payload', () => {
      const mockDonor: User = {
        id: 'donor_123',
        phoneNumber: '+1987654321',
        profile: {
          name: 'Jane Smith',
          bloodType: BloodType.O_POSITIVE,
          location: {
            coordinates: { latitude: 40.7128, longitude: -74.0060 },
            address: 'New York, NY',
            lastUpdated: new Date().toISOString(),
          },
          isAvailable: true,
          userType: UserType.DONOR,
          joinedAt: new Date().toISOString(),
          lastActive: new Date().toISOString(),
        },
        donorStats: {
          totalDonations: 3,
          lastDonationDate: new Date().toISOString(),
          rating: 4.8,
          reviewCount: 5,
          badges: ['lifesaver'],
        },
        notificationSettings: {
          fcmToken: 'mock-token',
          pushEnabled: true,
          maxDistance: 25,
          urgencyLevels: ['critical', 'urgent'],
        },
      };

      const { payload, data } = firebaseNotificationService.createDonorResponseNotification(
        mockDonor,
        'yes'
      );

      expect(payload.title).toBe('✅ Donor Response');
      expect(payload.body).toContain('Jane Smith can help');
      expect(payload.body).toContain('+1987654321');
      expect(payload.data?.type).toBe('donor_response');
      expect(data.donorName).toBe('Jane Smith');
      expect(data.response).toBe('yes');
    });
  });

  describe('Appwrite Notification Service', () => {
    const mockUserId = 'user_123';
    const mockPayload = {
      title: 'Test Notification',
      body: 'This is a test notification',
      data: { type: 'test' },
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('should create blood request notification payload', () => {
      const mockRequest: BloodRequest = {
        id: 'req_123',
        requesterId: 'user_456',
        bloodType: BloodType.A_NEGATIVE,
        quantity: '1 unit',
        urgency: UrgencyLevel.URGENT,
        location: {
          coordinates: { latitude: 40.7128, longitude: -74.0060 },
          address: 'New York, NY',
        },
        contactInfo: {
          name: 'Alice Johnson',
          phone: '+1555123456',
          preferredContact: ContactPreference.TEXT,
        },
        description: 'Planned surgery',
        status: RequestStatus.ACTIVE,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),
        responses: [],
        notifiedDonors: [],
      };

      const distance = 3.7;
      const { payload } = appwriteNotificationService.createBloodRequestNotification(
        mockRequest,
        distance
      );

      expect(payload.title).toBe('⚡ Blood Needed: A-');
      expect(payload.body).toContain('3.7km away');
      expect(payload.body).toContain('1 unit needed');
      expect(payload.data?.type).toBe('blood_request');
      expect(payload.data?.requestId).toBe('req_123');
      expect(payload.data?.urgency).toBe('urgent');
    });

    test('should create donor response notification payload', () => {
      const mockDonor: User = {
        id: 'donor_456',
        phoneNumber: '+1555987654',
        profile: {
          name: 'Bob Wilson',
          bloodType: BloodType.A_NEGATIVE,
          location: {
            coordinates: { latitude: 40.7128, longitude: -74.0060 },
            address: 'New York, NY',
            lastUpdated: new Date().toISOString(),
          },
          isAvailable: false,
          userType: UserType.DONOR,
          joinedAt: new Date().toISOString(),
          lastActive: new Date().toISOString(),
        },
        donorStats: {
          totalDonations: 1,
          lastDonationDate: new Date().toISOString(),
          rating: 5.0,
          reviewCount: 1,
          badges: [],
        },
        notificationSettings: {
          pushEnabled: true,
          maxDistance: 15,
          urgencyLevels: ['critical'],
        },
      };

      const { payload } = appwriteNotificationService.createDonorResponseNotification(
        mockDonor,
        'no'
      );

      expect(payload.title).toBe('❌ Donor Response');
      expect(payload.body).toContain('Bob Wilson is not available');
      expect(payload.data?.type).toBe('donor_response');
      expect(payload.data?.response).toBe('no');
    });
  });

  describe('Notification Templates', () => {
    test('should interpolate template variables correctly', () => {
      const template = NOTIFICATION_TEMPLATES.BLOOD_REQUEST_CRITICAL;
      const variables = {
        bloodType: 'AB+',
        distance: '2.5',
        quantity: '3 units',
      };

      const result = interpolateTemplate(template, variables);

      expect(result.title).toBe('🚨 URGENT: Blood Needed - AB+');
      expect(result.body).toContain('2.5km away');
      expect(result.body).toContain('3 units of AB+');
      expect(result.data.type).toBe('blood_request');
      expect(result.data.urgency).toBe('critical');
    });

    test('should validate template variables', () => {
      const template = NOTIFICATION_TEMPLATES.DONOR_RESPONSE_YES;
      const validVariables = {
        donorName: 'John Doe',
        donorPhone: '+1234567890',
      };
      const invalidVariables = {
        donorName: 'John Doe',
        // Missing donorPhone
      };

      const validResult = validateTemplateVariables(template, validVariables);
      expect(validResult.isValid).toBe(true);
      expect(validResult.missingVariables).toHaveLength(0);

      const invalidResult = validateTemplateVariables(template, invalidVariables);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.missingVariables).toContain('donorPhone');
    });

    test('should get correct template by urgency', () => {
      expect(getBloodRequestTemplate('critical')).toBe(NOTIFICATION_TEMPLATES.BLOOD_REQUEST_CRITICAL);
      expect(getBloodRequestTemplate('urgent')).toBe(NOTIFICATION_TEMPLATES.BLOOD_REQUEST_URGENT);
      expect(getBloodRequestTemplate('normal')).toBe(NOTIFICATION_TEMPLATES.BLOOD_REQUEST_NORMAL);
    });

    test('should get correct template by donor response', () => {
      expect(getDonorResponseTemplate('yes')).toBe(NOTIFICATION_TEMPLATES.DONOR_RESPONSE_YES);
      expect(getDonorResponseTemplate('no')).toBe(NOTIFICATION_TEMPLATES.DONOR_RESPONSE_NO);
    });

    test('should handle missing template variables gracefully', () => {
      const template = NOTIFICATION_TEMPLATES.WEEKLY_SUMMARY;
      const variables = {
        requestsResponded: '5',
        // Missing donationsCompleted
      };

      const result = interpolateTemplate(template, variables);
      
      // Should still interpolate available variables
      expect(result.body).toContain('5 requests responded');
      // Missing variables should remain as placeholders
      expect(result.body).toContain('{{donationsCompleted}}');
    });
  });

  describe('Template Categories', () => {
    test('should have all required blood request templates', () => {
      expect(NOTIFICATION_TEMPLATES.BLOOD_REQUEST_CRITICAL).toBeDefined();
      expect(NOTIFICATION_TEMPLATES.BLOOD_REQUEST_URGENT).toBeDefined();
      expect(NOTIFICATION_TEMPLATES.BLOOD_REQUEST_NORMAL).toBeDefined();
    });

    test('should have donor response templates', () => {
      expect(NOTIFICATION_TEMPLATES.DONOR_RESPONSE_YES).toBeDefined();
      expect(NOTIFICATION_TEMPLATES.DONOR_RESPONSE_NO).toBeDefined();
    });

    test('should have request status templates', () => {
      expect(NOTIFICATION_TEMPLATES.REQUEST_FULFILLED).toBeDefined();
      expect(NOTIFICATION_TEMPLATES.REQUEST_CANCELLED).toBeDefined();
      expect(NOTIFICATION_TEMPLATES.REQUEST_EXPIRED).toBeDefined();
    });

    test('should have achievement and reminder templates', () => {
      expect(NOTIFICATION_TEMPLATES.DONOR_MILESTONE).toBeDefined();
      expect(NOTIFICATION_TEMPLATES.WEEKLY_SUMMARY).toBeDefined();
      expect(NOTIFICATION_TEMPLATES.LOCATION_UPDATE_REMINDER).toBeDefined();
      expect(NOTIFICATION_TEMPLATES.AVAILABILITY_REMINDER).toBeDefined();
    });
  });

  describe('Notification Data Validation', () => {
    test('should validate blood request notification data', () => {
      const mockRequest: BloodRequest = {
        id: 'req_123',
        requesterId: 'user_456',
        bloodType: BloodType.O_POSITIVE,
        quantity: '2 units',
        urgency: UrgencyLevel.CRITICAL,
        location: {
          coordinates: { latitude: 40.7128, longitude: -74.0060 },
          address: 'New York, NY',
        },
        contactInfo: {
          name: 'Test User',
          phone: '+1234567890',
          preferredContact: ContactPreference.CALL,
        },
        description: 'Test request',
        status: RequestStatus.ACTIVE,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
        responses: [],
        notifiedDonors: [],
      };

      const { data } = firebaseNotificationService.createBloodRequestNotification(mockRequest, 5);

      expect(data.type).toBe('blood_request');
      expect(data.requestId).toBe('req_123');
      expect(data.bloodType).toBe('O+');
      expect(data.urgency).toBe('critical');
      expect(data.recipientName).toBe('Test User');
      expect(data.recipientPhone).toBe('+1234567890');
      expect(parseFloat(data.distance)).toBe(5);
    });

    test('should handle different urgency levels correctly', () => {
      const urgencyLevels: Array<UrgencyLevel> = [UrgencyLevel.CRITICAL, UrgencyLevel.URGENT, UrgencyLevel.NORMAL];
      const expectedEmojis = ['🚨', '⚡', '🩸'];

      urgencyLevels.forEach((urgency, index) => {
        const mockRequest: BloodRequest = {
          id: 'req_123',
          requesterId: 'user_456',
          bloodType: BloodType.B_POSITIVE,
          quantity: '1 unit',
          urgency,
          location: {
            coordinates: { latitude: 40.7128, longitude: -74.0060 },
            address: 'New York, NY',
          },
          contactInfo: {
            name: 'Test User',
            phone: '+1234567890',
            preferredContact: ContactPreference.CALL,
          },
          description: 'Test request',
          status: RequestStatus.ACTIVE,
          createdAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
          responses: [],
          notifiedDonors: [],
        };

        const { payload } = firebaseNotificationService.createBloodRequestNotification(mockRequest, 3);
        expect(payload.title).toContain(expectedEmojis[index]);
        expect(payload.data?.urgency).toBe(urgency);
      });
    });
  });
});