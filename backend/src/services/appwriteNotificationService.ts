/**
 * Appwrite-based notification service
 * Handles push notifications using Appwrite Messaging service
 */

import { BloodRequest } from '../models/BloodRequest';
import { User } from '../models/User';
import { 
  NotificationPayload, 
  BloodRequestNotificationData, 
  DonorResponseNotificationData,
  NotificationResult,
  BatchNotificationResult
} from './notificationService';

export class AppwriteNotificationService {
  /**
   * Send notification to a single user
   */
  async sendNotification(
    fcmToken: string,
    payload: NotificationPayload
  ): Promise<NotificationResult> {
    try {
      // For now, this is a placeholder implementation
      // In a real implementation, you would use Appwrite's Messaging service
      console.log(`[Appwrite] Sending notification to ${fcmToken}:`, payload);
      
      // Simulate successful notification
      const messageId = `appwrite_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Log successful notification
      await this.logNotification({
        token: fcmToken,
        payload,
        status: 'delivered',
        messageId,
        sentAt: new Date(),
      });

      return {
        success: true,
        messageId,
        token: fcmToken,
      };
    } catch (error) {
      console.error('Failed to send Appwrite notification:', error);
      
      // Log failed notification
      await this.logNotification({
        token: fcmToken,
        payload,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date(),
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        token: fcmToken,
      };
    }
  }

  /**
   * Send notifications to multiple users
   */
  async sendBatchNotifications(
    tokens: string[],
    payload: NotificationPayload
  ): Promise<BatchNotificationResult> {
    const results: NotificationResult[] = [];
    let successCount = 0;
    let failureCount = 0;

    // Process in batches (Appwrite may have different limits)
    const batchSize = 100;
    for (let i = 0; i < tokens.length; i += batchSize) {
      const batch = tokens.slice(i, i + batchSize);
      
      const batchResults = await Promise.all(
        batch.map(token => this.sendNotification(token, payload))
      );

      results.push(...batchResults);
      
      batchResults.forEach(result => {
        if (result.success) {
          successCount++;
        } else {
          failureCount++;
        }
      });
    }

    return {
      successCount,
      failureCount,
      results,
    };
  }

  /**
   * Create notification payload for blood request
   */
  createBloodRequestNotification(
    request: BloodRequest,
    distance: number
  ): { payload: NotificationPayload; data: BloodRequestNotificationData } {
    const urgencyEmoji = {
      critical: '🚨',
      urgent: '⚡',
      normal: '🩸',
    };

    const payload: NotificationPayload = {
      title: `${urgencyEmoji[request.urgency]} Blood Needed: ${request.bloodType}`,
      body: `Someone ${distance.toFixed(1)}km away needs your help! ${request.quantity} needed.`,
      data: {
        type: 'blood_request',
        requestId: request.id,
        bloodType: request.bloodType,
        distance: distance.toString(),
        urgency: request.urgency,
        recipientName: request.contactInfo.name,
        recipientPhone: request.contactInfo.phone,
      },
    };

    const data: BloodRequestNotificationData = {
      type: 'blood_request',
      requestId: request.id,
      bloodType: request.bloodType,
      distance: distance.toString(),
      urgency: request.urgency,
      recipientName: request.contactInfo.name,
      recipientPhone: request.contactInfo.phone,
    };

    return { payload, data };
  }

  /**
   * Create notification payload for donor response
   */
  createDonorResponseNotification(
    donor: User,
    response: 'yes' | 'no'
  ): { payload: NotificationPayload; data: DonorResponseNotificationData } {
    const responseEmoji = response === 'yes' ? '✅' : '❌';
    const responseText = response === 'yes' ? 'can help' : 'is not available';

    const payload: NotificationPayload = {
      title: `${responseEmoji} Donor Response`,
      body: `${donor.profile.name} ${responseText}${response === 'yes' ? ` - ${donor.phoneNumber}` : ''}`,
      data: {
        type: 'donor_response',
        donorName: donor.profile.name,
        donorPhone: donor.phoneNumber,
        response,
      },
    };

    const data: DonorResponseNotificationData = {
      type: 'donor_response',
      requestId: '', // Will be set by caller
      donorName: donor.profile.name,
      donorPhone: donor.phoneNumber,
      response,
    };

    return { payload, data };
  }

  /**
   * Send blood request notifications to compatible donors
   */
  async notifyDonorsOfBloodRequest(
    request: BloodRequest,
    donors: Array<{ user: User; distance: number }>
  ): Promise<BatchNotificationResult> {
    const tokens: string[] = [];
    const notifications: Array<{ token: string; distance: number }> = [];

    // Collect FCM tokens from available donors
    donors.forEach(({ user, distance }) => {
      if (user.notificationSettings?.fcmToken && 
          user.notificationSettings?.pushEnabled && 
          user.profile.isAvailable) {
        tokens.push(user.notificationSettings.fcmToken);
        notifications.push({ 
          token: user.notificationSettings.fcmToken, 
          distance 
        });
      }
    });

    if (tokens.length === 0) {
      return {
        successCount: 0,
        failureCount: 0,
        results: [],
      };
    }

    // Use average distance for batch notification
    const avgDistance = notifications.reduce((sum, n) => sum + n.distance, 0) / notifications.length;
    const { payload } = this.createBloodRequestNotification(request, avgDistance);

    const result = await this.sendBatchNotifications(tokens, payload);

    // Update request with notified donors (would need to be implemented in Appwrite service)
    await this.updateNotifiedDonors(request.id, donors.map(d => d.user.id));

    return result;
  }

  /**
   * Send notification to recipient when donor responds
   */
  async notifyRecipientOfDonorResponse(
    recipientToken: string,
    donor: User,
    response: 'yes' | 'no',
    requestId: string
  ): Promise<NotificationResult> {
    const { payload } = this.createDonorResponseNotification(donor, response);
    payload.data = { ...payload.data, requestId };

    return await this.sendNotification(recipientToken, payload);
  }

  /**
   * Log notification for tracking and analytics
   */
  private async logNotification(log: {
    token: string;
    payload: NotificationPayload;
    status: 'delivered' | 'failed';
    messageId?: string;
    error?: string;
    sentAt: Date;
  }): Promise<void> {
    try {
      // In a real implementation, you would store this in Appwrite Database
      console.log('[Appwrite] Notification log:', {
        ...log,
        sentAt: log.sentAt.toISOString(),
      });
    } catch (error) {
      console.error('Failed to log Appwrite notification:', error);
      // Don't throw - logging failure shouldn't break notification sending
    }
  }

  /**
   * Update blood request with list of notified donors
   */
  private async updateNotifiedDonors(
    requestId: string,
    donorIds: string[]
  ): Promise<void> {
    try {
      // In a real implementation, you would update the Appwrite document
      console.log(`[Appwrite] Updating request ${requestId} with notified donors:`, donorIds);
    } catch (error) {
      console.error('Failed to update notified donors in Appwrite:', error);
    }
  }

  /**
   * Get notification delivery statistics
   */
  async getNotificationStats(
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalSent: number;
    totalDelivered: number;
    totalFailed: number;
    deliveryRate: number;
  }> {
    try {
      // In a real implementation, you would query Appwrite Database for notification logs
      console.log(`[Appwrite] Getting notification stats from ${startDate.toISOString()} to ${endDate.toISOString()}`);
      
      // Return mock data for now
      return {
        totalSent: 100,
        totalDelivered: 95,
        totalFailed: 5,
        deliveryRate: 95.0,
      };
    } catch (error) {
      console.error('Failed to get Appwrite notification stats:', error);
      return {
        totalSent: 0,
        totalDelivered: 0,
        totalFailed: 0,
        deliveryRate: 0,
      };
    }
  }
}

export const appwriteNotificationService = new AppwriteNotificationService();