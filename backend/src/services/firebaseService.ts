import { getFirestore, getAuth, getMessaging } from '../config/firebase';
import { 
  DocumentData, 
  CollectionReference,
  Query,
} from 'firebase-admin/firestore';

export class FirebaseService {
  private db = getFirestore();
  private auth = getAuth();
  private messaging = getMessaging();

  // User Management
  async createUser(userData: any): Promise<string> {
    try {
      const docRef = await this.db.collection('users').add({
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async getUserById(userId: string): Promise<DocumentData | null> {
    try {
      const doc = await this.db.collection('users').doc(userId).get();
      return doc.exists ? { id: doc.id, ...doc.data() } : null;
    } catch (error) {
      console.error('Error getting user:', error);
      throw error;
    }
  }

  async updateUser(userId: string, updateData: any): Promise<void> {
    try {
      await this.db.collection('users').doc(userId).update({
        ...updateData,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  async getUserByPhoneNumber(phoneNumber: string): Promise<DocumentData | null> {
    try {
      const snapshot = await this.db.collection('users')
        .where('phoneNumber', '==', phoneNumber)
        .limit(1)
        .get();
      
      if (snapshot.empty) return null;
      
      const doc = snapshot.docs[0];
      return { id: doc.id, ...doc.data() };
    } catch (error) {
      console.error('Error getting user by phone:', error);
      throw error;
    }
  }

  // Blood Request Management
  async createBloodRequest(requestData: any): Promise<string> {
    try {
      const docRef = await this.db.collection('blood_requests').add({
        ...requestData,
        status: 'active',
        responses: [],
        notifiedDonors: [],
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating blood request:', error);
      throw error;
    }
  }

  async getBloodRequestById(requestId: string): Promise<DocumentData | null> {
    try {
      const doc = await this.db.collection('blood_requests').doc(requestId).get();
      return doc.exists ? { id: doc.id, ...doc.data() } : null;
    } catch (error) {
      console.error('Error getting blood request:', error);
      throw error;
    }
  }

  // Alias for getBloodRequestById to match the interface expected by matching routes
  async getBloodRequest(requestId: string): Promise<DocumentData | null> {
    return this.getBloodRequestById(requestId);
  }

  async getAllDonors(): Promise<DocumentData[]> {
    try {
      const snapshot = await this.db.collection('users')
        .where('profile.userType', 'in', ['donor', 'both'])
        .get();
      
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting all donors:', error);
      throw error;
    }
  }

  async updateBloodRequest(requestId: string, updateData: any): Promise<void> {
    try {
      await this.db.collection('blood_requests').doc(requestId).update({
        ...updateData,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating blood request:', error);
      throw error;
    }
  }

  async getActiveBloodRequests(filters?: {
    bloodType?: string;
    urgency?: string;
    limit?: number;
  }): Promise<DocumentData[]> {
    try {
      let query: Query = this.db.collection('blood_requests')
        .where('status', '==', 'active')
        .orderBy('createdAt', 'desc');

      if (filters?.bloodType) {
        query = query.where('bloodType', '==', filters.bloodType);
      }

      if (filters?.urgency) {
        query = query.where('urgency', '==', filters.urgency);
      }

      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      const snapshot = await query.get();
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting active blood requests:', error);
      throw error;
    }
  }

  // Donor Matching
  async findNearbyDonors(params: {
    bloodType: string;
    location: { latitude: number; longitude: number };
    radius: number;
    isAvailable?: boolean;
  }): Promise<DocumentData[]> {
    try {
      // Note: Firestore doesn't support geo-queries natively
      // This is a simplified version - in production, use GeoFirestore or similar
      let query: Query = this.db.collection('users')
        .where('profile.userType', 'in', ['donor', 'both']);

      if (params.isAvailable !== undefined) {
        query = query.where('profile.isAvailable', '==', params.isAvailable);
      }

      const snapshot = await query.get();
      const donors = snapshot.docs.map(doc => {
        const data = doc.data();
        return { id: doc.id, ...data } as DocumentData & { id: string };
      });

      // Filter by distance (simplified - in production use proper geo-indexing)
      return donors.filter(donor => {
        if (!donor.profile?.location?.coordinates) return false;
        
        const distance = this.calculateDistance(
          params.location,
          donor.profile.location.coordinates
        );
        
        return distance <= params.radius;
      });
    } catch (error) {
      console.error('Error finding nearby donors:', error);
      throw error;
    }
  }

  // Authentication
  async verifyIdToken(idToken: string): Promise<any> {
    try {
      const decodedToken = await this.auth.verifyIdToken(idToken);
      return decodedToken;
    } catch (error) {
      console.error('Error verifying ID token:', error);
      throw error;
    }
  }

  async createCustomToken(uid: string, additionalClaims?: object): Promise<string> {
    try {
      return await this.auth.createCustomToken(uid, additionalClaims);
    } catch (error) {
      console.error('Error creating custom token:', error);
      throw error;
    }
  }

  // Notifications
  async sendNotification(params: {
    token: string;
    title: string;
    body: string;
    data?: { [key: string]: string };
  }): Promise<string> {
    try {
      const message = {
        notification: {
          title: params.title,
          body: params.body,
        },
        data: params.data || {},
        token: params.token,
      };

      const response = await this.messaging.send(message);
      return response;
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  async sendBatchNotifications(messages: any[]): Promise<any> {
    try {
      const response = await this.messaging.sendAll(messages);
      return response;
    } catch (error) {
      console.error('Error sending batch notifications:', error);
      throw error;
    }
  }

  // Utility Methods
  private calculateDistance(
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
  ): number {
    // Haversine formula
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(point1.latitude)) * 
      Math.cos(this.toRadians(point2.latitude)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  // Collection References (for advanced queries)
  getUsersCollection(): CollectionReference {
    return this.db.collection('users');
  }

  getBloodRequestsCollection(): CollectionReference {
    return this.db.collection('blood_requests');
  }

  getNotificationsCollection(): CollectionReference {
    return this.db.collection('notifications');
  }
}

export const firebaseService = new FirebaseService();