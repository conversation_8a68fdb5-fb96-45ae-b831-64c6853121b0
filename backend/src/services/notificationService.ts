import { getMessaging } from '../config/firebase';
import { getFirestore } from '../config/firebase';
import { BloodRequest } from '../models/BloodRequest';
import { User } from '../models/User';

export interface NotificationPayload {
  title: string;
  body: string;
  data?: Record<string, string>;
}

export interface BloodRequestNotificationData {
  type: 'blood_request';
  requestId: string;
  bloodType: string;
  distance: string;
  urgency: string;
  recipientName: string;
  recipientPhone: string;
}

export interface DonorResponseNotificationData {
  type: 'donor_response';
  requestId: string;
  donorName: string;
  donorPhone: string;
  response: 'yes' | 'no';
}

export interface NotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
  token: string;
}

export interface BatchNotificationResult {
  successCount: number;
  failureCount: number;
  results: NotificationResult[];
}

export class FirebaseNotificationService {
  private messaging = getMessaging();
  private firestore = getFirestore();

  /**
   * Send notification to a single user
   */
  async sendNotification(
    fcmToken: string,
    payload: NotificationPayload
  ): Promise<NotificationResult> {
    try {
      const message = {
        token: fcmToken,
        notification: {
          title: payload.title,
          body: payload.body,
        },
        data: payload.data || {},
        android: {
          notification: {
            priority: 'high' as const,
            sound: 'default',
            channelId: 'blood_requests',
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: payload.title,
                body: payload.body,
              },
              sound: 'default',
              badge: 1,
            },
          },
        },
      };

      const messageId = await this.messaging.send(message);
      
      // Log successful notification
      await this.logNotification({
        token: fcmToken,
        payload,
        status: 'delivered',
        messageId,
        sentAt: new Date(),
      });

      return {
        success: true,
        messageId,
        token: fcmToken,
      };
    } catch (error) {
      console.error('Failed to send notification:', error);
      
      // Log failed notification
      await this.logNotification({
        token: fcmToken,
        payload,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date(),
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        token: fcmToken,
      };
    }
  }

  /**
   * Send notifications to multiple users
   */
  async sendBatchNotifications(
    tokens: string[],
    payload: NotificationPayload
  ): Promise<BatchNotificationResult> {
    const results: NotificationResult[] = [];
    let successCount = 0;
    let failureCount = 0;

    // Process in batches of 500 (FCM limit)
    const batchSize = 500;
    for (let i = 0; i < tokens.length; i += batchSize) {
      const batch = tokens.slice(i, i + batchSize);
      
      const batchResults = await Promise.all(
        batch.map(token => this.sendNotification(token, payload))
      );

      results.push(...batchResults);
      
      batchResults.forEach(result => {
        if (result.success) {
          successCount++;
        } else {
          failureCount++;
        }
      });
    }

    return {
      successCount,
      failureCount,
      results,
    };
  }

  /**
   * Create notification payload for blood request
   */
  createBloodRequestNotification(
    request: BloodRequest,
    distance: number
  ): { payload: NotificationPayload; data: BloodRequestNotificationData } {
    const urgencyEmoji = {
      critical: '🚨',
      urgent: '⚡',
      normal: '🩸',
    };

    const payload: NotificationPayload = {
      title: `${urgencyEmoji[request.urgency]} Blood Needed: ${request.bloodType}`,
      body: `Someone ${distance.toFixed(1)}km away needs your help! ${request.quantity} needed.`,
      data: {
        type: 'blood_request',
        requestId: request.id,
        bloodType: request.bloodType,
        distance: distance.toString(),
        urgency: request.urgency,
        recipientName: request.contactInfo.name,
        recipientPhone: request.contactInfo.phone,
      },
    };

    const data: BloodRequestNotificationData = {
      type: 'blood_request',
      requestId: request.id,
      bloodType: request.bloodType,
      distance: distance.toString(),
      urgency: request.urgency,
      recipientName: request.contactInfo.name,
      recipientPhone: request.contactInfo.phone,
    };

    return { payload, data };
  }

  /**
   * Create notification payload for donor response
   */
  createDonorResponseNotification(
    donor: User,
    response: 'yes' | 'no'
  ): { payload: NotificationPayload; data: DonorResponseNotificationData } {
    const responseEmoji = response === 'yes' ? '✅' : '❌';
    const responseText = response === 'yes' ? 'can help' : 'is not available';

    const payload: NotificationPayload = {
      title: `${responseEmoji} Donor Response`,
      body: `${donor.profile.name} ${responseText}${response === 'yes' ? ` - ${donor.phoneNumber}` : ''}`,
      data: {
        type: 'donor_response',
        donorName: donor.profile.name,
        donorPhone: donor.phoneNumber,
        response,
      },
    };

    const data: DonorResponseNotificationData = {
      type: 'donor_response',
      requestId: '', // Will be set by caller
      donorName: donor.profile.name,
      donorPhone: donor.phoneNumber,
      response,
    };

    return { payload, data };
  }

  /**
   * Send blood request notifications to compatible donors
   */
  async notifyDonorsOfBloodRequest(
    request: BloodRequest,
    donors: Array<{ user: User; distance: number }>
  ): Promise<BatchNotificationResult> {
    const tokens: string[] = [];
    const notifications: Array<{ token: string; distance: number }> = [];

    // Collect FCM tokens from available donors
    donors.forEach(({ user, distance }) => {
      if (user.notificationSettings?.fcmToken && 
          user.notificationSettings?.pushEnabled && 
          user.profile.isAvailable) {
        tokens.push(user.notificationSettings.fcmToken);
        notifications.push({ 
          token: user.notificationSettings.fcmToken, 
          distance 
        });
      }
    });

    if (tokens.length === 0) {
      return {
        successCount: 0,
        failureCount: 0,
        results: [],
      };
    }

    // Use average distance for batch notification
    const avgDistance = notifications.reduce((sum, n) => sum + n.distance, 0) / notifications.length;
    const { payload } = this.createBloodRequestNotification(request, avgDistance);

    const result = await this.sendBatchNotifications(tokens, payload);

    // Update request with notified donors
    await this.updateNotifiedDonors(request.id, donors.map(d => d.user.id));

    return result;
  }

  /**
   * Send notification to recipient when donor responds
   */
  async notifyRecipientOfDonorResponse(
    recipientToken: string,
    donor: User,
    response: 'yes' | 'no',
    requestId: string
  ): Promise<NotificationResult> {
    const { payload } = this.createDonorResponseNotification(donor, response);
    payload.data = { ...payload.data, requestId };

    return await this.sendNotification(recipientToken, payload);
  }

  /**
   * Log notification for tracking and analytics
   */
  private async logNotification(log: {
    token: string;
    payload: NotificationPayload;
    status: 'delivered' | 'failed';
    messageId?: string;
    error?: string;
    sentAt: Date;
  }): Promise<void> {
    try {
      await this.firestore.collection('notification_logs').add({
        ...log,
        sentAt: log.sentAt.toISOString(),
      });
    } catch (error) {
      console.error('Failed to log notification:', error);
      // Don't throw - logging failure shouldn't break notification sending
    }
  }

  /**
   * Update blood request with list of notified donors
   */
  private async updateNotifiedDonors(
    requestId: string,
    donorIds: string[]
  ): Promise<void> {
    try {
      await this.firestore
        .collection('blood_requests')
        .doc(requestId)
        .update({
          notifiedDonors: donorIds,
          notifiedAt: new Date().toISOString(),
        });
    } catch (error) {
      console.error('Failed to update notified donors:', error);
    }
  }

  /**
   * Get notification delivery statistics
   */
  async getNotificationStats(
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalSent: number;
    totalDelivered: number;
    totalFailed: number;
    deliveryRate: number;
  }> {
    try {
      const logsRef = this.firestore.collection('notification_logs');
      
      const snapshot = await logsRef
        .where('sentAt', '>=', startDate.toISOString())
        .where('sentAt', '<=', endDate.toISOString())
        .get();

      let totalDelivered = 0;
      let totalFailed = 0;

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.status === 'delivered') {
          totalDelivered++;
        } else if (data.status === 'failed') {
          totalFailed++;
        }
      });

      const totalSent = totalDelivered + totalFailed;
      const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;

      return {
        totalSent,
        totalDelivered,
        totalFailed,
        deliveryRate,
      };
    } catch (error) {
      console.error('Failed to get notification stats:', error);
      return {
        totalSent: 0,
        totalDelivered: 0,
        totalFailed: 0,
        deliveryRate: 0,
      };
    }
  }
}

export const firebaseNotificationService = new FirebaseNotificationService();