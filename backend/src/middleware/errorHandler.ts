import { Request, Response, NextFunction } from 'express';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export const errorHandler = (
  err: AppError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // Set default error values
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';
  const isOperational = err.isOperational || false;

  // Log error details
  console.error('🚨 Error occurred:', {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    statusCode,
    message,
    stack: err.stack,
    isOperational,
  });

  // Send error response
  const errorResponse: {
    success: boolean;
    error: {
      code: string;
      message: string;
      timestamp: string;
      stack?: string;
      details?: object;
    };
  } = {
    success: false,
    error: {
      code: getErrorCode(statusCode),
      message: isProduction() ? getProductionMessage(statusCode) : message,
      timestamp: new Date().toISOString(),
    },
  };

  // Include stack trace in development
  if (!isProduction()) {
    errorResponse.error.stack = err.stack;
    errorResponse.error.details = {
      method: req.method,
      url: req.url,
      body: req.body,
      params: req.params,
      query: req.query,
    };
  }

  res.status(statusCode).json(errorResponse);
};

const getErrorCode = (statusCode: number): string => {
  const errorCodes: { [key: number]: string } = {
    400: 'BAD_REQUEST',
    401: 'UNAUTHORIZED',
    403: 'FORBIDDEN',
    404: 'NOT_FOUND',
    409: 'CONFLICT',
    422: 'VALIDATION_ERROR',
    429: 'RATE_LIMIT_EXCEEDED',
    500: 'INTERNAL_SERVER_ERROR',
    502: 'BAD_GATEWAY',
    503: 'SERVICE_UNAVAILABLE',
  };

  return errorCodes[statusCode] || 'UNKNOWN_ERROR';
};

const getProductionMessage = (statusCode: number): string => {
  const productionMessages: { [key: number]: string } = {
    400: 'Bad request. Please check your input.',
    401: 'Authentication required.',
    403: 'Access denied.',
    404: 'Resource not found.',
    409: 'Resource conflict.',
    422: 'Invalid input data.',
    429: 'Too many requests. Please try again later.',
    500: 'Internal server error. Please try again later.',
    502: 'Service temporarily unavailable.',
    503: 'Service temporarily unavailable.',
  };

  return productionMessages[statusCode] || 'An error occurred.';
};

const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

// Custom error classes
export class ValidationError extends Error {
  statusCode = 422;
  isOperational = true;

  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error {
  statusCode = 404;
  isOperational = true;

  constructor(message: string = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends Error {
  statusCode = 401;
  isOperational = true;

  constructor(message: string = 'Authentication required') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends Error {
  statusCode = 403;
  isOperational = true;

  constructor(message: string = 'Access denied') {
    super(message);
    this.name = 'ForbiddenError';
  }
}