import { Request, Response, NextFunction } from 'express';
import { getAuth } from '../config/firebase';
import { getUsers } from '../config/appwrite';
import { firebaseService } from '../services/firebaseService';
import { appwriteService } from '../services/appwriteService';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        uid: string;
        phoneNumber?: string;
        email?: string;
        customClaims?: any;
        userData?: any;
      };
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: {
    uid: string;
    phoneNumber?: string;
    email?: string;
    customClaims?: any;
    userData?: any;
  };
}

/**
 * Middleware to verify Firebase ID token
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authorization header missing or invalid format'
        }
      });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    if (!idToken) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'ID token missing'
        }
      });
      return;
    }

    // Verify the ID token
    const decodedToken = await firebaseService.verifyIdToken(idToken);
    
    // Attach user info to request
    req.user = {
      uid: decodedToken.uid,
      phoneNumber: decodedToken.phone_number,
      email: decodedToken.email,
      customClaims: decodedToken
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    let errorMessage = 'Invalid or expired token';
    let errorCode = 'INVALID_TOKEN';
    
    if (error instanceof Error) {
      if (error.message.includes('expired')) {
        errorMessage = 'Token has expired';
        errorCode = 'TOKEN_EXPIRED';
      } else if (error.message.includes('invalid')) {
        errorMessage = 'Invalid token format';
        errorCode = 'INVALID_TOKEN_FORMAT';
      }
    }

    res.status(401).json({
      success: false,
      error: {
        code: errorCode,
        message: errorMessage
      }
    });
  }
};

/**
 * Middleware to load user data from database
 */
export const loadUserData = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user?.uid) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    // Load user data from Firestore
    const userData = await firebaseService.getUserById(req.user.uid);
    
    if (!userData) {
      res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User profile not found'
        }
      });
      return;
    }

    // Attach user data to request
    req.user.userData = userData;
    
    next();
  } catch (error) {
    console.error('Error loading user data:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to load user data'
      }
    });
  }
};

/**
 * Middleware to check if user has required role/permissions
 */
export const requireRole = (allowedRoles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user?.userData?.profile?.userType) {
        res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'User role not found'
          }
        });
        return;
      }

      const userRole = req.user.userData.profile.userType;
      
      if (!allowedRoles.includes(userRole) && !allowedRoles.includes('both')) {
        res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: `Access denied. Required roles: ${allowedRoles.join(', ')}`
          }
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'AUTHORIZATION_ERROR',
          message: 'Failed to verify user permissions'
        }
      });
    }
  };
};

/**
 * Middleware to check if user profile is complete
 */
export const requireCompleteProfile = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    const userData = req.user?.userData;
    
    if (!userData?.profile) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INCOMPLETE_PROFILE',
          message: 'User profile not found. Please complete your profile first.'
        }
      });
      return;
    }

    const requiredFields = ['name', 'bloodType', 'userType'];
    const missingFields = requiredFields.filter(
      field => !userData.profile[field]
    );

    if (missingFields.length > 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INCOMPLETE_PROFILE',
          message: `Profile incomplete. Missing fields: ${missingFields.join(', ')}`
        }
      });
      return;
    }

    next();
  } catch (error) {
    console.error('Profile check error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PROFILE_CHECK_ERROR',
        message: 'Failed to verify profile completeness'
      }
    });
  }
};

/**
 * Optional authentication - doesn't fail if no token provided
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without authentication
      next();
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    if (idToken) {
      try {
        const decodedToken = await firebaseService.verifyIdToken(idToken);
        req.user = {
          uid: decodedToken.uid,
          phoneNumber: decodedToken.phone_number,
          email: decodedToken.email,
          customClaims: decodedToken
        };
      } catch (error) {
        // Invalid token, but don't fail - just continue without auth
        console.warn('Optional auth failed:', error);
      }
    }

    next();
  } catch (error) {
    // Don't fail on optional auth errors
    console.warn('Optional authentication error:', error);
    next();
  }
};

/**
 * Middleware to verify Appwrite session token
 */
export const authenticateAppwriteSession = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const sessionToken = req.headers['x-appwrite-session'] as string;
    
    if (!sessionToken) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Appwrite session token missing'
        }
      });
      return;
    }

    // For Appwrite, we'll need to verify the session on the client side
    // or use JWT tokens. This is a simplified version.
    const userId = req.headers['x-appwrite-user-id'] as string;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User ID missing from Appwrite session'
        }
      });
      return;
    }

    // Attach user info to request
    req.user = {
      uid: userId,
      customClaims: { provider: 'appwrite' }
    };

    next();
  } catch (error) {
    console.error('Appwrite authentication error:', error);
    res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_SESSION',
        message: 'Invalid Appwrite session'
      }
    });
  }
};

/**
 * Middleware to load user data from Appwrite database
 */
export const loadAppwriteUserData = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user?.uid) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    // Load user data from Appwrite
    const userData = await appwriteService.getUserById(req.user.uid);
    
    if (!userData) {
      res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User profile not found'
        }
      });
      return;
    }

    // Attach user data to request
    req.user.userData = userData;
    
    next();
  } catch (error) {
    console.error('Error loading Appwrite user data:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to load user data from Appwrite'
      }
    });
  }
};