import { Request, Response, NextFunction } from 'express';
import { getAuth } from '../config/firebase';
import { DecodedIdToken } from 'firebase-admin/auth';

// Extend Request interface to include user (Firebase specific)
declare global {
  namespace Express {
    interface Request {
      firebaseUser?: DecodedIdToken;
    }
  }
}

export const verifyFirebaseToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authorization header missing or invalid format'
        }
      });
      return;
    }

    const token = authHeader.split('Bearer ')[1];
    
    if (!token) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Token missing from authorization header'
        }
      });
      return;
    }

    try {
      const decodedToken = await getAuth().verifyIdToken(token);
      req.firebaseUser = decodedToken;
      req.user = {
        uid: decodedToken.uid,
        phoneNumber: decodedToken.phone_number,
        email: decodedToken.email,
        customClaims: decodedToken
      };
      next();
    } catch (error) {
      console.error('Firebase token verification failed:', error);
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired token'
        }
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Authentication service error'
      }
    });
  }
};