/**
 * Unit tests for authentication middleware
 */

import { Request, Response, NextFunction } from 'express';
import {
  authenticateToken,
  loadUserData,
  requireRole,
  requireCompleteProfile,
  optionalAuth,
  authenticateAppwriteSession,
  loadAppwriteUserData,
  AuthenticatedRequest
} from '../authMiddleware';
import { firebaseService } from '../../services/firebaseService';
import { appwriteService } from '../../services/appwriteService';
import { UserType } from '../../types/enums';

// Mock the services
jest.mock('../../services/firebaseService');
jest.mock('../../services/appwriteService');

const mockFirebaseService = firebaseService as jest.Mocked<typeof firebaseService>;
const mockAppwriteService = appwriteService as jest.Mocked<typeof appwriteService>;

describe('Authentication Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let jsonSpy: jest.SpyInstance;
  let statusSpy: jest.SpyInstance;

  beforeEach(() => {
    mockRequest = {
      headers: {},
      user: undefined
    };
    
    jsonSpy = jest.fn();
    statusSpy = jest.fn().mockReturnThis();
    
    mockResponse = {
      status: statusSpy,
      json: jsonSpy
    };
    
    mockNext = jest.fn();
    
    jest.clearAllMocks();
  });

  describe('authenticateToken', () => {
    it('should authenticate valid Firebase token', async () => {
      const mockToken = 'valid-firebase-token';
      const mockDecodedToken = {
        uid: 'user123',
        phone_number: '+1234567890',
        email: '<EMAIL>'
      };

      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      mockFirebaseService.verifyIdToken.mockResolvedValue(mockDecodedToken);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockFirebaseService.verifyIdToken).toHaveBeenCalledWith(mockToken);
      expect(mockRequest.user).toEqual({
        uid: 'user123',
        phoneNumber: '+1234567890',
        email: '<EMAIL>',
        customClaims: mockDecodedToken
      });
      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should reject request without authorization header', async () => {
      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authorization header missing or invalid format'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject request with invalid authorization format', async () => {
      mockRequest.headers = {
        authorization: 'InvalidFormat token123'
      };

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authorization header missing or invalid format'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject request with missing token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer '
      };

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'ID token missing'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle expired token', async () => {
      const mockToken = 'expired-token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      const expiredError = new Error('Token has expired');
      mockFirebaseService.verifyIdToken.mockRejectedValue(expiredError);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'TOKEN_EXPIRED',
          message: 'Token has expired'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle invalid token', async () => {
      const mockToken = 'invalid-token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      const invalidError = new Error('Invalid token format');
      mockFirebaseService.verifyIdToken.mockRejectedValue(invalidError);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_TOKEN_FORMAT',
          message: 'Invalid token format'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('loadUserData', () => {
    it('should load user data successfully', async () => {
      const mockUserData = {
        id: 'user123',
        profile: {
          name: 'John Doe',
          bloodType: 'O+',
          userType: UserType.DONOR
        }
      };

      mockRequest.user = { uid: 'user123' };
      mockFirebaseService.getUserById.mockResolvedValue(mockUserData);

      await loadUserData(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockFirebaseService.getUserById).toHaveBeenCalledWith('user123');
      expect(mockRequest.user.userData).toEqual(mockUserData);
      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should handle missing user authentication', async () => {
      mockRequest.user = undefined;

      await loadUserData(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle user not found', async () => {
      mockRequest.user = { uid: 'nonexistent-user' };
      mockFirebaseService.getUserById.mockResolvedValue(null);

      await loadUserData(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(404);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User profile not found'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      mockRequest.user = { uid: 'user123' };
      mockFirebaseService.getUserById.mockRejectedValue(new Error('Database connection failed'));

      await loadUserData(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(500);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Failed to load user data'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('requireRole', () => {
    it('should allow access for users with required role', () => {
      const middleware = requireRole([UserType.DONOR]);
      
      mockRequest.user = {
        uid: 'user123',
        userData: {
          profile: {
            userType: UserType.DONOR
          }
        }
      };

      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should allow access for users with "both" role when any role is allowed', () => {
      const middleware = requireRole([UserType.DONOR]);
      
      mockRequest.user = {
        uid: 'user123',
        userData: {
          profile: {
            userType: UserType.BOTH
          }
        }
      };

      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should deny access for users without required role', () => {
      const middleware = requireRole([UserType.DONOR]);
      
      mockRequest.user = {
        uid: 'user123',
        userData: {
          profile: {
            userType: UserType.RECIPIENT
          }
        }
      };

      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(403);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Access denied. Required roles: donor'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle missing user role', () => {
      const middleware = requireRole([UserType.DONOR]);
      
      mockRequest.user = {
        uid: 'user123',
        userData: {
          profile: {}
        }
      };

      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(403);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'User role not found'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should allow multiple roles', () => {
      const middleware = requireRole([UserType.DONOR, UserType.RECIPIENT]);
      
      mockRequest.user = {
        uid: 'user123',
        userData: {
          profile: {
            userType: UserType.RECIPIENT
          }
        }
      };

      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });
  });

  describe('requireCompleteProfile', () => {
    it('should allow access for users with complete profile', () => {
      mockRequest.user = {
        userData: {
          profile: {
            name: 'John Doe',
            bloodType: 'O+',
            userType: UserType.DONOR
          }
        }
      };

      requireCompleteProfile(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should deny access for users without profile', () => {
      mockRequest.user = {
        userData: {}
      };

      requireCompleteProfile(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(400);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INCOMPLETE_PROFILE',
          message: 'User profile not found. Please complete your profile first.'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should deny access for users with incomplete profile', () => {
      mockRequest.user = {
        userData: {
          profile: {
            name: 'John Doe',
            // Missing bloodType and userType
          }
        }
      };

      requireCompleteProfile(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(400);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INCOMPLETE_PROFILE',
          message: 'Profile incomplete. Missing fields: bloodType, userType'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should identify specific missing fields', () => {
      mockRequest.user = {
        userData: {
          profile: {
            name: 'John Doe',
            bloodType: 'O+'
            // Missing userType
          }
        }
      };

      requireCompleteProfile(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(400);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INCOMPLETE_PROFILE',
          message: 'Profile incomplete. Missing fields: userType'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('optionalAuth', () => {
    it('should authenticate valid token when provided', async () => {
      const mockToken = 'valid-token';
      const mockDecodedToken = {
        uid: 'user123',
        phone_number: '+1234567890',
        email: '<EMAIL>'
      };

      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      mockFirebaseService.verifyIdToken.mockResolvedValue(mockDecodedToken);

      await optionalAuth(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual({
        uid: 'user123',
        phoneNumber: '+1234567890',
        email: '<EMAIL>',
        customClaims: mockDecodedToken
      });
      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should continue without authentication when no token provided', async () => {
      await optionalAuth(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should continue without authentication when token is invalid', async () => {
      const mockToken = 'invalid-token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      mockFirebaseService.verifyIdToken.mockRejectedValue(new Error('Invalid token'));

      await optionalAuth(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });
  });

  describe('authenticateAppwriteSession', () => {
    it('should authenticate valid Appwrite session', async () => {
      mockRequest.headers = {
        'x-appwrite-session': 'valid-session-token',
        'x-appwrite-user-id': 'appwrite-user-123'
      };

      await authenticateAppwriteSession(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual({
        uid: 'appwrite-user-123',
        customClaims: { provider: 'appwrite' }
      });
      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should reject request without session token', async () => {
      await authenticateAppwriteSession(mockRequest as Request, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Appwrite session token missing'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject request without user ID', async () => {
      mockRequest.headers = {
        'x-appwrite-session': 'valid-session-token'
      };

      await authenticateAppwriteSession(mockRequest as Request, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User ID missing from Appwrite session'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('loadAppwriteUserData', () => {
    it('should load Appwrite user data successfully', async () => {
      const mockUserData = {
        id: 'appwrite-user-123',
        profile: {
          name: 'Jane Doe',
          bloodType: 'A+',
          userType: UserType.RECIPIENT
        }
      };

      mockRequest.user = { uid: 'appwrite-user-123' };
      mockAppwriteService.getUserById.mockResolvedValue(mockUserData);

      await loadAppwriteUserData(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockAppwriteService.getUserById).toHaveBeenCalledWith('appwrite-user-123');
      expect(mockRequest.user.userData).toEqual(mockUserData);
      expect(mockNext).toHaveBeenCalled();
      expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should handle missing user authentication', async () => {
      mockRequest.user = undefined;

      await loadAppwriteUserData(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(401);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle user not found in Appwrite', async () => {
      mockRequest.user = { uid: 'nonexistent-user' };
      mockAppwriteService.getUserById.mockResolvedValue(null);

      await loadAppwriteUserData(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(404);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User profile not found'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle Appwrite database errors', async () => {
      mockRequest.user = { uid: 'user123' };
      mockAppwriteService.getUserById.mockRejectedValue(new Error('Appwrite connection failed'));

      await loadAppwriteUserData(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(500);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Failed to load user data from Appwrite'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle middleware errors gracefully', async () => {
      const middleware = requireRole([UserType.DONOR]);
      
      // Simulate an error in the middleware
      mockRequest.user = null as any;

      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(500);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'AUTHORIZATION_ERROR',
          message: 'Failed to verify user permissions'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle profile check errors gracefully', () => {
      // Simulate an error in profile checking
      mockRequest.user = null as any;

      requireCompleteProfile(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(statusSpy).toHaveBeenCalledWith(500);
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'PROFILE_CHECK_ERROR',
          message: 'Failed to verify profile completeness'
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});