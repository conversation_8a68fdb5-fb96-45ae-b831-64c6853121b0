import express from 'express';
import { appwriteNotificationService } from '../../services/appwriteNotificationService';
import { authMiddleware } from '../../middleware/authMiddleware';
import { 
  NOTIFICATION_TEMPLATES, 
  interpolateTemplate, 
  validateTemplateVariables 
} from '../../services/notificationTemplates';

const router = express.Router();

/**
 * @swagger
 * /api/notifications/appwrite/send:
 *   post:
 *     summary: Send manual notification via Appwrite
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - title
 *               - body
 *             properties:
 *               userId:
 *                 type: string
 *                 description: Target user ID
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional data payload
 *               topics:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Notification topics
 *     responses:
 *       200:
 *         description: Notification sent successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to send notification
 */
router.post('/send', authMiddleware, async (req, res) => {
  try {
    const { userId, title, body, data, topics } = req.body;

    if (!userId || !title || !body) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, title, body',
      });
    }

    const result = await appwriteNotificationService.sendNotification(userId, {
      title,
      body,
      data,
    }, topics);

    if (result.success) {
      res.json({
        success: true,
        messageId: result.messageId,
        message: 'Notification sent successfully',
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Failed to send Appwrite notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send notification',
    });
  }
});

/**
 * @swagger
 * /api/notifications/appwrite/send-batch:
 *   post:
 *     summary: Send batch notifications via Appwrite
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userIds
 *               - title
 *               - body
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of target user IDs
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional data payload
 *               topics:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Notification topics
 *     responses:
 *       200:
 *         description: Batch notifications sent
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to send notifications
 */
router.post('/send-batch', authMiddleware, async (req, res) => {
  try {
    const { userIds, title, body, data, topics } = req.body;

    if (!userIds || !Array.isArray(userIds) || !title || !body) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userIds (array), title, body',
      });
    }

    const result = await appwriteNotificationService.sendBatchNotifications(userIds, {
      title,
      body,
      data,
    }, topics);

    res.json({
      success: true,
      successCount: result.successCount,
      failureCount: result.failureCount,
      totalSent: userIds.length,
      message: 'Batch notifications processed',
    });
  } catch (error) {
    console.error('Failed to send Appwrite batch notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send batch notifications',
    });
  }
});

/**
 * @swagger
 * /api/notifications/appwrite/send-template:
 *   post:
 *     summary: Send notification using template via Appwrite
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - templateId
 *               - variables
 *             properties:
 *               userId:
 *                 type: string
 *                 description: Target user ID
 *               templateId:
 *                 type: string
 *                 description: Notification template ID
 *               variables:
 *                 type: object
 *                 description: Template variables
 *               topics:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Notification topics
 *     responses:
 *       200:
 *         description: Template notification sent successfully
 *       400:
 *         description: Invalid template or missing variables
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to send notification
 */
router.post('/send-template', authMiddleware, async (req, res) => {
  try {
    const { userId, templateId, variables, topics } = req.body;

    if (!userId || !templateId || !variables) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, templateId, variables',
      });
    }

    const template = NOTIFICATION_TEMPLATES[templateId];
    if (!template) {
      return res.status(400).json({
        success: false,
        error: `Template not found: ${templateId}`,
      });
    }

    // Validate template variables
    const validation = validateTemplateVariables(template, variables);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Missing template variables',
        missingVariables: validation.missingVariables,
      });
    }

    // Interpolate template
    const { title, body, data } = interpolateTemplate(template, variables);

    const result = await appwriteNotificationService.sendNotification(userId, {
      title,
      body,
      data,
    }, topics);

    if (result.success) {
      res.json({
        success: true,
        messageId: result.messageId,
        template: templateId,
        message: 'Template notification sent successfully',
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Failed to send Appwrite template notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send template notification',
    });
  }
});

/**
 * @swagger
 * /api/notifications/appwrite/history/{userId}:
 *   get:
 *     summary: Get notification history for user
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of notifications to retrieve
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for filtering
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for filtering
 *     responses:
 *       200:
 *         description: Notification history retrieved
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to retrieve history
 */
router.get('/history/:userId', authMiddleware, async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 50, startDate, endDate } = req.query;
    
    // Note: This would query Appwrite database for user's notification history
    // For now, we'll return a placeholder response
    const history = {
      notifications: [],
      total: 0,
      limit: parseInt(limit as string),
      userId,
      message: 'Notification history feature coming soon',
    };

    res.json({
      success: true,
      data: history,
    });
  } catch (error) {
    console.error('Failed to get Appwrite notification history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve notification history',
    });
  }
});

/**
 * @swagger
 * /api/notifications/appwrite/stats:
 *   get:
 *     summary: Get notification delivery statistics
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for statistics
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for statistics
 *     responses:
 *       200:
 *         description: Notification statistics retrieved
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to retrieve statistics
 */
router.get('/stats', authMiddleware, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const start = startDate ? new Date(startDate as string) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate as string) : new Date();

    const stats = await appwriteNotificationService.getNotificationStats(start, end);

    res.json({
      success: true,
      data: {
        ...stats,
        period: {
          startDate: start.toISOString(),
          endDate: end.toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('Failed to get Appwrite notification stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve notification statistics',
    });
  }
});

/**
 * @swagger
 * /api/notifications/appwrite/topics/subscribe:
 *   post:
 *     summary: Subscribe user to notification topics
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - topics
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID
 *               topics:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Topics to subscribe to
 *     responses:
 *       200:
 *         description: Successfully subscribed to topics
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to subscribe to topics
 */
router.post('/topics/subscribe', authMiddleware, async (req, res) => {
  try {
    const { userId, topics } = req.body;

    if (!userId || !topics || !Array.isArray(topics)) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, topics (array)',
      });
    }

    await appwriteNotificationService.subscribeToTopics(userId, topics);

    res.json({
      success: true,
      message: 'Successfully subscribed to topics',
      topics,
    });
  } catch (error) {
    console.error('Failed to subscribe to topics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to subscribe to topics',
    });
  }
});

/**
 * @swagger
 * /api/notifications/appwrite/topics/unsubscribe:
 *   post:
 *     summary: Unsubscribe user from notification topics
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - topics
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID
 *               topics:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Topics to unsubscribe from
 *     responses:
 *       200:
 *         description: Successfully unsubscribed from topics
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to unsubscribe from topics
 */
router.post('/topics/unsubscribe', authMiddleware, async (req, res) => {
  try {
    const { userId, topics } = req.body;

    if (!userId || !topics || !Array.isArray(topics)) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, topics (array)',
      });
    }

    await appwriteNotificationService.unsubscribeFromTopics(userId, topics);

    res.json({
      success: true,
      message: 'Successfully unsubscribed from topics',
      topics,
    });
  } catch (error) {
    console.error('Failed to unsubscribe from topics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to unsubscribe from topics',
    });
  }
});

/**
 * @swagger
 * /api/notifications/appwrite/templates:
 *   get:
 *     summary: Get available notification templates
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Available templates retrieved
 */
router.get('/templates', (req, res) => {
  try {
    const templates = Object.entries(NOTIFICATION_TEMPLATES).map(([key, template]) => ({
      id: key,
      name: template.name,
      title: template.title,
      body: template.body,
    }));

    res.json({
      success: true,
      data: templates,
    });
  } catch (error) {
    console.error('Failed to get notification templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve templates',
    });
  }
});

export { router as appwriteNotificationRoutes };