import express from 'express';
import { firebaseNotificationRoutes } from './firebase';
import { appwriteNotificationRoutes } from './appwrite';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Notifications
 *   description: Notification management endpoints
 */

// Mount Firebase notification routes
router.use('/firebase', firebaseNotificationRoutes);

// Mount Appwrite notification routes  
router.use('/appwrite', appwriteNotificationRoutes);

/**
 * @swagger
 * /api/notifications/health:
 *   get:
 *     summary: Check notification service health
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Notification services status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 firebase:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       example: "healthy"
 *                 appwrite:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       example: "healthy"
 */
router.get('/health', async (req, res) => {
  try {
    // Basic health check for notification services
    const health = {
      firebase: {
        status: process.env.FIREBASE_SERVICE_ACCOUNT_KEY ? 'configured' : 'not_configured',
      },
      appwrite: {
        status: process.env.APPWRITE_PROJECT_ID ? 'configured' : 'not_configured',
      },
      timestamp: new Date().toISOString(),
    };

    res.json(health);
  } catch (error) {
    console.error('Notification health check failed:', error);
    res.status(500).json({
      success: false,
      error: 'Health check failed',
    });
  }
});

export { router as notificationRoutes };