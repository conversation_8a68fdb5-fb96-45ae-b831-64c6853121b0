import express from 'express';
import { firebaseNotificationService } from '../../services/notificationService';
import { authMiddleware } from '../../middleware/authMiddleware';
import { 
  NOTIFICATION_TEMPLATES, 
  interpolateTemplate, 
  validateTemplateVariables 
} from '../../services/notificationTemplates';

const router = express.Router();

/**
 * @swagger
 * /api/notifications/firebase/send:
 *   post:
 *     summary: Send manual notification via Firebase
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fcmToken
 *               - title
 *               - body
 *             properties:
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional data payload
 *     responses:
 *       200:
 *         description: Notification sent successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to send notification
 */
router.post('/send', authMiddleware, async (req, res) => {
  try {
    const { fcmToken, title, body, data } = req.body;

    if (!fcmToken || !title || !body) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: fcmToken, title, body',
      });
    }

    const result = await firebaseNotificationService.sendNotification(fcmToken, {
      title,
      body,
      data,
    });

    if (result.success) {
      res.json({
        success: true,
        messageId: result.messageId,
        message: 'Notification sent successfully',
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Failed to send Firebase notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send notification',
    });
  }
});

/**
 * @swagger
 * /api/notifications/firebase/send-batch:
 *   post:
 *     summary: Send batch notifications via Firebase
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fcmTokens
 *               - title
 *               - body
 *             properties:
 *               fcmTokens:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of Firebase Cloud Messaging tokens
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional data payload
 *     responses:
 *       200:
 *         description: Batch notifications sent
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to send notifications
 */
router.post('/send-batch', authMiddleware, async (req, res) => {
  try {
    const { fcmTokens, title, body, data } = req.body;

    if (!fcmTokens || !Array.isArray(fcmTokens) || !title || !body) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: fcmTokens (array), title, body',
      });
    }

    const result = await firebaseNotificationService.sendBatchNotifications(fcmTokens, {
      title,
      body,
      data,
    });

    res.json({
      success: true,
      successCount: result.successCount,
      failureCount: result.failureCount,
      totalSent: fcmTokens.length,
      message: 'Batch notifications processed',
    });
  } catch (error) {
    console.error('Failed to send Firebase batch notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send batch notifications',
    });
  }
});

/**
 * @swagger
 * /api/notifications/firebase/send-template:
 *   post:
 *     summary: Send notification using template via Firebase
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fcmToken
 *               - templateId
 *               - variables
 *             properties:
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token
 *               templateId:
 *                 type: string
 *                 description: Notification template ID
 *               variables:
 *                 type: object
 *                 description: Template variables
 *     responses:
 *       200:
 *         description: Template notification sent successfully
 *       400:
 *         description: Invalid template or missing variables
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to send notification
 */
router.post('/send-template', authMiddleware, async (req, res) => {
  try {
    const { fcmToken, templateId, variables } = req.body;

    if (!fcmToken || !templateId || !variables) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: fcmToken, templateId, variables',
      });
    }

    const template = NOTIFICATION_TEMPLATES[templateId];
    if (!template) {
      return res.status(400).json({
        success: false,
        error: `Template not found: ${templateId}`,
      });
    }

    // Validate template variables
    const validation = validateTemplateVariables(template, variables);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Missing template variables',
        missingVariables: validation.missingVariables,
      });
    }

    // Interpolate template
    const { title, body, data } = interpolateTemplate(template, variables);

    const result = await firebaseNotificationService.sendNotification(fcmToken, {
      title,
      body,
      data,
    });

    if (result.success) {
      res.json({
        success: true,
        messageId: result.messageId,
        template: templateId,
        message: 'Template notification sent successfully',
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Failed to send Firebase template notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send template notification',
    });
  }
});

/**
 * @swagger
 * /api/notifications/firebase/history:
 *   get:
 *     summary: Get notification history for user
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of notifications to retrieve
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for filtering
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for filtering
 *     responses:
 *       200:
 *         description: Notification history retrieved
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to retrieve history
 */
router.get('/history', authMiddleware, async (req, res) => {
  try {
    const { limit = 50, startDate, endDate } = req.query;
    
    // Note: This would typically query Firestore for user's notification history
    // For now, we'll return a placeholder response
    const history = {
      notifications: [],
      total: 0,
      limit: parseInt(limit as string),
      message: 'Notification history feature coming soon',
    };

    res.json({
      success: true,
      data: history,
    });
  } catch (error) {
    console.error('Failed to get Firebase notification history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve notification history',
    });
  }
});

/**
 * @swagger
 * /api/notifications/firebase/stats:
 *   get:
 *     summary: Get notification delivery statistics
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for statistics
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for statistics
 *     responses:
 *       200:
 *         description: Notification statistics retrieved
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to retrieve statistics
 */
router.get('/stats', authMiddleware, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const start = startDate ? new Date(startDate as string) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate as string) : new Date();

    const stats = await firebaseNotificationService.getNotificationStats(start, end);

    res.json({
      success: true,
      data: {
        ...stats,
        period: {
          startDate: start.toISOString(),
          endDate: end.toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('Failed to get Firebase notification stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve notification statistics',
    });
  }
});

/**
 * @swagger
 * /api/notifications/firebase/templates:
 *   get:
 *     summary: Get available notification templates
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Available templates retrieved
 */
router.get('/templates', (req, res) => {
  try {
    const templates = Object.entries(NOTIFICATION_TEMPLATES).map(([key, template]) => ({
      id: key,
      name: template.name,
      title: template.title,
      body: template.body,
    }));

    res.json({
      success: true,
      data: templates,
    });
  } catch (error) {
    console.error('Failed to get notification templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve templates',
    });
  }
});

export { router as firebaseNotificationRoutes };