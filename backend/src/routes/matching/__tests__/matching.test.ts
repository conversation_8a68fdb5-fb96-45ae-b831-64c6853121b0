/**
 * Integration tests for matching API endpoints
 */

import request from 'supertest';
import app from '../../../server';
import { BloodType, UrgencyLevel } from '../../../types/enums';

// Mock the services to avoid actual database calls during testing
jest.mock('../../../services/firebaseService');
jest.mock('../../../services/appwriteService');
jest.mock('../../../middleware/firebaseAuth');
jest.mock('../../../middleware/authMiddleware');

import { firebaseService } from '../../../services/firebaseService';
import { appwriteService } from '../../../services/appwriteService';

const mockFirebaseService = firebaseService as jest.Mocked<typeof firebaseService>;
const mockAppwriteService = appwriteService as jest.Mocked<typeof appwriteService>;

// Mock middleware to simulate authenticated requests
jest.mock('../../../middleware/firebaseAuth', () => ({
  firebaseAuthMiddleware: (req: any, res: any, next: any) => {
    req.user = { uid: 'test-user-id' };
    next();
  }
}));

jest.mock('../../../middleware/authMiddleware', () => ({
  authMiddleware: (req: any, res: any, next: any) => {
    req.user = { $id: 'test-user-id' };
    next();
  }
}));

describe('Matching API Endpoints', () => {
  const mockDonors = [
    {
      id: 'donor1',
      phoneNumber: '+1234567890',
      profile: {
        name: 'John Doe',
        bloodType: BloodType.O_NEGATIVE,
        location: {
          coordinates: { latitude: 40.7128, longitude: -74.0060 },
          address: 'New York, NY'
        },
        isAvailable: true,
        userType: 'donor',
        joinedAt: '2024-01-01T00:00:00Z',
        lastActive: new Date().toISOString()
      },
      donorStats: {
        totalDonations: 5,
        rating: 4.8,
        reviewCount: 10,
        badges: ['lifesaver']
      },
      notificationSettings: {
        pushEnabled: true,
        maxDistance: 25,
        urgencyLevels: ['critical', 'urgent']
      }
    },
    {
      id: 'donor2',
      phoneNumber: '+1234567891',
      profile: {
        name: 'Jane Smith',
        bloodType: BloodType.A_POSITIVE,
        location: {
          coordinates: { latitude: 40.7589, longitude: -73.9851 },
          address: 'Manhattan, NY'
        },
        isAvailable: true,
        userType: 'donor',
        joinedAt: '2024-01-01T00:00:00Z',
        lastActive: new Date().toISOString()
      },
      donorStats: {
        totalDonations: 3,
        rating: 4.5,
        reviewCount: 6,
        badges: []
      },
      notificationSettings: {
        pushEnabled: true,
        maxDistance: 25,
        urgencyLevels: ['critical', 'urgent', 'normal']
      }
    }
  ];

  const mockBloodRequest = {
    id: 'req1',
    requesterId: 'test-user-id',
    bloodType: BloodType.A_POSITIVE,
    quantity: '2 units',
    urgency: UrgencyLevel.URGENT,
    location: {
      coordinates: { latitude: 40.7128, longitude: -74.0060 },
      address: 'New York, NY'
    },
    contactInfo: {
      name: 'Test User',
      phone: '+1234567890',
      preferredContact: 'call'
    },
    status: 'active',
    createdAt: new Date().toISOString(),
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    responses: [],
    notifiedDonors: []
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockFirebaseService.getAllDonors.mockResolvedValue(mockDonors);
    mockAppwriteService.getAllDonors.mockResolvedValue(mockDonors);
    mockFirebaseService.getBloodRequest.mockResolvedValue(mockBloodRequest);
    mockAppwriteService.getBloodRequest.mockResolvedValue(mockBloodRequest);
  });

  describe('POST /api/matching/firebase/find-donors', () => {
    const validRequestBody = {
      bloodType: BloodType.A_POSITIVE,
      location: { latitude: 40.7128, longitude: -74.0060 },
      urgency: UrgencyLevel.URGENT
    };

    it('should find compatible donors successfully', async () => {
      const response = await request(app)
        .post('/api/matching/firebase/find-donors')
        .send(validRequestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('matches');
      expect(response.body.data).toHaveProperty('stats');
      expect(response.body.data).toHaveProperty('searchParams');
      expect(Array.isArray(response.body.data.matches)).toBe(true);
    });

    it('should return validation error for missing blood type', async () => {
      const invalidBody = { ...validRequestBody };
      delete (invalidBody as any).bloodType;

      const response = await request(app)
        .post('/api/matching/firebase/find-donors')
        .send(invalidBody)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.message).toContain('Blood type, location, and urgency are required');
    });

    it('should return validation error for invalid blood type', async () => {
      const invalidBody = { ...validRequestBody, bloodType: 'INVALID' };

      const response = await request(app)
        .post('/api/matching/firebase/find-donors')
        .send(invalidBody)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.message).toBe('Invalid blood type');
    });

    it('should return validation error for invalid coordinates', async () => {
      const invalidBody = { 
        ...validRequestBody, 
        location: { latitude: 200, longitude: -74.0060 } // Invalid latitude
      };

      const response = await request(app)
        .post('/api/matching/firebase/find-donors')
        .send(invalidBody)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.message).toBe('Invalid location coordinates');
    });

    it('should return validation error for invalid urgency', async () => {
      const invalidBody = { ...validRequestBody, urgency: 'INVALID' };

      const response = await request(app)
        .post('/api/matching/firebase/find-donors')
        .send(invalidBody)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.message).toBe('Invalid urgency level');
    });

    it('should handle service errors gracefully', async () => {
      mockFirebaseService.getAllDonors.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .post('/api/matching/firebase/find-donors')
        .send(validRequestBody)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INTERNAL_ERROR');
    });
  });

  describe('GET /api/matching/firebase/:requestId', () => {
    it('should get matches for a specific request successfully', async () => {
      const response = await request(app)
        .get('/api/matching/firebase/req1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('request');
      expect(response.body.data).toHaveProperty('matches');
      expect(response.body.data).toHaveProperty('stats');
      expect(response.body.data.request.id).toBe('req1');
    });

    it('should return 404 for non-existent request', async () => {
      mockFirebaseService.getBloodRequest.mockResolvedValue(null);

      const response = await request(app)
        .get('/api/matching/firebase/non-existent')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('NOT_FOUND');
    });

    it('should return 403 for unauthorized access', async () => {
      const unauthorizedRequest = { ...mockBloodRequest, requesterId: 'different-user' };
      mockFirebaseService.getBloodRequest.mockResolvedValue(unauthorizedRequest);

      const response = await request(app)
        .get('/api/matching/firebase/req1')
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('FORBIDDEN');
    });
  });

  describe('POST /api/matching/appwrite/find-donors', () => {
    const validRequestBody = {
      bloodType: BloodType.A_POSITIVE,
      location: { latitude: 40.7128, longitude: -74.0060 },
      urgency: UrgencyLevel.URGENT
    };

    it('should find compatible donors successfully', async () => {
      const response = await request(app)
        .post('/api/matching/appwrite/find-donors')
        .send(validRequestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('matches');
      expect(response.body.data).toHaveProperty('stats');
      expect(response.body.data).toHaveProperty('searchParams');
      expect(Array.isArray(response.body.data.matches)).toBe(true);
    });

    it('should return validation error for missing required fields', async () => {
      const invalidBody = { bloodType: BloodType.A_POSITIVE };

      const response = await request(app)
        .post('/api/matching/appwrite/find-donors')
        .send(invalidBody)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /api/matching/appwrite/:requestId', () => {
    it('should get matches for a specific request successfully', async () => {
      const response = await request(app)
        .get('/api/matching/appwrite/req1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('request');
      expect(response.body.data).toHaveProperty('matches');
      expect(response.body.data).toHaveProperty('stats');
    });

    it('should handle service errors gracefully', async () => {
      mockAppwriteService.getBloodRequest.mockRejectedValue(new Error('Service error'));

      const response = await request(app)
        .get('/api/matching/appwrite/req1')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INTERNAL_ERROR');
    });
  });
});