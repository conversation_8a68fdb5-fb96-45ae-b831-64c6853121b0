/**
 * Appwrite matching routes
 */

import { Router, Request, Response } from 'express';
import { geoMatchingService, GeoMatchingParams } from '../../services/geoMatchingService';
import { appwriteService } from '../../services/appwriteService';
import { authenticateToken } from '../../middleware/authMiddleware';
import { BloodType, UrgencyLevel } from '../../types/enums';
import { validateBloodType } from '../../types/bloodCompatibility';
import { validateCoordinates } from '../../types/location';

const router = Router();

/**
 * @swagger
 * /api/matching/appwrite/find-donors:
 *   post:
 *     summary: Find compatible donors for a blood request (Appwrite)
 *     tags: [Matching]
 *     security:
 *       - AppwriteAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bloodType
 *               - location
 *               - urgency
 *             properties:
 *               bloodType:
 *                 type: string
 *                 enum: [A+, A-, B+, B-, AB+, AB-, O+, O-]
 *               location:
 *                 type: object
 *                 properties:
 *                   latitude:
 *                     type: number
 *                   longitude:
 *                     type: number
 *               urgency:
 *                 type: string
 *                 enum: [critical, urgent, normal]
 *               maxRadius:
 *                 type: number
 *                 description: Maximum search radius in kilometers
 *               maxResults:
 *                 type: number
 *                 description: Maximum number of results to return
 *     responses:
 *       200:
 *         description: List of compatible donors found
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 */
router.post('/find-donors', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { bloodType, location, urgency, maxRadius, maxResults } = req.body;

    // Validate required fields
    if (!bloodType || !location || !urgency) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Blood type, location, and urgency are required',
          details: {
            required: ['bloodType', 'location', 'urgency']
          }
        }
      });
      return;
    }

    // Validate blood type
    if (!validateBloodType(bloodType)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid blood type',
          details: {
            field: 'bloodType',
            value: bloodType,
            allowedValues: Object.values(BloodType)
          }
        }
      });
      return;
    }

    // Validate location coordinates
    if (!location.latitude || !location.longitude || !validateCoordinates(location)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid location coordinates',
          details: {
            field: 'location',
            requirements: 'latitude and longitude must be valid numbers'
          }
        }
      });
      return;
    }

    // Validate urgency
    if (!Object.values(UrgencyLevel).includes(urgency)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid urgency level',
          details: {
            field: 'urgency',
            value: urgency,
            allowedValues: Object.values(UrgencyLevel)
          }
        }
      });
      return;
    }

    // Get all available donors from Appwrite
    const allDonors = await appwriteService.getAllDonors();
    
    // Convert Appwrite documents to User type and filter eligible donors
    const users = allDonors.map(donor => ({
      id: donor.$id,
      phoneNumber: donor.phoneNumber,
      profile: donor.profile || {
        name: donor.name,
        bloodType: donor.bloodType,
        location: donor.location,
        isAvailable: donor.isAvailable,
        userType: donor.userType,
        joinedAt: donor.createdAt,
        lastActive: donor.updatedAt
      },
      donorStats: donor.donorStats,
      notificationSettings: donor.notificationSettings || {
        pushEnabled: true,
        maxDistance: 25,
        urgencyLevels: ['critical', 'urgent', 'normal']
      }
    }));
    const eligibleDonors = geoMatchingService.filterEligibleDonors(users);

    // Prepare matching parameters
    const matchingParams: GeoMatchingParams = {
      bloodType: bloodType as BloodType,
      location: {
        latitude: location.latitude,
        longitude: location.longitude
      },
      urgency: urgency as UrgencyLevel,
      maxRadius,
      maxResults
    };

    // Find matching donors
    const matches = await geoMatchingService.findNearbyDonors(matchingParams, eligibleDonors);
    
    // Get matching statistics
    const stats = geoMatchingService.getMatchingStats(matches);

    // Format response data (remove sensitive information)
    const formattedMatches = matches.map(match => ({
      donorId: match.donor.id,
      name: match.donor.profile.name,
      bloodType: match.donor.profile.bloodType,
      distance: match.distance,
      compatibility: match.compatibility,
      rating: match.rating,
      totalDonations: match.donor.donorStats?.totalDonations || 0,
      lastActive: match.lastActive,
      responseTime: match.responseTime
    }));

    res.json({
      success: true,
      data: {
        matches: formattedMatches,
        stats,
        searchParams: {
          bloodType,
          urgency,
          radius: matchingParams.maxRadius || (urgency === 'critical' ? 50 : 25),
          maxResults: matchingParams.maxResults || 50
        }
      }
    });

  } catch (error) {
    console.error('Error finding donors:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to find donors',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      }
    });
  }
});

/**
 * @swagger
 * /api/matching/appwrite/{requestId}:
 *   get:
 *     summary: Get donor matches for a specific blood request (Appwrite)
 *     tags: [Matching]
 *     security:
 *       - AppwriteAuth: []
 *     parameters:
 *       - in: path
 *         name: requestId
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     responses:
 *       200:
 *         description: Donor matches for the request
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 */
router.get('/:requestId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { requestId } = req.params;
    const userId = (req as any).user.$id;

    // Get the blood request
    const bloodRequest = await appwriteService.getBloodRequest(requestId);
    
    if (!bloodRequest) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found',
          details: { requestId }
        }
      });
      return;
    }

    // Check if user is authorized to view this request
    if (bloodRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Not authorized to view this request',
          details: { requestId }
        }
      });
      return;
    }

    // Get all available donors
    const allDonors = await appwriteService.getAllDonors();
    
    // Convert Appwrite documents to User type
    const users = allDonors.map(donor => ({
      id: donor.$id,
      phoneNumber: donor.phoneNumber,
      profile: donor.profile || {
        name: donor.name,
        bloodType: donor.bloodType,
        location: donor.location,
        isAvailable: donor.isAvailable,
        userType: donor.userType,
        joinedAt: donor.createdAt,
        lastActive: donor.updatedAt
      },
      donorStats: donor.donorStats,
      notificationSettings: donor.notificationSettings || {
        pushEnabled: true,
        maxDistance: 25,
        urgencyLevels: ['critical', 'urgent', 'normal']
      }
    }));
    
    // Convert Appwrite document to BloodRequest type
    const request = {
      id: bloodRequest.$id,
      requesterId: bloodRequest.requesterId,
      bloodType: bloodRequest.bloodType,
      quantity: bloodRequest.quantity,
      urgency: bloodRequest.urgency,
      location: bloodRequest.location,
      contactInfo: bloodRequest.contactInfo,
      description: bloodRequest.description,
      status: bloodRequest.status,
      createdAt: bloodRequest.createdAt,
      expiresAt: bloodRequest.expiresAt,
      responses: bloodRequest.responses || [],
      notifiedDonors: bloodRequest.notifiedDonors || [],
      fulfillmentDetails: bloodRequest.fulfillmentDetails
    };
    
    // Find matches for this request
    const matches = await geoMatchingService.findDonorsForRequest(request, users);
    
    // Get matching statistics
    const stats = geoMatchingService.getMatchingStats(matches);

    // Format response data
    const formattedMatches = matches.map(match => ({
      donorId: match.donor.id,
      name: match.donor.profile.name,
      bloodType: match.donor.profile.bloodType,
      distance: match.distance,
      compatibility: match.compatibility,
      rating: match.rating,
      totalDonations: match.donor.donorStats?.totalDonations || 0,
      lastActive: match.lastActive,
      responseTime: match.responseTime,
      hasResponded: bloodRequest.responses.some((r: any) => r.donorId === match.donor.id)
    }));

    res.json({
      success: true,
      data: {
        request: {
          id: bloodRequest.id,
          bloodType: bloodRequest.bloodType,
          urgency: bloodRequest.urgency,
          status: bloodRequest.status,
          createdAt: bloodRequest.createdAt,
          location: bloodRequest.location.address
        },
        matches: formattedMatches,
        stats,
        responseCount: bloodRequest.responses.length,
        notifiedCount: bloodRequest.notifiedDonors.length
      }
    });

  } catch (error) {
    console.error('Error getting request matches:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to get request matches',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      }
    });
  }
});

export { router as appwriteMatchingRoutes };