/**
 * Matching routes - handles donor-recipient matching
 */

import { Router } from 'express';
import { firebaseMatchingRoutes } from './firebase';
import { appwriteMatchingRoutes } from './appwrite';

const router = Router();

// Firebase matching routes
router.use('/firebase', firebaseMatchingRoutes);

// Appwrite matching routes  
router.use('/appwrite', appwriteMatchingRoutes);

export { router as matchingRoutes };