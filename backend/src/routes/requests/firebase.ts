import { Router, Request, Response } from 'express';
import { getFirestore, FieldValue, Query } from 'firebase-admin/firestore';
import { verifyFirebaseToken } from '../../middleware/firebaseAuth';
import { 
  BloodRequest, 
  CreateBloodRequestData, 
  UpdateBloodRequestData,
  DonorResponse,
  FulfillmentDetails,
  validateCreateBloodRequestData,
  createBloodRequest,
  isRequestActive
} from '../../models/BloodRequest';
import { User } from '../../models/User';
import { RequestStatus, UserType } from '../../types/enums';
import { matchingNotificationService } from '../../services/matchingNotificationService';

const router = Router();
const db = getFirestore();

// Apply Firebase auth middleware to all routes
router.use(verifyFirebaseToken);

/**
 * @swagger
 * /api/requests/firebase:
 *   post:
 *     summary: Create a new blood request (Firebase)
 *     description: Create a new blood request using Firebase Firestore
 *     tags: [Blood Requests - Firebase]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateBloodRequestData'
 *           example:
 *             bloodType: "O+"
 *             quantity: "2 units"
 *             urgency: "urgent"
 *             location:
 *               latitude: 40.7128
 *               longitude: -74.0060
 *               address: "123 Main St, New York, NY 10001"
 *               city: "New York"
 *               state: "NY"
 *               country: "USA"
 *               postalCode: "10001"
 *             contactInfo:
 *               name: "John Doe"
 *               phone: "+1234567890"
 *               preferredContact: "call"
 *             description: "Urgent blood needed for surgery"
 *     responses:
 *       201:
 *         description: Blood request created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BloodRequest'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const requestData: CreateBloodRequestData = {
      requesterId: req.user?.uid || '',
      bloodType: req.body.bloodType,
      quantity: req.body.quantity,
      urgency: req.body.urgency,
      location: req.body.location,
      contactInfo: req.body.contactInfo,
      description: req.body.description
    };

    // Validate request data
    const validation = validateCreateBloodRequestData(requestData);
    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid blood request data',
          details: validation.errors
        }
      });
    }

    // Create blood request object
    const bloodRequest = createBloodRequest(requestData);

    // Save to Firestore
    const docRef = await db.collection('blood_requests').add({
      ...bloodRequest,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp()
    });

    // Update the request with the generated ID
    await docRef.update({ id: docRef.id });

    // Fetch the created request
    const createdDoc = await docRef.get();
    const createdRequest = { id: docRef.id, ...createdDoc.data() } as BloodRequest;

    // Trigger automatic donor matching and notifications
    try {
      // Get available donors from database
      const donorsSnapshot = await db.collection('users')
        .where('profile.userType', 'in', [UserType.DONOR, UserType.BOTH])
        .where('profile.isAvailable', '==', true)
        .get();

      const availableDonors: User[] = [];
      donorsSnapshot.forEach(doc => {
        const userData = doc.data();
        availableDonors.push({
          id: doc.id,
          phoneNumber: userData.phoneNumber,
          profile: userData.profile,
          donorStats: userData.donorStats,
          notificationSettings: userData.notificationSettings
        } as User);
      });

      // Process blood request with matching and notifications
      const matchingResult = await matchingNotificationService.processBloodRequest(
        createdRequest,
        availableDonors,
        {
          backend: 'firebase',
          expandRadius: createdRequest.urgency === 'critical',
          maxRetries: 2
        }
      );

      console.log(`Blood request ${createdRequest.id} processed: ${matchingResult.matchedDonors} donors matched, ${matchingResult.notificationsSent} notifications sent`);

    } catch (matchingError) {
      // Log error but don't fail the request creation
      console.error('Error in automatic donor matching and notifications:', matchingError);
    }

    res.status(201).json({
      success: true,
      data: createdRequest,
      message: 'Blood request created successfully'
    });

  } catch (error) {
    console.error('Error creating blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/firebase:
 *   get:
 *     summary: Get blood requests with filtering and pagination (Firebase)
 *     description: Retrieve blood requests from Firebase Firestore with optional filtering and pagination
 *     tags: [Blood Requests - Firebase]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, fulfilled, cancelled, expired]
 *         description: Filter by request status
 *       - in: query
 *         name: bloodType
 *         schema:
 *           type: string
 *           enum: [A+, A-, B+, B-, AB+, AB-, O+, O-]
 *         description: Filter by blood type
 *       - in: query
 *         name: urgency
 *         schema:
 *           type: string
 *           enum: [critical, urgent, normal]
 *         description: Filter by urgency level
 *       - in: query
 *         name: requesterId
 *         schema:
 *           type: string
 *         description: Filter by requester ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of results to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of results to skip
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: createdAt
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Blood requests retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginationResponse'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      status,
      bloodType,
      urgency,
      requesterId,
      limit = '10',
      offset = '0',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    let query: Query = db.collection('blood_requests');

    // Apply filters
    if (status) {
      query = query.where('status', '==', status);
    }
    if (bloodType) {
      query = query.where('bloodType', '==', bloodType);
    }
    if (urgency) {
      query = query.where('urgency', '==', urgency);
    }
    if (requesterId) {
      query = query.where('requesterId', '==', requesterId);
    }

    // Apply sorting
    const sortDirection = sortOrder === 'asc' ? 'asc' : 'desc';
    query = query.orderBy(sortBy as string, sortDirection);

    // Apply pagination
    const limitNum = Math.min(parseInt(limit as string) || 10, 50); // Max 50 items
    const offsetNum = parseInt(offset as string) || 0;
    
    query = query.limit(limitNum).offset(offsetNum);

    // Execute query
    const snapshot = await query.get();
    const requests: BloodRequest[] = [];

    snapshot.forEach(doc => {
      const data = doc.data();
      requests.push({
        id: doc.id,
        requesterId: data.requesterId,
        bloodType: data.bloodType,
        quantity: data.quantity,
        urgency: data.urgency,
        location: data.location,
        contactInfo: data.contactInfo,
        description: data.description,
        status: data.status,
        responses: data.responses || [],
        notifiedDonors: data.notifiedDonors || [],
        fulfillmentDetails: data.fulfillmentDetails,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        expiresAt: data.expiresAt?.toDate?.()?.toISOString() || data.expiresAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
      } as BloodRequest);
    });

    // Get total count for pagination
    const countQuery = db.collection('blood_requests');
    const countSnapshot = await countQuery.count().get();
    const totalCount = countSnapshot.data().count;

    res.status(200).json({
      success: true,
      data: requests,
      pagination: {
        total: totalCount,
        limit: limitNum,
        offset: offsetNum,
        hasMore: offsetNum + limitNum < totalCount
      }
    });

  } catch (error) {
    console.error('Error fetching blood requests:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch blood requests'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/firebase/{id}:
 *   get:
 *     summary: Get a specific blood request by ID (Firebase)
 *     description: Retrieve a specific blood request from Firebase Firestore
 *     tags: [Blood Requests - Firebase]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     responses:
 *       200:
 *         description: Blood request retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BloodRequest'
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const doc = await db.collection('blood_requests').doc(id).get();
    
    if (!doc.exists) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    const data = doc.data();
    const request: BloodRequest = {
      id: doc.id,
      requesterId: data?.requesterId,
      bloodType: data?.bloodType,
      quantity: data?.quantity,
      urgency: data?.urgency,
      location: data?.location,
      contactInfo: data?.contactInfo,
      description: data?.description,
      status: data?.status,
      responses: data?.responses || [],
      notifiedDonors: data?.notifiedDonors || [],
      fulfillmentDetails: data?.fulfillmentDetails,
      createdAt: data?.createdAt?.toDate?.()?.toISOString() || data?.createdAt,
      expiresAt: data?.expiresAt?.toDate?.()?.toISOString() || data?.expiresAt
    };

    res.status(200).json({
      success: true,
      data: request
    });

  } catch (error) {
    console.error('Error fetching blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/firebase/{id}:
 *   put:
 *     summary: Update a blood request (Firebase)
 *     description: Update an existing blood request in Firebase Firestore (only by the requester)
 *     tags: [Blood Requests - Firebase]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateBloodRequestData'
 *           example:
 *             quantity: "3 units"
 *             urgency: "critical"
 *             description: "Updated: Very urgent blood needed for emergency surgery"
 *     responses:
 *       200:
 *         description: Blood request updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BloodRequest'
 *       400:
 *         description: Request inactive or validation error
 *       403:
 *         description: Forbidden - not the request owner
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.uid;

    // Get existing request
    const doc = await db.collection('blood_requests').doc(id).get();
    
    if (!doc.exists) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    const existingRequest = doc.data() as BloodRequest;

    // Check if user owns this request
    if (existingRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only update your own blood requests'
        }
      });
    }

    // Check if request is still active
    if (!isRequestActive(existingRequest)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'REQUEST_INACTIVE',
          message: 'Cannot update inactive or expired blood request'
        }
      });
    }

    const updateData: UpdateBloodRequestData = {
      quantity: req.body.quantity,
      urgency: req.body.urgency,
      location: req.body.location,
      contactInfo: req.body.contactInfo,
      description: req.body.description,
      status: req.body.status
    };

    // Remove undefined fields
    const cleanUpdateData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    // Add update timestamp
    const updatePayload = {
      ...cleanUpdateData,
      updatedAt: FieldValue.serverTimestamp()
    };

    // Update in Firestore
    await db.collection('blood_requests').doc(id).update(updatePayload);

    // Fetch updated request
    const updatedDoc = await db.collection('blood_requests').doc(id).get();
    const updatedData = updatedDoc.data();
    const updatedRequest: BloodRequest = {
      id: updatedDoc.id,
      requesterId: updatedData?.requesterId,
      bloodType: updatedData?.bloodType,
      quantity: updatedData?.quantity,
      urgency: updatedData?.urgency,
      location: updatedData?.location,
      contactInfo: updatedData?.contactInfo,
      description: updatedData?.description,
      status: updatedData?.status,
      responses: updatedData?.responses || [],
      notifiedDonors: updatedData?.notifiedDonors || [],
      fulfillmentDetails: updatedData?.fulfillmentDetails,
      createdAt: updatedData?.createdAt?.toDate?.()?.toISOString() || updatedData?.createdAt,
      expiresAt: updatedData?.expiresAt?.toDate?.()?.toISOString() || updatedData?.expiresAt
    };

    res.status(200).json({
      success: true,
      data: updatedRequest,
      message: 'Blood request updated successfully'
    });

  } catch (error) {
    console.error('Error updating blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/firebase/{id}:
 *   delete:
 *     summary: Cancel a blood request (Firebase)
 *     description: Cancel an existing blood request in Firebase Firestore (only by the requester)
 *     tags: [Blood Requests - Firebase]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     responses:
 *       200:
 *         description: Blood request cancelled successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       403:
 *         description: Forbidden - not the request owner
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.delete('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.uid;

    // Get existing request
    const doc = await db.collection('blood_requests').doc(id).get();
    
    if (!doc.exists) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    const existingRequest = doc.data() as BloodRequest;

    // Check if user owns this request
    if (existingRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only cancel your own blood requests'
        }
      });
    }

    // Update status to cancelled instead of deleting
    await db.collection('blood_requests').doc(id).update({
      status: RequestStatus.CANCELLED,
      updatedAt: FieldValue.serverTimestamp(),
      cancelledAt: FieldValue.serverTimestamp()
    });

    // Trigger cancellation notifications to notified donors
    try {
      const notifiedDonors = existingRequest.notifiedDonors || [];
      if (notifiedDonors.length > 0) {
        // Get FCM tokens for notified donors
        const donorTokens: string[] = [];
        for (const donorId of notifiedDonors) {
          const donorDoc = await db.collection('users').doc(donorId).get();
          if (donorDoc.exists) {
            const donorData = donorDoc.data();
            const fcmToken = donorData?.notificationSettings?.fcmToken;
            if (fcmToken) {
              donorTokens.push(fcmToken);
            }
          }
        }

        if (donorTokens.length > 0) {
          await matchingNotificationService.processRequestCancellation(
            existingRequest,
            donorTokens,
            { backend: 'firebase', maxRetries: 2 }
          );
        }
      }
    } catch (notificationError) {
      // Log error but don't fail the cancellation
      console.error('Error sending cancellation notifications:', notificationError);
    }

    res.status(200).json({
      success: true,
      message: 'Blood request cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to cancel blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/firebase/{id}/respond:
 *   post:
 *     summary: Respond to a blood request (Firebase)
 *     description: Allow donors to respond to a blood request with yes/no
 *     tags: [Blood Requests - Firebase]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/DonorResponseRequest'
 *           example:
 *             response: "yes"
 *     responses:
 *       200:
 *         description: Response recorded successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/DonorResponse'
 *       400:
 *         description: Validation error or already responded
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/:id/respond', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { response } = req.body;
    const userId = req.user?.uid;

    // Validate response
    if (!response || !['yes', 'no'].includes(response)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Response must be either "yes" or "no"'
        }
      });
    }

    // Get existing request
    const doc = await db.collection('blood_requests').doc(id).get();
    
    if (!doc.exists) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    const existingRequest = doc.data() as BloodRequest;

    // Check if request is still active
    if (!isRequestActive(existingRequest)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'REQUEST_INACTIVE',
          message: 'Cannot respond to inactive or expired blood request'
        }
      });
    }

    // Check if user already responded
    const existingResponse = existingRequest.responses?.find(r => r.donorId === userId);
    if (existingResponse) {
      res.status(400).json({
        success: false,
        error: {
          code: 'ALREADY_RESPONDED',
          message: 'You have already responded to this blood request'
        }
      });
    }

    // Get donor profile for contact info (if responding yes)
    let donorContact;
    if (response === 'yes') {
      const donorDoc = await db.collection('users').doc(userId!).get();
      if (donorDoc.exists) {
        const donorData = donorDoc.data();
        donorContact = {
          name: donorData?.profile?.name || 'Anonymous Donor',
          phone: donorData?.phoneNumber || ''
        };
      }
    }

    // Create donor response
    const donorResponse: DonorResponse = {
      donorId: userId!,
      response: response as 'yes' | 'no',
      respondedAt: new Date().toISOString(),
      donorContact: response === 'yes' ? donorContact : undefined
    };

    // Update request with new response
    const updatedResponses = [...(existingRequest.responses || []), donorResponse];
    
    await db.collection('blood_requests').doc(id).update({
      responses: updatedResponses,
      updatedAt: FieldValue.serverTimestamp()
    });

    // Trigger notification to recipient about donor response
    try {
      // Get recipient's FCM token
      const requesterDoc = await db.collection('users').doc(existingRequest.requesterId).get();
      if (requesterDoc.exists) {
        const requesterData = requesterDoc.data();
        const recipientFcmToken = requesterData?.notificationSettings?.fcmToken;
        
        if (recipientFcmToken) {
          // Get donor data for notification
          const donorDoc = await db.collection('users').doc(userId!).get();
          if (donorDoc.exists) {
            const donorData = donorDoc.data();
            const donor: User = {
              id: donorDoc.id,
              phoneNumber: donorData?.phoneNumber,
              profile: donorData?.profile,
              donorStats: donorData?.donorStats,
              notificationSettings: donorData?.notificationSettings
            } as User;

            await matchingNotificationService.processDonorResponse(
              id,
              donor,
              response as 'yes' | 'no',
              recipientFcmToken,
              { backend: 'firebase', maxRetries: 2 }
            );
          }
        }
      }
    } catch (notificationError) {
      // Log error but don't fail the response recording
      console.error('Error sending donor response notification:', notificationError);
    }

    res.status(200).json({
      success: true,
      data: donorResponse,
      message: `Response "${response}" recorded successfully`
    });

  } catch (error) {
    console.error('Error responding to blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to respond to blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/firebase/{id}/responses:
 *   get:
 *     summary: Get all responses for a blood request (Firebase)
 *     description: Retrieve all donor responses for a specific blood request (only by requester)
 *     tags: [Blood Requests - Firebase]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     responses:
 *       200:
 *         description: Responses retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         requestId:
 *                           type: string
 *                         totalResponses:
 *                           type: number
 *                         positiveResponses:
 *                           type: number
 *                         responses:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/DonorResponse'
 *       403:
 *         description: Forbidden - not the request owner
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/:id/responses', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.uid;

    // Get existing request
    const doc = await db.collection('blood_requests').doc(id).get();
    
    if (!doc.exists) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    const existingRequest = doc.data() as BloodRequest;

    // Check if user owns this request (only requester can see all responses)
    if (existingRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only view responses to your own blood requests'
        }
      });
    }

    const responses = existingRequest.responses || [];
    
    // Filter to only show positive responses with contact info
    const positiveResponses = responses.filter(r => r.response === 'yes');

    res.status(200).json({
      success: true,
      data: {
        requestId: id,
        totalResponses: responses.length,
        positiveResponses: positiveResponses.length,
        responses: positiveResponses
      }
    });

  } catch (error) {
    console.error('Error fetching blood request responses:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch blood request responses'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/firebase/{id}/fulfill:
 *   put:
 *     summary: Mark a blood request as fulfilled (Firebase)
 *     description: Mark a blood request as completed/fulfilled (only by requester)
 *     tags: [Blood Requests - Firebase]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/FulfillRequest'
 *           example:
 *             fulfilledBy: "donor123"
 *             recipientFeedback:
 *               rating: 5
 *               comment: "Thank you for the quick response and donation!"
 *     responses:
 *       200:
 *         description: Blood request marked as fulfilled successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/FulfillmentDetails'
 *       400:
 *         description: Request not active or invalid donor
 *       403:
 *         description: Forbidden - not the request owner
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/:id/fulfill', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { fulfilledBy, recipientFeedback } = req.body;
    const userId = req.user?.uid;

    // Get existing request
    const doc = await db.collection('blood_requests').doc(id).get();
    
    if (!doc.exists) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    const existingRequest = doc.data() as BloodRequest;

    // Check if user owns this request
    if (existingRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only fulfill your own blood requests'
        }
      });
    }

    // Check if request is still active
    if (existingRequest.status !== RequestStatus.ACTIVE) {
      res.status(400).json({
        success: false,
        error: {
          code: 'REQUEST_NOT_ACTIVE',
          message: 'Only active blood requests can be fulfilled'
        }
      });
    }

    // Validate fulfilledBy donor exists in responses
    if (fulfilledBy) {
      const donorResponse = existingRequest.responses?.find(r => 
        r.donorId === fulfilledBy && r.response === 'yes'
      );
      if (!donorResponse) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_DONOR',
            message: 'Specified donor did not respond positively to this request'
          }
        });
      }
    }

    // Create fulfillment details
    const fulfillmentDetails: FulfillmentDetails = {
      fulfilledAt: new Date().toISOString(),
      fulfilledBy: fulfilledBy || 'unknown',
      recipientFeedback: recipientFeedback ? {
        rating: recipientFeedback.rating,
        comment: recipientFeedback.comment
      } : undefined
    };

    // Update request status and fulfillment details
    await db.collection('blood_requests').doc(id).update({
      status: RequestStatus.FULFILLED,
      fulfillmentDetails,
      updatedAt: FieldValue.serverTimestamp()
    });

    // Trigger fulfillment notifications to other notified donors
    try {
      const notifiedDonors = existingRequest.notifiedDonors || [];
      if (notifiedDonors.length > 0) {
        // Get FCM tokens for notified donors (excluding the fulfilling donor)
        const donorTokens: string[] = [];
        for (const donorId of notifiedDonors) {
          if (donorId !== fulfilledBy) { // Don't notify the donor who fulfilled it
            const donorDoc = await db.collection('users').doc(donorId).get();
            if (donorDoc.exists) {
              const donorData = donorDoc.data();
              const fcmToken = donorData?.notificationSettings?.fcmToken;
              if (fcmToken) {
                donorTokens.push(fcmToken);
              }
            }
          }
        }

        if (donorTokens.length > 0) {
          await matchingNotificationService.processRequestFulfillment(
            existingRequest,
            fulfilledBy || 'unknown',
            donorTokens,
            { backend: 'firebase', maxRetries: 2 }
          );
        }
      }
    } catch (notificationError) {
      // Log error but don't fail the fulfillment
      console.error('Error sending fulfillment notifications:', notificationError);
    }

    res.status(200).json({
      success: true,
      data: fulfillmentDetails,
      message: 'Blood request marked as fulfilled successfully'
    });

  } catch (error) {
    console.error('Error fulfilling blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fulfill blood request'
      }
    });
  }
});

export default router;