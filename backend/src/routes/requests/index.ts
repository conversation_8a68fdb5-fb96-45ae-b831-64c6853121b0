import { Router } from 'express';
import firebaseRequestRoutes from './firebase';
import appwriteRequestRoutes from './appwrite';

const router = Router();

// Mount Firebase blood request routes
router.use('/firebase', firebaseRequestRoutes);

// Mount Appwrite blood request routes  
router.use('/appwrite', appwriteRequestRoutes);

/**
 * @swagger
 * /api/requests/health:
 *   get:
 *     summary: Blood request service health check
 *     description: Check the health status of the blood request service
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Blood request service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Blood request service is running"
 *                 backends:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["firebase", "appwrite"]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Blood request service is running',
    backends: ['firebase', 'appwrite'],
    timestamp: new Date().toISOString()
  });
});

export default router;