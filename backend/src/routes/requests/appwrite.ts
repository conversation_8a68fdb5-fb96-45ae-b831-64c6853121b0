import { Router, Request, Response } from 'express';
import { Query } from 'node-appwrite';
import { authenticateToken } from '../../middleware/authMiddleware';
import { appwriteService } from '../../services/appwriteService';
import { 
  BloodRequest, 
  CreateBloodRequestData, 
  UpdateBloodRequestData,
  DonorResponse,
  FulfillmentDetails,
  validateCreateBloodRequestData,
  createBloodRequest,
  isRequestActive
} from '../../models/BloodRequest';
import { User } from '../../models/User';
import { RequestStatus, UserType } from '../../types/enums';
import { matchingNotificationService } from '../../services/matchingNotificationService';

const router = Router();

// Apply auth middleware to all routes
router.use(authenticateToken);

/**
 * @swagger
 * /api/requests/appwrite:
 *   post:
 *     summary: Create a new blood request (Appwrite)
 *     description: Create a new blood request using Appwrite Database
 *     tags: [Blood Requests - Appwrite]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateBloodRequestData'
 *           example:
 *             bloodType: "A+"
 *             quantity: "1 unit"
 *             urgency: "critical"
 *             location:
 *               latitude: 37.7749
 *               longitude: -122.4194
 *               address: "456 Oak St, San Francisco, CA 94102"
 *               city: "San Francisco"
 *               state: "CA"
 *               country: "USA"
 *               postalCode: "94102"
 *             contactInfo:
 *               name: "Jane Smith"
 *               phone: "+1987654321"
 *               preferredContact: "text"
 *             description: "Critical blood needed for emergency"
 *     responses:
 *       201:
 *         description: Blood request created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BloodRequest'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const requestData: CreateBloodRequestData = {
      requesterId: req.user?.uid || '',
      bloodType: req.body.bloodType,
      quantity: req.body.quantity,
      urgency: req.body.urgency,
      location: req.body.location,
      contactInfo: req.body.contactInfo,
      description: req.body.description
    };

    // Validate request data
    const validation = validateCreateBloodRequestData(requestData);
    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid blood request data',
          details: validation.errors
        }
      });
    }

    // Create blood request object
    const bloodRequest = createBloodRequest(requestData);

    // Save to Appwrite Database
    const requestId = await appwriteService.createBloodRequest({
      ...bloodRequest,
      id: undefined // Let Appwrite generate the ID
    });

    // Fetch the created request
    const createdRequest = await appwriteService.getBloodRequestById(requestId);
    const fullCreatedRequest: BloodRequest = {
      id: requestId,
      ...createdRequest
    } as BloodRequest;

    // Trigger automatic donor matching and notifications
    try {
      // Get available donors from Appwrite database
      const { getDatabases, APPWRITE_DATABASE_ID, COLLECTIONS } = await import('../../config/appwrite');
      const databases = getDatabases();
      
      const donorsResult = await databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        [
          Query.equal('profile.userType', [UserType.DONOR, UserType.BOTH]),
          Query.equal('profile.isAvailable', true)
        ]
      );

      const availableDonors: User[] = donorsResult.documents.map((doc: any) => ({
        id: doc.$id,
        phoneNumber: doc.phoneNumber,
        profile: doc.profile,
        donorStats: doc.donorStats,
        notificationSettings: doc.notificationSettings
      } as User));

      // Process blood request with matching and notifications
      const matchingResult = await matchingNotificationService.processBloodRequest(
        fullCreatedRequest,
        availableDonors,
        {
          backend: 'appwrite',
          expandRadius: fullCreatedRequest.urgency === 'critical',
          maxRetries: 2
        }
      );

      console.log(`Blood request ${requestId} processed: ${matchingResult.matchedDonors} donors matched, ${matchingResult.notificationsSent} notifications sent`);

    } catch (matchingError) {
      // Log error but don't fail the request creation
      console.error('Error in automatic donor matching and notifications:', matchingError);
    }

    res.status(201).json({
      success: true,
      data: fullCreatedRequest,
      message: 'Blood request created successfully'
    });

  } catch (error) {
    console.error('Error creating blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/appwrite:
 *   get:
 *     summary: Get blood requests with filtering and pagination (Appwrite)
 *     description: Retrieve blood requests from Appwrite Database with optional filtering and pagination
 *     tags: [Blood Requests - Appwrite]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, fulfilled, cancelled, expired]
 *         description: Filter by request status
 *       - in: query
 *         name: bloodType
 *         schema:
 *           type: string
 *           enum: [A+, A-, B+, B-, AB+, AB-, O+, O-]
 *         description: Filter by blood type
 *       - in: query
 *         name: urgency
 *         schema:
 *           type: string
 *           enum: [critical, urgent, normal]
 *         description: Filter by urgency level
 *       - in: query
 *         name: requesterId
 *         schema:
 *           type: string
 *         description: Filter by requester ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of results to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of results to skip
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: createdAt
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Blood requests retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginationResponse'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      status,
      bloodType,
      urgency,
      requesterId,
      limit = '10',
      offset = '0',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query filters
    const queries: string[] = [];

    if (status) {
      queries.push(Query.equal('status', status as string));
    }
    if (bloodType) {
      queries.push(Query.equal('bloodType', bloodType as string));
    }
    if (urgency) {
      queries.push(Query.equal('urgency', urgency as string));
    }
    if (requesterId) {
      queries.push(Query.equal('requesterId', requesterId as string));
    }

    // Apply sorting
    if (sortOrder === 'asc') {
      queries.push(Query.orderAsc(sortBy as string));
    } else {
      queries.push(Query.orderDesc(sortBy as string));
    }

    // Apply pagination
    const limitNum = Math.min(parseInt(limit as string) || 10, 50); // Max 50 items
    const offsetNum = parseInt(offset as string) || 0;
    
    queries.push(Query.limit(limitNum));
    queries.push(Query.offset(offsetNum));

    // Execute query using Appwrite service
    const { getDatabases, APPWRITE_DATABASE_ID, COLLECTIONS } = await import('../../config/appwrite');
    const databases = getDatabases();
    
    const result = await databases.listDocuments(
      APPWRITE_DATABASE_ID,
      COLLECTIONS.BLOOD_REQUESTS,
      queries
    );

    const requests: BloodRequest[] = result.documents.map((doc: any) => ({
      id: doc.$id,
      requesterId: doc.requesterId,
      bloodType: doc.bloodType,
      quantity: doc.quantity,
      urgency: doc.urgency,
      location: doc.location,
      contactInfo: doc.contactInfo,
      description: doc.description,
      status: doc.status,
      createdAt: doc.createdAt,
      expiresAt: doc.expiresAt,
      responses: doc.responses || [],
      notifiedDonors: doc.notifiedDonors || [],
      fulfillmentDetails: doc.fulfillmentDetails,
      updatedAt: doc.updatedAt
    }));

    res.status(200).json({
      success: true,
      data: requests,
      pagination: {
        total: result.total,
        limit: limitNum,
        offset: offsetNum,
        hasMore: offsetNum + limitNum < result.total
      }
    });

  } catch (error) {
    console.error('Error fetching blood requests:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch blood requests'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/appwrite/{id}:
 *   get:
 *     summary: Get a specific blood request by ID (Appwrite)
 *     description: Retrieve a specific blood request from Appwrite Database
 *     tags: [Blood Requests - Appwrite]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     responses:
 *       200:
 *         description: Blood request retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BloodRequest'
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const requestDoc = await appwriteService.getBloodRequestById(id);
    
    if (!requestDoc) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    const request: BloodRequest = {
      id: requestDoc.$id,
      requesterId: requestDoc.requesterId,
      bloodType: requestDoc.bloodType,
      quantity: requestDoc.quantity,
      urgency: requestDoc.urgency,
      location: requestDoc.location,
      contactInfo: requestDoc.contactInfo,
      description: requestDoc.description,
      status: requestDoc.status,
      createdAt: requestDoc.createdAt,
      expiresAt: requestDoc.expiresAt,
      responses: requestDoc.responses || [],
      notifiedDonors: requestDoc.notifiedDonors || [],
      fulfillmentDetails: requestDoc.fulfillmentDetails,
      updatedAt: requestDoc.updatedAt
    };

    res.status(200).json({
      success: true,
      data: request
    });

  } catch (error) {
    console.error('Error fetching blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/appwrite/{id}:
 *   put:
 *     summary: Update a blood request (Appwrite)
 *     description: Update an existing blood request in Appwrite Database (only by the requester)
 *     tags: [Blood Requests - Appwrite]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateBloodRequestData'
 *           example:
 *             quantity: "2 units"
 *             urgency: "critical"
 *             description: "Updated: Very urgent blood needed for emergency surgery"
 *     responses:
 *       200:
 *         description: Blood request updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BloodRequest'
 *       400:
 *         description: Request inactive or validation error
 *       403:
 *         description: Forbidden - not the request owner
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.uid;

    // Get existing request
    const existingRequest = await appwriteService.getBloodRequestById(id);
    
    if (!existingRequest) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    // Check if user owns this request
    if (existingRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only update your own blood requests'
        }
      });
    }

    // Check if request is still active
    if (!isRequestActive(existingRequest)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'REQUEST_INACTIVE',
          message: 'Cannot update inactive or expired blood request'
        }
      });
    }

    const updateData: UpdateBloodRequestData = {
      quantity: req.body.quantity,
      urgency: req.body.urgency,
      location: req.body.location,
      contactInfo: req.body.contactInfo,
      description: req.body.description,
      status: req.body.status
    };

    // Remove undefined fields
    const cleanUpdateData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    // Update in Appwrite Database
    await appwriteService.updateBloodRequest(id, cleanUpdateData);

    // Fetch updated request
    const updatedRequest = await appwriteService.getBloodRequestById(id);

    const response: BloodRequest = {
      id: updatedRequest.$id,
      requesterId: updatedRequest.requesterId,
      bloodType: updatedRequest.bloodType,
      quantity: updatedRequest.quantity,
      urgency: updatedRequest.urgency,
      location: updatedRequest.location,
      contactInfo: updatedRequest.contactInfo,
      description: updatedRequest.description,
      status: updatedRequest.status,
      createdAt: updatedRequest.createdAt,
      expiresAt: updatedRequest.expiresAt,
      responses: updatedRequest.responses || [],
      notifiedDonors: updatedRequest.notifiedDonors || [],
      fulfillmentDetails: updatedRequest.fulfillmentDetails,
      updatedAt: updatedRequest.updatedAt
    };

    res.status(200).json({
      success: true,
      data: response,
      message: 'Blood request updated successfully'
    });

  } catch (error) {
    console.error('Error updating blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/appwrite/{id}:
 *   delete:
 *     summary: Cancel a blood request (Appwrite)
 *     description: Cancel an existing blood request in Appwrite Database (only by the requester)
 *     tags: [Blood Requests - Appwrite]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     responses:
 *       200:
 *         description: Blood request cancelled successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       403:
 *         description: Forbidden - not the request owner
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.delete('/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.uid;

    // Get existing request
    const existingRequest = await appwriteService.getBloodRequestById(id);
    
    if (!existingRequest) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    // Check if user owns this request
    if (existingRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only cancel your own blood requests'
        }
      });
    }

    // Update status to cancelled instead of deleting
    await appwriteService.updateBloodRequest(id, {
      status: RequestStatus.CANCELLED,
      cancelledAt: new Date().toISOString()
    });

    // Trigger cancellation notifications to notified donors
    try {
      const notifiedDonors = existingRequest.notifiedDonors || [];
      if (notifiedDonors.length > 0) {
        // Get FCM tokens for notified donors
        const donorTokens: string[] = [];
        for (const donorId of notifiedDonors) {
          const donorProfile = await appwriteService.getUserById(donorId);
          if (donorProfile) {
            const fcmToken = donorProfile.notificationSettings?.fcmToken;
            if (fcmToken) {
              donorTokens.push(fcmToken);
            }
          }
        }

        if (donorTokens.length > 0) {
          await matchingNotificationService.processRequestCancellation(
            existingRequest,
            donorTokens,
            { backend: 'appwrite', maxRetries: 2 }
          );
        }
      }
    } catch (notificationError) {
      // Log error but don't fail the cancellation
      console.error('Error sending cancellation notifications:', notificationError);
    }

    res.status(200).json({
      success: true,
      message: 'Blood request cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to cancel blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/appwrite/{id}/respond:
 *   post:
 *     summary: Respond to a blood request (Appwrite)
 *     description: Allow donors to respond to a blood request with yes/no
 *     tags: [Blood Requests - Appwrite]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/DonorResponseRequest'
 *           example:
 *             response: "yes"
 *     responses:
 *       200:
 *         description: Response recorded successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/DonorResponse'
 *       400:
 *         description: Validation error or already responded
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/:id/respond', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { response } = req.body;
    const userId = req.user?.uid;

    // Validate response
    if (!response || !['yes', 'no'].includes(response)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Response must be either "yes" or "no"'
        }
      });
    }

    // Get existing request
    const existingRequest = await appwriteService.getBloodRequestById(id);
    
    if (!existingRequest) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    // Check if request is still active
    if (!isRequestActive(existingRequest)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'REQUEST_INACTIVE',
          message: 'Cannot respond to inactive or expired blood request'
        }
      });
    }

    // Check if user already responded
    const existingResponse = existingRequest.responses?.find((r: any) => r.donorId === userId);
    if (existingResponse) {
      res.status(400).json({
        success: false,
        error: {
          code: 'ALREADY_RESPONDED',
          message: 'You have already responded to this blood request'
        }
      });
    }

    // Get donor profile for contact info (if responding yes)
    let donorContact;
    if (response === 'yes') {
      const donorProfile = await appwriteService.getUserById(userId!);
      if (donorProfile) {
        donorContact = {
          name: donorProfile.name || 'Anonymous Donor',
          phone: donorProfile.phoneNumber || ''
        };
      }
    }

    // Create donor response
    const donorResponse: DonorResponse = {
      donorId: userId!,
      response: response as 'yes' | 'no',
      respondedAt: new Date().toISOString(),
      donorContact: response === 'yes' ? donorContact : undefined
    };

    // Update request with new response
    const updatedResponses = [...(existingRequest.responses || []), donorResponse];
    
    await appwriteService.updateBloodRequest(id, {
      responses: updatedResponses
    });

    // Trigger notification to recipient about donor response
    try {
      // Get recipient's FCM token
      const requesterProfile = await appwriteService.getUserById(existingRequest.requesterId);
      if (requesterProfile) {
        const recipientFcmToken = requesterProfile.notificationSettings?.fcmToken;
        
        if (recipientFcmToken) {
          // Get donor data for notification
          const donorProfile = await appwriteService.getUserById(userId!);
          if (donorProfile) {
            const donor: User = {
              id: userId!,
              phoneNumber: donorProfile.phoneNumber,
              profile: donorProfile.profile,
              donorStats: donorProfile.donorStats,
              notificationSettings: donorProfile.notificationSettings
            } as User;

            await matchingNotificationService.processDonorResponse(
              id,
              donor,
              response as 'yes' | 'no',
              recipientFcmToken,
              { backend: 'appwrite', maxRetries: 2 }
            );
          }
        }
      }
    } catch (notificationError) {
      // Log error but don't fail the response recording
      console.error('Error sending donor response notification:', notificationError);
    }

    res.status(200).json({
      success: true,
      data: donorResponse,
      message: `Response "${response}" recorded successfully`
    });

  } catch (error) {
    console.error('Error responding to blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to respond to blood request'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/appwrite/{id}/responses:
 *   get:
 *     summary: Get all responses for a blood request (Appwrite)
 *     description: Retrieve all donor responses for a specific blood request (only by requester)
 *     tags: [Blood Requests - Appwrite]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     responses:
 *       200:
 *         description: Responses retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         requestId:
 *                           type: string
 *                         totalResponses:
 *                           type: number
 *                         positiveResponses:
 *                           type: number
 *                         responses:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/DonorResponse'
 *       403:
 *         description: Forbidden - not the request owner
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/:id/responses', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.uid;

    // Get existing request
    const existingRequest = await appwriteService.getBloodRequestById(id);
    
    if (!existingRequest) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    // Check if user owns this request (only requester can see all responses)
    if (existingRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only view responses to your own blood requests'
        }
      });
    }

    const responses = existingRequest.responses || [];
    
    // Filter to only show positive responses with contact info
    const positiveResponses = responses.filter((r: any) => r.response === 'yes');

    res.status(200).json({
      success: true,
      data: {
        requestId: id,
        totalResponses: responses.length,
        positiveResponses: positiveResponses.length,
        responses: positiveResponses
      }
    });

  } catch (error) {
    console.error('Error fetching blood request responses:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch blood request responses'
      }
    });
  }
});

/**
 * @swagger
 * /api/requests/appwrite/{id}/fulfill:
 *   put:
 *     summary: Mark a blood request as fulfilled (Appwrite)
 *     description: Mark a blood request as completed/fulfilled (only by requester)
 *     tags: [Blood Requests - Appwrite]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Blood request ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/FulfillRequest'
 *           example:
 *             fulfilledBy: "donor456"
 *             recipientFeedback:
 *               rating: 5
 *               comment: "Excellent donor, very responsive and helpful!"
 *     responses:
 *       200:
 *         description: Blood request marked as fulfilled successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/FulfillmentDetails'
 *       400:
 *         description: Request not active or invalid donor
 *       403:
 *         description: Forbidden - not the request owner
 *       404:
 *         description: Blood request not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/:id/fulfill', async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { fulfilledBy, recipientFeedback } = req.body;
    const userId = req.user?.uid;

    // Get existing request
    const existingRequest = await appwriteService.getBloodRequestById(id);
    
    if (!existingRequest) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Blood request not found'
        }
      });
    }

    // Check if user owns this request
    if (existingRequest.requesterId !== userId) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only fulfill your own blood requests'
        }
      });
    }

    // Check if request is still active
    if (existingRequest.status !== RequestStatus.ACTIVE) {
      res.status(400).json({
        success: false,
        error: {
          code: 'REQUEST_NOT_ACTIVE',
          message: 'Only active blood requests can be fulfilled'
        }
      });
    }

    // Validate fulfilledBy donor exists in responses
    if (fulfilledBy) {
      const donorResponse = existingRequest.responses?.find((r: any) => 
        r.donorId === fulfilledBy && r.response === 'yes'
      );
      if (!donorResponse) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_DONOR',
            message: 'Specified donor did not respond positively to this request'
          }
        });
      }
    }

    // Create fulfillment details
    const fulfillmentDetails: FulfillmentDetails = {
      fulfilledAt: new Date().toISOString(),
      fulfilledBy: fulfilledBy || 'unknown',
      recipientFeedback: recipientFeedback ? {
        rating: recipientFeedback.rating,
        comment: recipientFeedback.comment
      } : undefined
    };

    // Update request status and fulfillment details
    await appwriteService.updateBloodRequest(id, {
      status: RequestStatus.FULFILLED,
      fulfillmentDetails
    });

    // Trigger fulfillment notifications to other notified donors
    try {
      const notifiedDonors = existingRequest.notifiedDonors || [];
      if (notifiedDonors.length > 0) {
        // Get FCM tokens for notified donors (excluding the fulfilling donor)
        const donorTokens: string[] = [];
        for (const donorId of notifiedDonors) {
          if (donorId !== fulfilledBy) { // Don't notify the donor who fulfilled it
            const donorProfile = await appwriteService.getUserById(donorId);
            if (donorProfile) {
              const fcmToken = donorProfile.notificationSettings?.fcmToken;
              if (fcmToken) {
                donorTokens.push(fcmToken);
              }
            }
          }
        }

        if (donorTokens.length > 0) {
          await matchingNotificationService.processRequestFulfillment(
            existingRequest,
            fulfilledBy || 'unknown',
            donorTokens,
            { backend: 'appwrite', maxRetries: 2 }
          );
        }
      }
    } catch (notificationError) {
      // Log error but don't fail the fulfillment
      console.error('Error sending fulfillment notifications:', notificationError);
    }

    res.status(200).json({
      success: true,
      data: fulfillmentDetails,
      message: 'Blood request marked as fulfilled successfully'
    });

  } catch (error) {
    console.error('Error fulfilling blood request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fulfill blood request'
      }
    });
  }
});

export default router;