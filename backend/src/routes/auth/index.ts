import { Router } from 'express';
import firebaseAuthRoutes from './firebase';
import appwriteAuthRoutes from './appwrite';

const router = Router();

// Mount Firebase auth routes
router.use('/firebase', firebaseAuthRoutes);

// Mount Appwrite auth routes  
router.use('/appwrite', appwriteAuthRoutes);

/**
 * @swagger
 * /api/auth/health:
 *   get:
 *     summary: Authentication service health check
 *     description: Check the health status of the authentication service
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Authentication service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Authentication service is running"
 *                 backends:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["firebase", "appwrite"]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Authentication service is running',
    backends: ['firebase', 'appwrite'],
    timestamp: new Date().toISOString()
  });
});

export default router;