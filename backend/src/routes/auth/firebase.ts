import { Router, Request, Response } from 'express';
import { getAuth, getFirestore } from '../../config/firebase';
import { verifyFirebaseToken } from '../../middleware/firebaseAuth';

const router = Router();

/**
 * @swagger
 * /api/auth/firebase/register:
 *   post:
 *     summary: Register a new user (Firebase)
 *     description: Create a new user account using Firebase Authentication and store profile in Firestore
 *     tags: [Authentication - Firebase]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterRequest'
 *           example:
 *             phoneNumber: "+**********"
 *             displayName: "John Doe"
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         uid:
 *                           type: string
 *                         phoneNumber:
 *                           type: string
 *                         displayName:
 *                           type: string
 *                         profile:
 *                           $ref: '#/components/schemas/UserProfile'
 *       400:
 *         description: Validation error
 *       409:
 *         description: User already exists
 *       500:
 *         description: Internal server error
 */
router.post('/register', async (req: Request, res: Response): Promise<void> => {
  try {
    const { phoneNumber, displayName } = req.body;

    // Validate required fields
    if (!phoneNumber) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Phone number is required',
          details: { field: 'phoneNumber' }
        }
      });
      return;
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    if (!phoneRegex.test(phoneNumber)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid phone number format. Use international format (+**********)',
          details: { field: 'phoneNumber', value: phoneNumber }
        }
      });
      return;
    }

    try {
      // Create user with Firebase Auth
      const userRecord = await getAuth().createUser({
        phoneNumber,
        displayName: displayName || undefined,
      });

      // Create user profile in Firestore
      const firestore = getFirestore();
      const userProfile = {
        id: userRecord.uid,
        phoneNumber,
        profile: {
          name: displayName || '',
          bloodType: '',
          location: {
            coordinates: {
              latitude: 0,
              longitude: 0
            },
            address: '',
            lastUpdated: new Date().toISOString()
          },
          isAvailable: false,
          userType: 'donor',
          joinedAt: new Date().toISOString(),
          lastActive: new Date().toISOString()
        },
        donorStats: {
          totalDonations: 0,
          lastDonationDate: null,
          rating: 0,
          reviewCount: 0,
          badges: []
        },
        notificationSettings: {
          fcmToken: '',
          pushEnabled: true,
          maxDistance: 25,
          urgencyLevels: ['critical', 'urgent']
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await firestore.collection('users').doc(userRecord.uid).set(userProfile);

      res.status(201).json({
        success: true,
        data: {
          uid: userRecord.uid,
          phoneNumber: userRecord.phoneNumber,
          displayName: userRecord.displayName,
          profile: userProfile.profile
        }
      });
    } catch (firebaseError: any) {
      console.error('Firebase user creation error:', firebaseError);
      
      if (firebaseError.code === 'auth/phone-number-already-exists') {
        res.status(409).json({
          success: false,
          error: {
            code: 'USER_EXISTS',
            message: 'User with this phone number already exists'
          }
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: {
          code: 'USER_CREATION_FAILED',
          message: 'Failed to create user account'
        }
      });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Registration service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/auth/firebase/login:
 *   post:
 *     summary: Login with Firebase ID token
 *     description: Verify Firebase ID token and retrieve user profile from Firestore
 *     tags: [Authentication - Firebase]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [idToken]
 *             properties:
 *               idToken:
 *                 type: string
 *                 description: Firebase ID token obtained from client SDK
 *           example:
 *             idToken: "eyJhbGciOiJSUzI1NiIsImtpZCI6IjE2NzAyN..."
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation error - ID token required
 *       401:
 *         description: Invalid or expired ID token
 *       404:
 *         description: User profile not found
 *       500:
 *         description: Internal server error
 */
router.post('/login', async (req: Request, res: Response): Promise<void> => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'ID token is required',
          details: { field: 'idToken' }
        }
      });
      return;
    }

    try {
      // Verify the Firebase ID token
      const decodedToken = await getAuth().verifyIdToken(idToken);
      
      // Get user profile from Firestore
      const firestore = getFirestore();
      const userDoc = await firestore.collection('users').doc(decodedToken.uid).get();
      
      if (!userDoc.exists) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User profile not found. Please complete registration.'
          }
        });
        return;
      }

      const userData = userDoc.data();
      
      // Update last active timestamp
      await firestore.collection('users').doc(decodedToken.uid).update({
        'profile.lastActive': new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      res.status(200).json({
        success: true,
        data: {
          uid: decodedToken.uid,
          phoneNumber: decodedToken.phone_number,
          profile: userData?.profile,
          donorStats: userData?.donorStats,
          notificationSettings: userData?.notificationSettings
        }
      });
    } catch (tokenError: any) {
      console.error('Token verification error:', tokenError);
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired ID token'
        }
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Login service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/auth/firebase/profile:
 *   get:
 *     summary: Get current user profile (Firebase)
 *     description: Retrieve the authenticated user's profile from Firestore
 *     tags: [Authentication - Firebase]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized - invalid or missing token
 *       404:
 *         description: User profile not found
 *       500:
 *         description: Internal server error
 */
router.get('/profile', verifyFirebaseToken, async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    const firestore = getFirestore();
    const userDoc = await firestore.collection('users').doc(req.user.uid).get();
    
    if (!userDoc.exists) {
      res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User profile not found'
        }
      });
      return;
    }

    const userData = userDoc.data();
    
    res.status(200).json({
      success: true,
      data: {
        uid: req.user.uid,
        phoneNumber: req.user.phoneNumber,
        profile: userData?.profile,
        donorStats: userData?.donorStats,
        notificationSettings: userData?.notificationSettings
      }
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Profile service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/auth/firebase/profile:
 *   put:
 *     summary: Update user profile (Firebase)
 *     description: Update the authenticated user's profile in Firestore
 *     tags: [Authentication - Firebase]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProfileRequest'
 *           example:
 *             name: "John Doe"
 *             bloodType: "O+"
 *             userType: "donor"
 *             location:
 *               coordinates:
 *                 latitude: 40.7128
 *                 longitude: -74.0060
 *               address: "123 Main St, New York, NY"
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized - invalid or missing token
 *       500:
 *         description: Internal server error
 */
router.put('/profile', verifyFirebaseToken, async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    const { name, bloodType, userType, location } = req.body;
    const firestore = getFirestore();
    
    // Build update object
    const updateData: any = {
      updatedAt: new Date().toISOString()
    };

    if (name !== undefined) {
      updateData['profile.name'] = name;
    }

    if (bloodType !== undefined) {
      // Validate blood type
      const validBloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
      if (!validBloodTypes.includes(bloodType)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid blood type',
            details: { 
              field: 'bloodType', 
              value: bloodType,
              allowedValues: validBloodTypes
            }
          }
        });
        return;
      }
      updateData['profile.bloodType'] = bloodType;
    }

    if (userType !== undefined) {
      // Validate user type
      const validUserTypes = ['donor', 'recipient', 'both'];
      if (!validUserTypes.includes(userType)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid user type',
            details: { 
              field: 'userType', 
              value: userType,
              allowedValues: validUserTypes
            }
          }
        });
        return;
      }
      updateData['profile.userType'] = userType;
    }

    if (location !== undefined) {
      if (location.coordinates) {
        updateData['profile.location.coordinates'] = location.coordinates;
      }
      if (location.address) {
        updateData['profile.location.address'] = location.address;
      }
      updateData['profile.location.lastUpdated'] = new Date().toISOString();
    }

    // Update user profile
    await firestore.collection('users').doc(req.user.uid).update(updateData);

    // Get updated profile
    const updatedDoc = await firestore.collection('users').doc(req.user.uid).get();
    const updatedData = updatedDoc.data();

    res.status(200).json({
      success: true,
      data: {
        uid: req.user.uid,
        profile: updatedData?.profile,
        donorStats: updatedData?.donorStats,
        notificationSettings: updatedData?.notificationSettings
      }
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Profile update service error'
      }
    });
  }
});

export default router;