import { Router, Request, Response } from 'express';
import { getDatabases, getUsers, APPWRITE_DATABASE_ID, COLLECTIONS } from '../../config/appwrite';
import { authenticateAppwriteSession } from '../../middleware/authMiddleware';
import { Client, Account, ID } from 'node-appwrite';

const router = Router();

/**
 * @swagger
 * /api/auth/appwrite/register:
 *   post:
 *     summary: Register a new user (Appwrite)
 *     description: Create a new user account using Appwrite Authentication and store profile in Appwrite Database
 *     tags: [Authentication - Appwrite]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             allOf:
 *               - $ref: '#/components/schemas/RegisterRequest'
 *               - type: object
 *                 required: [password]
 *                 properties:
 *                   password:
 *                     type: string
 *                     minLength: 8
 *                     description: Password for the account
 *           example:
 *             phoneNumber: "+**********"
 *             displayName: "<PERSON>"
 *             password: "securePassword123"
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         $id:
 *                           type: string
 *                         phoneNumber:
 *                           type: string
 *                         name:
 *                           type: string
 *                         profile:
 *                           $ref: '#/components/schemas/UserProfile'
 *       400:
 *         description: Validation error
 *       409:
 *         description: User already exists
 *       500:
 *         description: Internal server error
 */
router.post('/register', async (req: Request, res: Response): Promise<void> => {
  try {
    const { phoneNumber, displayName, password } = req.body;

    // Validate required fields
    if (!phoneNumber || !password) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Phone number and password are required',
          details: { 
            missingFields: [
              ...(!phoneNumber ? ['phoneNumber'] : []),
              ...(!password ? ['password'] : [])
            ]
          }
        }
      });
      return;
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    if (!phoneRegex.test(phoneNumber)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid phone number format. Use international format (+**********)',
          details: { field: 'phoneNumber', value: phoneNumber }
        }
      });
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Password must be at least 8 characters long',
          details: { field: 'password' }
        }
      });
      return;
    }

    try {
      const users = getUsers();
      
      // Create user with Appwrite Auth
      const userRecord = await users.create(
        ID.unique(),
        undefined, // email (optional)
        phoneNumber,
        password,
        displayName || undefined
      );

      // Create user profile in Appwrite Database
      const databases = getDatabases();
      const userProfile = {
        id: userRecord.$id,
        phoneNumber,
        profile: {
          name: displayName || '',
          bloodType: '',
          location: {
            coordinates: {
              latitude: 0,
              longitude: 0
            },
            address: '',
            lastUpdated: new Date().toISOString()
          },
          isAvailable: false,
          userType: 'donor',
          joinedAt: new Date().toISOString(),
          lastActive: new Date().toISOString()
        },
        donorStats: {
          totalDonations: 0,
          lastDonationDate: null,
          rating: 0,
          reviewCount: 0,
          badges: []
        },
        notificationSettings: {
          fcmToken: '',
          pushEnabled: true,
          maxDistance: 25,
          urgencyLevels: ['critical', 'urgent']
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await databases.createDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userRecord.$id,
        userProfile
      );

      res.status(201).json({
        success: true,
        data: {
          $id: userRecord.$id,
          phoneNumber: userRecord.phone,
          name: userRecord.name,
          profile: userProfile.profile
        }
      });
    } catch (appwriteError: any) {
      console.error('Appwrite user creation error:', appwriteError);
      
      if (appwriteError.code === 409) {
        res.status(409).json({
          success: false,
          error: {
            code: 'USER_EXISTS',
            message: 'User with this phone number already exists'
          }
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: {
          code: 'USER_CREATION_FAILED',
          message: 'Failed to create user account',
          details: appwriteError.message
        }
      });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Registration service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/auth/appwrite/login:
 *   post:
 *     summary: Login with phone and password (Appwrite)
 *     description: Create an Appwrite session using phone number and password
 *     tags: [Authentication - Appwrite]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [phoneNumber, password]
 *             properties:
 *               phoneNumber:
 *                 type: string
 *                 pattern: '^\\+[1-9]\\d{1,14}$'
 *                 description: Phone number in international format
 *               password:
 *                 type: string
 *                 description: User password
 *           example:
 *             phoneNumber: "+**********"
 *             password: "securePassword123"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         sessionId:
 *                           type: string
 *                         userId:
 *                           type: string
 *                         phoneNumber:
 *                           type: string
 *                         profile:
 *                           $ref: '#/components/schemas/UserProfile'
 *                         donorStats:
 *                           $ref: '#/components/schemas/DonorStats'
 *                         notificationSettings:
 *                           $ref: '#/components/schemas/NotificationSettings'
 *       400:
 *         description: Validation error - phone and password required
 *       401:
 *         description: Invalid credentials
 *       500:
 *         description: Internal server error
 */
router.post('/login', async (req: Request, res: Response): Promise<void> => {
  try {
    const { phoneNumber, password } = req.body;

    if (!phoneNumber || !password) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Phone number and password are required',
          details: { 
            missingFields: [
              ...(!phoneNumber ? ['phoneNumber'] : []),
              ...(!password ? ['password'] : [])
            ]
          }
        }
      });
      return;
    }

    try {
      // Create a client for session creation
      const client = new Client()
        .setEndpoint(process.env.APPWRITE_ENDPOINT!)
        .setProject(process.env.APPWRITE_PROJECT_ID!);

      const account = new Account(client);
      
      // Create session with phone and password
      const session = await account.createSession(phoneNumber, password);
      
      // Get user profile from database
      const databases = getDatabases();
      const userDoc = await databases.getDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        session.userId
      );
      
      // Update last active timestamp
      await databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        session.userId,
        {
          'profile.lastActive': new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      );

      res.status(200).json({
        success: true,
        data: {
          sessionId: session.$id,
          userId: session.userId,
          phoneNumber: userDoc.phoneNumber,
          profile: userDoc.profile,
          donorStats: userDoc.donorStats,
          notificationSettings: userDoc.notificationSettings
        }
      });
    } catch (appwriteError: any) {
      console.error('Appwrite login error:', appwriteError);
      
      if (appwriteError.code === 401) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid phone number or password'
          }
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: {
          code: 'LOGIN_FAILED',
          message: 'Failed to create session'
        }
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Login service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/auth/appwrite/profile:
 *   get:
 *     summary: Get current user profile (Appwrite)
 *     description: Retrieve the authenticated user's profile from Appwrite Database
 *     tags: [Authentication - Appwrite]
 *     security:
 *       - AppwriteSession: []
 *       - AppwriteUserId: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized - invalid or missing session
 *       500:
 *         description: Internal server error
 */
router.get('/profile', authenticateAppwriteSession, async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user?.uid) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    // Get user profile from database
    const databases = getDatabases();
    const userDoc = await databases.getDocument(
      APPWRITE_DATABASE_ID,
      COLLECTIONS.USERS,
      req.user.uid
    );

    const userData = userDoc;
    
    res.status(200).json({
      success: true,
      data: {
        $id: req.user.uid,
        phoneNumber: userData.phoneNumber,
        profile: userData.profile,
        donorStats: userData.donorStats,
        notificationSettings: userData.notificationSettings
      }
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Profile service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/auth/appwrite/profile:
 *   put:
 *     summary: Update user profile (Appwrite)
 *     description: Update the authenticated user's profile in Appwrite Database
 *     tags: [Authentication - Appwrite]
 *     security:
 *       - AppwriteSession: []
 *       - AppwriteUserId: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProfileRequest'
 *           example:
 *             name: "Jane Smith"
 *             bloodType: "A+"
 *             userType: "both"
 *             location:
 *               coordinates:
 *                 latitude: 37.7749
 *                 longitude: -122.4194
 *               address: "456 Oak St, San Francisco, CA"
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized - invalid or missing session
 *       500:
 *         description: Internal server error
 */
router.put('/profile', authenticateAppwriteSession, async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user?.uid) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    const { name, bloodType, userType, location } = req.body;
    const databases = getDatabases();
    
    // Build update object
    const updateData: any = {
      updatedAt: new Date().toISOString()
    };

    if (name !== undefined) {
      updateData['profile.name'] = name;
    }

    if (bloodType !== undefined) {
      // Validate blood type
      const validBloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
      if (!validBloodTypes.includes(bloodType)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid blood type',
            details: { 
              field: 'bloodType', 
              value: bloodType,
              allowedValues: validBloodTypes
            }
          }
        });
        return;
      }
      updateData['profile.bloodType'] = bloodType;
    }

    if (userType !== undefined) {
      // Validate user type
      const validUserTypes = ['donor', 'recipient', 'both'];
      if (!validUserTypes.includes(userType)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid user type',
            details: { 
              field: 'userType', 
              value: userType,
              allowedValues: validUserTypes
            }
          }
        });
        return;
      }
      updateData['profile.userType'] = userType;
    }

    if (location !== undefined) {
      if (location.coordinates) {
        updateData['profile.location.coordinates'] = location.coordinates;
      }
      if (location.address) {
        updateData['profile.location.address'] = location.address;
      }
      updateData['profile.location.lastUpdated'] = new Date().toISOString();
    }

    // Update user profile
    const updatedDoc = await databases.updateDocument(
      APPWRITE_DATABASE_ID,
      COLLECTIONS.USERS,
      req.user.uid,
      updateData
    );

    res.status(200).json({
      success: true,
      data: {
        $id: req.user.uid,
        profile: updatedDoc.profile,
        donorStats: updatedDoc.donorStats,
        notificationSettings: updatedDoc.notificationSettings
      }
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Profile update service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/auth/appwrite/logout:
 *   delete:
 *     summary: Logout user (Appwrite)
 *     description: Delete the current Appwrite session to log out the user
 *     tags: [Authentication - Appwrite]
 *     security:
 *       - AppwriteSession: []
 *       - AppwriteUserId: []
 *     responses:
 *       200:
 *         description: Logout successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: "Logged out successfully"
 *       400:
 *         description: Session ID required for logout
 *       401:
 *         description: Unauthorized - invalid or missing session
 *       500:
 *         description: Internal server error
 */
router.delete('/logout', authenticateAppwriteSession, async (req: Request, res: Response): Promise<void> => {
  try {
    const sessionId = req.headers['x-appwrite-session'] as string;
    
    if (!sessionId) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Session ID required for logout'
        }
      });
      return;
    }

    try {
      // Create a client with the session to delete it
      const client = new Client()
        .setEndpoint(process.env.APPWRITE_ENDPOINT!)
        .setProject(process.env.APPWRITE_PROJECT_ID!)
        .setSession(sessionId);

      const account = new Account(client);
      await account.deleteSession('current');

      res.status(200).json({
        success: true,
        message: 'Logged out successfully'
      });
    } catch (appwriteError: any) {
      console.error('Appwrite logout error:', appwriteError);
      res.status(500).json({
        success: false,
        error: {
          code: 'LOGOUT_FAILED',
          message: 'Failed to delete session'
        }
      });
    }
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Logout service error'
      }
    });
  }
});

export default router;