import { Router, Request, Response } from 'express';
import { getFirestore } from '../config/firebase';
import { getDatabases, APPWRITE_DATABASE_ID, COLLECTIONS } from '../config/appwrite';
import { verifyFirebaseToken } from '../middleware/firebaseAuth';
import { authenticateAppwriteSession } from '../middleware/authMiddleware';

const router = Router();

// Helper function to determine backend type from headers
const getBackendType = (req: Request): 'firebase' | 'appwrite' => {
  const authHeader = req.headers.authorization;
  const sessionHeader = req.headers['x-appwrite-session'];
  
  if (sessionHeader) return 'appwrite';
  if (authHeader?.startsWith('Bearer ')) return 'firebase';
  
  return 'firebase'; // default
};

// Middleware to handle both authentication types
const authenticateUser = async (req: Request, res: Response, next: any) => {
  const backendType = getBackendType(req);
  
  if (backendType === 'firebase') {
    return verifyFirebaseToken(req, res, next);
  } else {
    return authenticateAppwriteSession(req, res, next);
  }
};

/**
 * @swagger
 * /api/users/profile:
 *   get:
 *     summary: Get current user profile (Universal)
 *     description: Retrieve the authenticated user's profile from either Firebase or Appwrite backend
 *     tags: [User Management]
 *     security:
 *       - BearerAuth: []
 *       - AppwriteSession: []
 *       - AppwriteUserId: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized - invalid or missing authentication
 *       404:
 *         description: User profile not found
 *       500:
 *         description: Internal server error
 */
router.get('/profile', authenticateUser, async (req: Request, res: Response): Promise<void> => {
  try {
    const backendType = getBackendType(req);
    let userData: any = null;
    let userId: string;

    if (backendType === 'firebase') {
      if (!req.user?.uid) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User not authenticated'
          }
        });
        return;
      }

      userId = req.user.uid;
      const firestore = getFirestore();
      const userDoc = await firestore.collection('users').doc(userId).get();
      
      if (!userDoc.exists) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User profile not found'
          }
        });
        return;
      }

      userData = userDoc.data();
    } else {
      // Appwrite
      if (!req.user?.uid) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User not authenticated'
          }
        });
        return;
      }

      userId = req.user.uid;
      const databases = getDatabases();
      const userDoc = await databases.getDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userId
      );
      
      userData = userDoc;
    }

    res.status(200).json({
      success: true,
      data: {
        id: userId,
        phoneNumber: userData.phoneNumber,
        profile: userData.profile,
        donorStats: userData.donorStats,
        notificationSettings: userData.notificationSettings,
        backend: backendType
      }
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Profile service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/users/profile:
 *   put:
 *     summary: Update user profile (Universal)
 *     description: Update the authenticated user's profile in either Firebase or Appwrite backend
 *     tags: [User Management]
 *     security:
 *       - BearerAuth: []
 *       - AppwriteSession: []
 *       - AppwriteUserId: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: User display name
 *               bloodType:
 *                 type: string
 *                 enum: [A+, A-, B+, B-, AB+, AB-, O+, O-]
 *                 description: User blood type
 *               userType:
 *                 type: string
 *                 enum: [donor, recipient, both]
 *                 description: User role in the platform
 *           example:
 *             name: "John Doe"
 *             bloodType: "O+"
 *             userType: "donor"
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized - invalid or missing authentication
 *       500:
 *         description: Internal server error
 */
router.put('/profile', authenticateUser, async (req: Request, res: Response): Promise<void> => {
  try {
    const backendType = getBackendType(req);
    
    if (!req.user?.uid) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    const { name, bloodType, userType } = req.body;
    const userId = req.user.uid;
    
    // Build update object
    const updateData: any = {
      updatedAt: new Date().toISOString()
    };

    if (name !== undefined) {
      updateData['profile.name'] = name;
    }

    if (bloodType !== undefined) {
      // Validate blood type
      const validBloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
      if (!validBloodTypes.includes(bloodType)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid blood type',
            details: { 
              field: 'bloodType', 
              value: bloodType,
              allowedValues: validBloodTypes
            }
          }
        });
        return;
      }
      updateData['profile.bloodType'] = bloodType;
    }

    if (userType !== undefined) {
      // Validate user type
      const validUserTypes = ['donor', 'recipient', 'both'];
      if (!validUserTypes.includes(userType)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid user type',
            details: { 
              field: 'userType', 
              value: userType,
              allowedValues: validUserTypes
            }
          }
        });
        return;
      }
      updateData['profile.userType'] = userType;
    }

    let updatedData: any;

    if (backendType === 'firebase') {
      const firestore = getFirestore();
      await firestore.collection('users').doc(userId).update(updateData);
      
      // Get updated profile
      const updatedDoc = await firestore.collection('users').doc(userId).get();
      updatedData = updatedDoc.data();
    } else {
      // Appwrite
      const databases = getDatabases();
      const updatedDoc = await databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userId,
        updateData
      );
      updatedData = updatedDoc;
    }

    res.status(200).json({
      success: true,
      data: {
        id: userId,
        profile: updatedData?.profile,
        donorStats: updatedData?.donorStats,
        notificationSettings: updatedData?.notificationSettings,
        backend: backendType
      }
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Profile update service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/users/location:
 *   put:
 *     summary: Update user location (Universal)
 *     description: Update the authenticated user's location in either Firebase or Appwrite backend
 *     tags: [User Management]
 *     security:
 *       - BearerAuth: []
 *       - AppwriteSession: []
 *       - AppwriteUserId: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateLocationRequest'
 *           example:
 *             coordinates:
 *               latitude: 40.7128
 *               longitude: -74.0060
 *             address: "123 Main St, New York, NY 10001"
 *     responses:
 *       200:
 *         description: Location updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         location:
 *                           $ref: '#/components/schemas/UserLocation'
 *                         backend:
 *                           type: string
 *                           enum: [firebase, appwrite]
 *       400:
 *         description: Validation error - invalid coordinates
 *       401:
 *         description: Unauthorized - invalid or missing authentication
 *       500:
 *         description: Internal server error
 */
router.put('/location', authenticateUser, async (req: Request, res: Response): Promise<void> => {
  try {
    const backendType = getBackendType(req);
    
    if (!req.user?.uid) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    const { coordinates, address } = req.body;
    const userId = req.user.uid;

    // Validate coordinates
    if (coordinates) {
      const { latitude, longitude } = coordinates;
      
      if (typeof latitude !== 'number' || typeof longitude !== 'number') {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid coordinates format',
            details: { 
              field: 'coordinates',
              expected: { latitude: 'number', longitude: 'number' }
            }
          }
        });
        return;
      }

      if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Coordinates out of valid range',
            details: { 
              field: 'coordinates',
              ranges: { latitude: '[-90, 90]', longitude: '[-180, 180]' }
            }
          }
        });
        return;
      }
    }

    // Build update object
    const updateData: any = {
      'profile.location.lastUpdated': new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (coordinates) {
      updateData['profile.location.coordinates'] = coordinates;
    }

    if (address !== undefined) {
      updateData['profile.location.address'] = address;
    }

    let updatedData: any;

    if (backendType === 'firebase') {
      const firestore = getFirestore();
      await firestore.collection('users').doc(userId).update(updateData);
      
      // Get updated profile
      const updatedDoc = await firestore.collection('users').doc(userId).get();
      updatedData = updatedDoc.data();
    } else {
      // Appwrite
      const databases = getDatabases();
      const updatedDoc = await databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userId,
        updateData
      );
      updatedData = updatedDoc;
    }

    res.status(200).json({
      success: true,
      data: {
        id: userId,
        location: updatedData?.profile?.location,
        backend: backendType
      }
    });
  } catch (error) {
    console.error('Location update error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Location update service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/users/availability:
 *   put:
 *     summary: Update donor availability status (Universal)
 *     description: Update the authenticated user's availability status in either Firebase or Appwrite backend
 *     tags: [User Management]
 *     security:
 *       - BearerAuth: []
 *       - AppwriteSession: []
 *       - AppwriteUserId: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateAvailabilityRequest'
 *           example:
 *             isAvailable: true
 *     responses:
 *       200:
 *         description: Availability status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         isAvailable:
 *                           type: boolean
 *                         lastActive:
 *                           type: string
 *                           format: date-time
 *                         backend:
 *                           type: string
 *                           enum: [firebase, appwrite]
 *       400:
 *         description: Validation error - isAvailable must be boolean
 *       401:
 *         description: Unauthorized - invalid or missing authentication
 *       500:
 *         description: Internal server error
 */
router.put('/availability', authenticateUser, async (req: Request, res: Response): Promise<void> => {
  try {
    const backendType = getBackendType(req);
    
    if (!req.user?.uid) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    const { isAvailable } = req.body;
    const userId = req.user.uid;

    // Validate availability status
    if (typeof isAvailable !== 'boolean') {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Availability status must be a boolean value',
          details: { 
            field: 'isAvailable',
            value: isAvailable,
            expected: 'boolean'
          }
        }
      });
      return;
    }

    // Build update object
    const updateData: any = {
      'profile.isAvailable': isAvailable,
      'profile.lastActive': new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    let updatedData: any;

    if (backendType === 'firebase') {
      const firestore = getFirestore();
      await firestore.collection('users').doc(userId).update(updateData);
      
      // Get updated profile
      const updatedDoc = await firestore.collection('users').doc(userId).get();
      updatedData = updatedDoc.data();
    } else {
      // Appwrite
      const databases = getDatabases();
      const updatedDoc = await databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userId,
        updateData
      );
      updatedData = updatedDoc;
    }

    res.status(200).json({
      success: true,
      data: {
        id: userId,
        isAvailable: updatedData?.profile?.isAvailable,
        lastActive: updatedData?.profile?.lastActive,
        backend: backendType
      }
    });
  } catch (error) {
    console.error('Availability update error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Availability update service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/users/notification-settings:
 *   put:
 *     summary: Update notification settings (Universal)
 *     description: Update the authenticated user's notification preferences in either Firebase or Appwrite backend
 *     tags: [User Management]
 *     security:
 *       - BearerAuth: []
 *       - AppwriteSession: []
 *       - AppwriteUserId: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateNotificationSettingsRequest'
 *           example:
 *             fcmToken: "dGhpcyBpcyBhIGZha2UgdG9rZW4="
 *             pushEnabled: true
 *             maxDistance: 25
 *             urgencyLevels: ["critical", "urgent"]
 *     responses:
 *       200:
 *         description: Notification settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         notificationSettings:
 *                           $ref: '#/components/schemas/NotificationSettings'
 *                         backend:
 *                           type: string
 *                           enum: [firebase, appwrite]
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized - invalid or missing authentication
 *       500:
 *         description: Internal server error
 */
router.put('/notification-settings', authenticateUser, async (req: Request, res: Response): Promise<void> => {
  try {
    const backendType = getBackendType(req);
    
    if (!req.user?.uid) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
      return;
    }

    const { fcmToken, pushEnabled, maxDistance, urgencyLevels } = req.body;
    const userId = req.user.uid;

    // Build update object
    const updateData: any = {
      updatedAt: new Date().toISOString()
    };

    if (fcmToken !== undefined) {
      updateData['notificationSettings.fcmToken'] = fcmToken;
    }

    if (pushEnabled !== undefined) {
      if (typeof pushEnabled !== 'boolean') {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Push enabled must be a boolean value',
            details: { field: 'pushEnabled', expected: 'boolean' }
          }
        });
        return;
      }
      updateData['notificationSettings.pushEnabled'] = pushEnabled;
    }

    if (maxDistance !== undefined) {
      if (typeof maxDistance !== 'number' || maxDistance < 1 || maxDistance > 100) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Max distance must be a number between 1 and 100 km',
            details: { field: 'maxDistance', range: '[1, 100]' }
          }
        });
        return;
      }
      updateData['notificationSettings.maxDistance'] = maxDistance;
    }

    if (urgencyLevels !== undefined) {
      const validUrgencyLevels = ['critical', 'urgent', 'normal'];
      if (!Array.isArray(urgencyLevels) || 
          !urgencyLevels.every(level => validUrgencyLevels.includes(level))) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid urgency levels',
            details: { 
              field: 'urgencyLevels',
              allowedValues: validUrgencyLevels
            }
          }
        });
        return;
      }
      updateData['notificationSettings.urgencyLevels'] = urgencyLevels;
    }

    let updatedData: any;

    if (backendType === 'firebase') {
      const firestore = getFirestore();
      await firestore.collection('users').doc(userId).update(updateData);
      
      // Get updated profile
      const updatedDoc = await firestore.collection('users').doc(userId).get();
      updatedData = updatedDoc.data();
    } else {
      // Appwrite
      const databases = getDatabases();
      const updatedDoc = await databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.USERS,
        userId,
        updateData
      );
      updatedData = updatedDoc;
    }

    res.status(200).json({
      success: true,
      data: {
        id: userId,
        notificationSettings: updatedData?.notificationSettings,
        backend: backendType
      }
    });
  } catch (error) {
    console.error('Notification settings update error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Notification settings update service error'
      }
    });
  }
});

export default router;