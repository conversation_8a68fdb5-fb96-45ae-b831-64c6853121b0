/**
 * Response model interface and validation
 */

import { ResponseType } from '../types/enums';

/**
 * Response interface for donor responses to blood requests
 */
export interface Response {
  id: string;
  requestId: string;
  donorId: string;
  response: ResponseType;
  respondedAt: string;
  donorContact?: {
    name: string;
    phone: string;
  };
  message?: string;
}

/**
 * Response creation data interface
 */
export interface CreateResponseData {
  requestId: string;
  donorId: string;
  response: string;
  donorContact?: {
    name: string;
    phone: string;
  };
  message?: string;
}

/**
 * Response update data interface
 */
export interface UpdateResponseData {
  response?: ResponseType;
  message?: string;
}

/**
 * Validates response creation data
 * @param responseData - The response data to validate
 * @returns Object with isValid boolean and errors array
 */
export function validateCreateResponseData(responseData: CreateResponseData): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate request ID
  if (!responseData.requestId || typeof responseData.requestId !== 'string') {
    errors.push('Request ID is required');
  }

  // Validate donor ID
  if (!responseData.donorId || typeof responseData.donorId !== 'string') {
    errors.push('Donor ID is required');
  }

  // Validate response type
  if (!responseData.response || !Object.values(ResponseType).includes(responseData.response as ResponseType)) {
    errors.push('Valid response is required (yes, no)');
  }

  // Validate donor contact if provided
  if (responseData.donorContact) {
    const contactValidation = validateDonorContact(responseData.donorContact);
    if (!contactValidation.isValid) {
      errors.push(...contactValidation.errors);
    }
  }

  // Validate message if provided
  if (responseData.message && responseData.message.length > 200) {
    errors.push('Message must be less than 200 characters');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates donor contact information
 * @param donorContact - The donor contact info to validate
 * @returns Object with isValid boolean and errors array
 */
export function validateDonorContact(donorContact: { name: string; phone: string }): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate name
  if (!donorContact.name || typeof donorContact.name !== 'string') {
    errors.push('Donor name is required');
  } else if (donorContact.name.trim().length < 2) {
    errors.push('Donor name must be at least 2 characters long');
  }

  // Validate phone
  if (!donorContact.phone || typeof donorContact.phone !== 'string') {
    errors.push('Donor phone is required');
  } else if (!/^\+[1-9]\d{1,14}$/.test(donorContact.phone)) {
    errors.push('Donor phone must be in international format (e.g., +1234567890)');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Creates a new response object with default values
 * @param responseData - The response creation data
 * @returns Complete Response object
 */
export function createResponse(responseData: CreateResponseData): Response {
  const now = new Date().toISOString();
  
  return {
    id: '', // Will be set by database
    requestId: responseData.requestId,
    donorId: responseData.donorId,
    response: responseData.response as ResponseType,
    respondedAt: now,
    donorContact: responseData.donorContact,
    message: responseData.message?.trim()
  };
}

/**
 * Checks if a response is positive (donor willing to help)
 * @param response - The response to check
 * @returns boolean indicating if response is positive
 */
export function isPositiveResponse(response: Response): boolean {
  return response.response === ResponseType.YES;
}

/**
 * Checks if a response is negative (donor not available)
 * @param response - The response to check
 * @returns boolean indicating if response is negative
 */
export function isNegativeResponse(response: Response): boolean {
  return response.response === ResponseType.NO;
}