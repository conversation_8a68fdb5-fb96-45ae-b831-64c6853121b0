/**
 * Models and types index file
 * Exports all models, types, and validation functions
 */

// User model exports
export {
  User,
  UserProfile,
  DonorStats,
  NotificationSettings,
  CreateUserData,
  UpdateUserData,
  validateCreateUserData,
  validateUserProfile,
  createUser
} from './User';

// BloodRequest model exports
export {
  BloodRequest,
  ContactInfo,
  DonorResponse,
  FulfillmentDetails,
  CreateBloodRequestData,
  UpdateBloodRequestData,
  validateCreateBloodRequestData,
  validateContactInfo,
  calculateExpirationTime,
  createBloodRequest,
  isRequestExpired,
  isRequestActive
} from './BloodRequest';

// Response model exports
export {
  Response,
  CreateResponseData,
  UpdateResponseData,
  validateCreateResponseData,
  validateDonorContact,
  createResponse,
  isPositiveResponse,
  isNegativeResponse
} from './Response';

// Enum exports
export {
  BloodType,
  RequestStatus,
  UrgencyLevel,
  UserType,
  ResponseType,
  NotificationStatus,
  ContactPreference
} from '../types/enums';

// Location types exports
export {
  Coordinates,
  Location,
  validateCoordinates,
  validateLocation,
  calculateDistance
} from '../types/location';

// Blood compatibility exports
export {
  isBloodTypeCompatible,
  getCompatibleDonorTypes,
  getCompatibleRecipientTypes,
  validateBloodType
} from '../types/bloodCompatibility';