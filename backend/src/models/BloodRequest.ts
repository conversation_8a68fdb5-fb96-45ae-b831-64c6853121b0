/**
 * Blood Request model interface and validation
 */

import { BloodType, RequestStatus, UrgencyLevel, ContactPreference } from '../types/enums';
import { Location, validateLocation } from '../types/location';
import { validateBloodType } from '../types/bloodCompatibility';

/**
 * Contact information interface
 */
export interface ContactInfo {
  name: string;
  phone: string;
  preferredContact: ContactPreference;
}

/**
 * Donor response interface
 */
export interface DonorResponse {
  donorId: string;
  response: 'yes' | 'no';
  respondedAt: string;
  donorContact?: {
    name: string;
    phone: string;
  };
}

/**
 * Fulfillment details interface
 */
export interface FulfillmentDetails {
  fulfilledAt: string;
  fulfilledBy: string;
  recipientFeedback?: {
    rating: number;
    comment: string;
  };
}

/**
 * Blood Request interface
 */
export interface BloodRequest {
  id: string;
  requesterId: string;
  bloodType: BloodType;
  quantity: string;
  urgency: UrgencyLevel;
  location: Location;
  contactInfo: ContactInfo;
  description?: string;
  status: RequestStatus;
  createdAt: string;
  expiresAt: string;
  responses: DonorResponse[];
  notifiedDonors: string[];
  fulfillmentDetails?: FulfillmentDetails;
  updatedAt?: string;
}

/**
 * Blood request creation data interface
 */
export interface CreateBloodRequestData {
  requesterId: string;
  bloodType: string;
  quantity: string;
  urgency: string;
  location: Location;
  contactInfo: ContactInfo;
  description?: string;
}

/**
 * Blood request update data interface
 */
export interface UpdateBloodRequestData {
  quantity?: string;
  urgency?: UrgencyLevel;
  location?: Location;
  contactInfo?: ContactInfo;
  description?: string;
  status?: RequestStatus;
}

/**
 * Validates blood request creation data
 * @param requestData - The blood request data to validate
 * @returns Object with isValid boolean and errors array
 */
export function validateCreateBloodRequestData(requestData: CreateBloodRequestData): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate requester ID
  if (!requestData.requesterId || typeof requestData.requesterId !== 'string') {
    errors.push('Requester ID is required');
  }

  // Validate blood type
  if (!requestData.bloodType || !validateBloodType(requestData.bloodType)) {
    errors.push('Valid blood type is required (A+, A-, B+, B-, AB+, AB-, O+, O-)');
  }

  // Validate quantity
  if (!requestData.quantity || typeof requestData.quantity !== 'string') {
    errors.push('Quantity is required');
  } else if (requestData.quantity.trim().length === 0) {
    errors.push('Quantity cannot be empty');
  }

  // Validate urgency
  if (!requestData.urgency || !Object.values(UrgencyLevel).includes(requestData.urgency as UrgencyLevel)) {
    errors.push('Valid urgency level is required (critical, urgent, normal)');
  }

  // Validate location
  if (!requestData.location || !validateLocation(requestData.location)) {
    errors.push('Valid location with coordinates and address is required');
  }

  // Validate contact info
  const contactValidation = validateContactInfo(requestData.contactInfo);
  if (!contactValidation.isValid) {
    errors.push(...contactValidation.errors);
  }

  // Validate description if provided
  if (requestData.description && requestData.description.length > 500) {
    errors.push('Description must be less than 500 characters');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates contact information
 * @param contactInfo - The contact info to validate
 * @returns Object with isValid boolean and errors array
 */
export function validateContactInfo(contactInfo: ContactInfo): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!contactInfo) {
    errors.push('Contact information is required');
    return { isValid: false, errors };
  }

  // Validate name
  if (!contactInfo.name || typeof contactInfo.name !== 'string') {
    errors.push('Contact name is required');
  } else if (contactInfo.name.trim().length < 2) {
    errors.push('Contact name must be at least 2 characters long');
  }

  // Validate phone
  if (!contactInfo.phone || typeof contactInfo.phone !== 'string') {
    errors.push('Contact phone is required');
  } else if (!/^\+[1-9]\d{1,14}$/.test(contactInfo.phone)) {
    errors.push('Contact phone must be in international format (e.g., +1234567890)');
  }

  // Validate preferred contact
  if (!contactInfo.preferredContact || !Object.values(ContactPreference).includes(contactInfo.preferredContact)) {
    errors.push('Valid preferred contact method is required (call, text)');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Calculates expiration time based on urgency level
 * @param urgency - The urgency level
 * @param createdAt - The creation timestamp
 * @returns Expiration timestamp
 */
export function calculateExpirationTime(urgency: UrgencyLevel, createdAt: string): string {
  const created = new Date(createdAt);
  let hoursToAdd: number;

  switch (urgency) {
    case UrgencyLevel.CRITICAL:
      hoursToAdd = 6; // 6 hours for critical
      break;
    case UrgencyLevel.URGENT:
      hoursToAdd = 24; // 24 hours for urgent
      break;
    case UrgencyLevel.NORMAL:
      hoursToAdd = 72; // 72 hours for normal
      break;
    default:
      hoursToAdd = 24;
  }

  const expirationTime = new Date(created.getTime() + (hoursToAdd * 60 * 60 * 1000));
  return expirationTime.toISOString();
}

/**
 * Creates a new blood request object with default values
 * @param requestData - The blood request creation data
 * @returns Complete BloodRequest object
 */
export function createBloodRequest(requestData: CreateBloodRequestData): BloodRequest {
  const now = new Date().toISOString();
  const urgency = requestData.urgency as UrgencyLevel;
  
  return {
    id: '', // Will be set by database
    requesterId: requestData.requesterId,
    bloodType: requestData.bloodType as BloodType,
    quantity: requestData.quantity.trim(),
    urgency,
    location: requestData.location,
    contactInfo: requestData.contactInfo,
    description: requestData.description?.trim(),
    status: RequestStatus.ACTIVE,
    createdAt: now,
    expiresAt: calculateExpirationTime(urgency, now),
    responses: [],
    notifiedDonors: []
  };
}

/**
 * Checks if a blood request has expired
 * @param request - The blood request to check
 * @returns boolean indicating if request has expired
 */
export function isRequestExpired(request: BloodRequest): boolean {
  return new Date() > new Date(request.expiresAt);
}

/**
 * Checks if a blood request is still active
 * @param request - The blood request to check
 * @returns boolean indicating if request is active
 */
export function isRequestActive(request: BloodRequest): boolean {
  return request.status === RequestStatus.ACTIVE && !isRequestExpired(request);
}