/**
 * User model interface and validation
 */

import { BloodType, UserType } from '../types/enums';
import { Location, validateLocation } from '../types/location';
import { validateBloodType } from '../types/bloodCompatibility';

/**
 * Donor statistics interface
 */
export interface DonorStats {
  totalDonations: number;
  lastDonationDate?: string;
  rating: number;
  reviewCount: number;
  badges: string[];
}

/**
 * Notification settings interface
 */
export interface NotificationSettings {
  fcmToken?: string;
  pushEnabled: boolean;
  maxDistance: number; // in kilometers
  urgencyLevels: string[];
}

/**
 * User profile interface
 */
export interface UserProfile {
  name: string;
  bloodType: BloodType;
  location: Location;
  isAvailable: boolean;
  userType: UserType;
  joinedAt: string;
  lastActive: string;
}

/**
 * Complete User interface
 */
export interface User {
  id: string;
  phoneNumber: string;
  profile: UserProfile;
  donorStats?: DonorStats;
  notificationSettings: NotificationSettings;
}

/**
 * User creation data interface (for registration)
 */
export interface CreateUserData {
  phoneNumber: string;
  name: string;
  bloodType: string;
  location: Location;
  userType: string;
  fcmToken?: string;
}

/**
 * User update data interface
 */
export interface UpdateUserData {
  name?: string;
  location?: Location;
  isAvailable?: boolean;
  userType?: UserType;
  notificationSettings?: Partial<NotificationSettings>;
}

/**
 * Validates user creation data
 * @param userData - The user data to validate
 * @returns Object with isValid boolean and errors array
 */
export function validateCreateUserData(userData: CreateUserData): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate phone number
  if (!userData.phoneNumber || typeof userData.phoneNumber !== 'string') {
    errors.push('Phone number is required');
  } else if (!/^\+[1-9]\d{1,14}$/.test(userData.phoneNumber)) {
    errors.push('Phone number must be in international format (e.g., +1234567890)');
  }

  // Validate name
  if (!userData.name || typeof userData.name !== 'string') {
    errors.push('Name is required');
  } else if (userData.name.trim().length < 2) {
    errors.push('Name must be at least 2 characters long');
  } else if (userData.name.trim().length > 100) {
    errors.push('Name must be less than 100 characters');
  }

  // Validate blood type
  if (!userData.bloodType || !validateBloodType(userData.bloodType)) {
    errors.push('Valid blood type is required (A+, A-, B+, B-, AB+, AB-, O+, O-)');
  }

  // Validate location
  if (!userData.location || !validateLocation(userData.location)) {
    errors.push('Valid location with coordinates and address is required');
  }

  // Validate user type
  if (!userData.userType || !Object.values(UserType).includes(userData.userType as UserType)) {
    errors.push('Valid user type is required (donor, recipient, both)');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates user profile data
 * @param profile - The user profile to validate
 * @returns Object with isValid boolean and errors array
 */
export function validateUserProfile(profile: UserProfile): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate name
  if (!profile.name || profile.name.trim().length < 2) {
    errors.push('Name must be at least 2 characters long');
  }

  // Validate blood type
  if (!validateBloodType(profile.bloodType)) {
    errors.push('Invalid blood type');
  }

  // Validate location
  if (!validateLocation(profile.location)) {
    errors.push('Invalid location data');
  }

  // Validate user type
  if (!Object.values(UserType).includes(profile.userType)) {
    errors.push('Invalid user type');
  }

  // Validate dates
  if (!profile.joinedAt || isNaN(Date.parse(profile.joinedAt))) {
    errors.push('Invalid joinedAt date');
  }

  if (!profile.lastActive || isNaN(Date.parse(profile.lastActive))) {
    errors.push('Invalid lastActive date');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Creates a new user object with default values
 * @param userData - The user creation data
 * @returns Complete User object
 */
export function createUser(userData: CreateUserData): User {
  const now = new Date().toISOString();
  
  return {
    id: '', // Will be set by database
    phoneNumber: userData.phoneNumber,
    profile: {
      name: userData.name.trim(),
      bloodType: userData.bloodType as BloodType,
      location: userData.location,
      isAvailable: true,
      userType: userData.userType as UserType,
      joinedAt: now,
      lastActive: now
    },
    donorStats: userData.userType === UserType.DONOR || userData.userType === UserType.BOTH ? {
      totalDonations: 0,
      rating: 0,
      reviewCount: 0,
      badges: []
    } : undefined,
    notificationSettings: {
      fcmToken: userData.fcmToken,
      pushEnabled: true,
      maxDistance: 25, // Default 25km radius
      urgencyLevels: ['critical', 'urgent', 'normal']
    }
  };
}