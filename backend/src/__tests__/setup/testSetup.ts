/**
 * Test setup and utilities for integration tests
 */

import { firebaseService } from '../../services/firebaseService';
import { appwriteService } from '../../services/appwriteService';

// Mock Firebase Admin SDK for testing
jest.mock('../../config/firebase', () => ({
  initializeFirebase: jest.fn(),
  getAuth: jest.fn(() => ({
    verifyIdToken: jest.fn().mockResolvedValue({
      uid: 'test-user-id',
      phone_number: '+1234567890',
      email: '<EMAIL>'
    })
  })),
  getFirestore: jest.fn(() => ({
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            id: 'test-user-id',
            profile: {
              name: 'Test User',
              bloodType: 'O+',
              userType: 'donor'
            }
          })
        }),
        set: jest.fn().mockResolvedValue({}),
        update: jest.fn().mockResolvedValue({}),
        delete: jest.fn().mockResolvedValue({})
      })),
      add: jest.fn().mockResolvedValue({
        id: 'test-doc-id'
      }),
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      get: jest.fn().mockResolvedValue({
        docs: []
      })
    }))
  }))
}));

// Mock Appwrite SDK for testing
jest.mock('../../config/appwrite', () => ({
  initializeAppwrite: jest.fn(),
  getUsers: jest.fn(() => ({
    get: jest.fn().mockResolvedValue({
      $id: 'test-user-id',
      phone: '+1234567890',
      email: '<EMAIL>'
    }),
    create: jest.fn().mockResolvedValue({
      $id: 'test-user-id'
    }),
    update: jest.fn().mockResolvedValue({
      $id: 'test-user-id'
    }),
    delete: jest.fn().mockResolvedValue({})
  })),
  getDatabases: jest.fn(() => ({
    getDocument: jest.fn().mockResolvedValue({
      $id: 'test-doc-id',
      data: {}
    }),
    createDocument: jest.fn().mockResolvedValue({
      $id: 'test-doc-id'
    }),
    updateDocument: jest.fn().mockResolvedValue({
      $id: 'test-doc-id'
    }),
    deleteDocument: jest.fn().mockResolvedValue({}),
    listDocuments: jest.fn().mockResolvedValue({
      documents: []
    })
  }))
}));

// Mock Firebase service
jest.mock('../../services/firebaseService', () => ({
  firebaseService: {
    verifyIdToken: jest.fn().mockResolvedValue({
      uid: 'test-user-id',
      phone_number: '+1234567890',
      email: '<EMAIL>'
    }),
    getUserById: jest.fn().mockResolvedValue({
      id: 'test-user-id',
      profile: {
        name: 'Test User',
        bloodType: 'O+',
        userType: 'donor',
        location: {
          coordinates: { latitude: 40.7128, longitude: -74.0060 },
          address: 'New York, NY'
        },
        isAvailable: true
      }
    }),
    createUser: jest.fn().mockResolvedValue({
      id: 'test-user-id'
    }),
    updateUser: jest.fn().mockResolvedValue({
      id: 'test-user-id'
    }),
    deleteUser: jest.fn().mockResolvedValue({}),
    createBloodRequest: jest.fn().mockResolvedValue({
      id: 'test-request-id'
    }),
    getBloodRequestById: jest.fn().mockResolvedValue({
      id: 'test-request-id',
      bloodType: 'A+',
      urgency: 'urgent'
    }),
    updateBloodRequest: jest.fn().mockResolvedValue({
      id: 'test-request-id'
    }),
    deleteBloodRequest: jest.fn().mockResolvedValue({}),
    getBloodRequests: jest.fn().mockResolvedValue([])
  }
}));

// Mock Appwrite service
jest.mock('../../services/appwriteService', () => ({
  appwriteService: {
    getUserById: jest.fn().mockResolvedValue({
      id: 'test-user-id',
      profile: {
        name: 'Test User',
        bloodType: 'O+',
        userType: 'donor'
      }
    }),
    createUser: jest.fn().mockResolvedValue({
      id: 'test-user-id'
    }),
    updateUser: jest.fn().mockResolvedValue({
      id: 'test-user-id'
    }),
    deleteUser: jest.fn().mockResolvedValue({})
  }
}));

// Mock notification services
jest.mock('../../services/notificationService', () => ({
  firebaseNotificationService: {
    sendNotification: jest.fn().mockResolvedValue({
      success: true,
      messageId: 'test-message-id'
    }),
    sendBatchNotifications: jest.fn().mockResolvedValue({
      successCount: 1,
      failureCount: 0,
      results: [{ success: true, messageId: 'test-message-id', token: 'test-token' }]
    }),
    notifyDonorsOfBloodRequest: jest.fn().mockResolvedValue({
      successCount: 1,
      failureCount: 0,
      results: [{ success: true, messageId: 'test-message-id', token: 'test-token' }]
    }),
    notifyRecipientOfDonorResponse: jest.fn().mockResolvedValue({
      success: true,
      messageId: 'test-message-id',
      token: 'test-token'
    }),
    getNotificationStats: jest.fn().mockResolvedValue({
      totalSent: 10,
      totalDelivered: 9,
      totalFailed: 1,
      deliveryRate: 0.9
    })
  }
}));

jest.mock('../../services/appwriteNotificationService', () => ({
  appwriteNotificationService: {
    sendNotification: jest.fn().mockResolvedValue({
      success: true,
      messageId: 'test-message-id'
    }),
    sendBatchNotifications: jest.fn().mockResolvedValue({
      successCount: 1,
      failureCount: 0,
      results: [{ success: true, messageId: 'test-message-id', token: 'test-token' }]
    }),
    notifyDonorsOfBloodRequest: jest.fn().mockResolvedValue({
      successCount: 1,
      failureCount: 0,
      results: [{ success: true, messageId: 'test-message-id', token: 'test-token' }]
    }),
    notifyRecipientOfDonorResponse: jest.fn().mockResolvedValue({
      success: true,
      messageId: 'test-message-id',
      token: 'test-token'
    })
  }
}));

// Mock geo-matching service
jest.mock('../../services/geoMatchingService', () => ({
  geoMatchingService: {
    findNearbyDonors: jest.fn().mockResolvedValue([]),
    findDonorsForRequest: jest.fn().mockResolvedValue([]),
    validateCompatibility: jest.fn().mockReturnValue(true),
    getMatchingStats: jest.fn().mockReturnValue({
      totalMatches: 0,
      exactMatches: 0,
      compatibleMatches: 0,
      averageDistance: 0,
      averageRating: 0
    })
  }
}));

// Test data generators
export const generateTestUser = (overrides = {}) => ({
  id: 'test-user-id',
  phoneNumber: '+1234567890',
  profile: {
    name: 'Test User',
    bloodType: 'O+',
    userType: 'donor',
    location: {
      coordinates: { latitude: 40.7128, longitude: -74.0060 },
      address: 'New York, NY'
    },
    isAvailable: true,
    joinedAt: new Date().toISOString(),
    lastActive: new Date().toISOString()
  },
  ...overrides
});

export const generateTestBloodRequest = (overrides = {}) => ({
  id: 'test-request-id',
  requesterId: 'test-user-id',
  bloodType: 'A+',
  quantity: '2 units',
  urgency: 'urgent',
  location: {
    coordinates: { latitude: 40.7128, longitude: -74.0060 },
    address: 'New York, NY'
  },
  contactInfo: {
    name: 'Test Requester',
    phone: '+1987654321',
    preferredContact: 'call'
  },
  description: 'Test blood request',
  status: 'active',
  createdAt: new Date().toISOString(),
  expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  responses: [],
  notifiedDonors: [],
  ...overrides
});

// Test utilities
export const createAuthToken = (userId = 'test-user-id') => {
  // In a real test environment, you might use a JWT library to create a valid token
  // For now, we'll return a mock token
  return `mock-token-${userId}`;
};

export const cleanupTestData = async () => {
  // Cleanup test data from databases
  // This would be implemented based on your database setup
  console.log('Cleaning up test data...');
};

// Setup and teardown
beforeAll(async () => {
  // Global test setup
  process.env.NODE_ENV = 'test';
  process.env.FIREBASE_PROJECT_ID = 'test-project';
  process.env.APPWRITE_PROJECT_ID = 'test-project';
});

afterAll(async () => {
  // Global test cleanup
  await cleanupTestData();
});

beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks();
});

export default {
  generateTestUser,
  generateTestBloodRequest,
  createAuthToken,
  cleanupTestData
};