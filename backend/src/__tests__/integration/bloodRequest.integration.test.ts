/**
 * Integration tests for Blood Request and Donor Matching Flow
 * Tests the complete workflow from request creation to donor notification
 */

import request from 'supertest';
import app from '../../server';
import { BloodType, UrgencyLevel, UserType, ContactPreference } from '../../types/enums';
import { generateTestUser, generateTestBloodRequest, createAuthToken } from '../setup/testSetup';

describe('Blood Request and Donor Matching Integration', () => {
  let recipientToken: string;
  let donorToken: string;
  let bloodRequestId: string;
  let recipientId: string;
  let donorId: string;

  beforeAll(async () => {
    // Create test users
    recipientToken = createAuthToken('recipient-user-id');
    donorToken = createAuthToken('donor-user-id');
    recipientId = 'recipient-user-id';
    donorId = 'donor-user-id';
  });

  describe('Complete Blood Request Workflow', () => {
    it('should create recipient profile', async () => {
      const recipientData = {
        name: '<PERSON> Recipient',
        bloodType: BloodType.A_POSITIVE,
        userType: UserType.RECIPIENT,
        location: {
          coordinates: {
            latitude: 40.7128,
            longitude: -74.0060
          },
          address: 'New York, NY'
        },
        isAvailable: false
      };

      const response = await request(app)
        .post('/api/users/profile')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(recipientData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.user.profile).toHaveProperty('name', 'Jane Recipient');
      expect(response.body.user.profile).toHaveProperty('userType', UserType.RECIPIENT);
    });

    it('should create donor profile', async () => {
      const donorData = {
        name: 'John Donor',
        bloodType: BloodType.O_NEGATIVE, // Universal donor
        userType: UserType.DONOR,
        location: {
          coordinates: {
            latitude: 40.7589,
            longitude: -73.9851
          },
          address: 'Manhattan, NY'
        },
        isAvailable: true
      };

      const response = await request(app)
        .post('/api/users/profile')
        .set('Authorization', `Bearer ${donorToken}`)
        .send(donorData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.user.profile).toHaveProperty('name', 'John Donor');
      expect(response.body.user.profile).toHaveProperty('userType', UserType.DONOR);
      expect(response.body.user.profile).toHaveProperty('isAvailable', true);
    });

    it('should create urgent blood request', async () => {
      const requestData = {
        bloodType: BloodType.A_POSITIVE,
        quantity: '3 units',
        urgency: UrgencyLevel.CRITICAL,
        location: {
          coordinates: {
            latitude: 40.7128,
            longitude: -74.0060
          },
          address: 'New York Presbyterian Hospital, NY'
        },
        contactInfo: {
          name: 'Jane Recipient',
          phone: '+1555123456',
          preferredContact: ContactPreference.CALL
        },
        description: 'Emergency surgery - need A+ blood urgently'
      };

      const response = await request(app)
        .post('/api/requests')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(requestData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.request).toHaveProperty('bloodType', BloodType.A_POSITIVE);
      expect(response.body.request).toHaveProperty('urgency', UrgencyLevel.CRITICAL);
      expect(response.body.request).toHaveProperty('status', 'active');

      bloodRequestId = response.body.request.id;
    });

    it('should find compatible donors for the request', async () => {
      const matchingParams = {
        bloodType: BloodType.A_POSITIVE,
        location: {
          latitude: 40.7128,
          longitude: -74.0060
        },
        urgency: UrgencyLevel.CRITICAL,
        maxRadius: 50 // Expanded radius for critical request
      };

      const response = await request(app)
        .post('/api/matching/find-donors')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(matchingParams)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('matches');
      expect(response.body).toHaveProperty('stats');
      expect(response.body.stats).toHaveProperty('totalMatches');

      // O- donor should be compatible with A+ recipient
      if (response.body.matches.length > 0) {
        const match = response.body.matches[0];
        expect(match).toHaveProperty('donor');
        expect(match).toHaveProperty('distance');
        expect(match).toHaveProperty('compatibility');
      }
    });

    it('should validate blood type compatibility', async () => {
      const compatibilityTests = [
        {
          donor: BloodType.O_NEGATIVE,
          recipient: BloodType.A_POSITIVE,
          expected: true
        },
        {
          donor: BloodType.A_POSITIVE,
          recipient: BloodType.A_POSITIVE,
          expected: true
        },
        {
          donor: BloodType.B_POSITIVE,
          recipient: BloodType.A_POSITIVE,
          expected: false
        }
      ];

      for (const test of compatibilityTests) {
        const response = await request(app)
          .post('/api/matching/validate-compatibility')
          .set('Authorization', `Bearer ${recipientToken}`)
          .send({
            donorBloodType: test.donor,
            recipientBloodType: test.recipient
          })
          .expect(200);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('compatible', test.expected);
      }
    });

    it('should send notifications to compatible donors', async () => {
      const notificationData = {
        requestId: bloodRequestId,
        donorTokens: ['mock-fcm-token-donor-1', 'mock-fcm-token-donor-2']
      };

      const response = await request(app)
        .post('/api/notifications/blood-request')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(notificationData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('notificationsSent');
      expect(response.body).toHaveProperty('notificationsFailed');
    });

    it('should allow donor to respond to blood request', async () => {
      const donorResponse = {
        response: 'yes',
        message: 'I can help! Available immediately.',
        estimatedArrival: '30 minutes'
      };

      const response = await request(app)
        .post(`/api/requests/${bloodRequestId}/respond`)
        .set('Authorization', `Bearer ${donorToken}`)
        .send(donorResponse)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
    });

    it('should get responses for blood request', async () => {
      const response = await request(app)
        .get(`/api/requests/${bloodRequestId}/responses`)
        .set('Authorization', `Bearer ${recipientToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('responses');
      expect(Array.isArray(response.body.responses)).toBe(true);
    });

    it('should notify recipient of donor response', async () => {
      const notificationData = {
        recipientToken: 'mock-fcm-token-recipient',
        donorId: donorId,
        response: 'yes',
        requestId: bloodRequestId
      };

      const response = await request(app)
        .post('/api/notifications/donor-response')
        .set('Authorization', `Bearer ${donorToken}`)
        .send(notificationData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('notificationSent', true);
    });

    it('should update request status to fulfilled', async () => {
      const fulfillmentData = {
        status: 'fulfilled',
        fulfilledBy: donorId,
        fulfillmentNotes: 'Blood donation completed successfully'
      };

      const response = await request(app)
        .put(`/api/requests/${bloodRequestId}/fulfill`)
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(fulfillmentData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.request).toHaveProperty('status', 'fulfilled');
      expect(response.body.request).toHaveProperty('fulfilledBy', donorId);
    });

    it('should send fulfillment notifications to other donors', async () => {
      const notificationData = {
        requestId: bloodRequestId,
        excludeDonorId: donorId,
        notifiedDonorTokens: ['mock-fcm-token-donor-2', 'mock-fcm-token-donor-3']
      };

      const response = await request(app)
        .post('/api/notifications/request-fulfilled')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(notificationData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('notificationsSent');
    });
  });

  describe('Request Cancellation Flow', () => {
    let cancellationRequestId: string;

    it('should create another blood request for cancellation test', async () => {
      const requestData = {
        bloodType: BloodType.B_NEGATIVE,
        quantity: '1 unit',
        urgency: UrgencyLevel.NORMAL,
        location: {
          coordinates: {
            latitude: 40.7128,
            longitude: -74.0060
          },
          address: 'New York, NY'
        },
        contactInfo: {
          name: 'Jane Recipient',
          phone: '+1555123456',
          preferredContact: ContactPreference.TEXT
        },
        description: 'Planned surgery next week'
      };

      const response = await request(app)
        .post('/api/requests')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(requestData)
        .expect(201);

      cancellationRequestId = response.body.request.id;
    });

    it('should cancel blood request', async () => {
      const cancellationData = {
        reason: 'Surgery postponed',
        notifyDonors: true,
        notifiedDonorTokens: ['mock-fcm-token-donor-1']
      };

      const response = await request(app)
        .delete(`/api/requests/${cancellationRequestId}`)
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(cancellationData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
    });

    it('should send cancellation notifications', async () => {
      const notificationData = {
        requestId: cancellationRequestId,
        notifiedDonorTokens: ['mock-fcm-token-donor-1'],
        reason: 'Surgery postponed'
      };

      const response = await request(app)
        .post('/api/notifications/request-cancelled')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(notificationData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('notificationsSent');
    });
  });

  describe('Donor Availability Management', () => {
    it('should update donor availability status', async () => {
      const availabilityData = {
        isAvailable: false,
        unavailableReason: 'Recently donated',
        availableAgainAt: new Date(Date.now() + 8 * 7 * 24 * 60 * 60 * 1000).toISOString() // 8 weeks
      };

      const response = await request(app)
        .put('/api/users/availability')
        .set('Authorization', `Bearer ${donorToken}`)
        .send(availabilityData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.user.profile).toHaveProperty('isAvailable', false);
    });

    it('should not include unavailable donors in matching', async () => {
      const matchingParams = {
        bloodType: BloodType.A_POSITIVE,
        location: {
          latitude: 40.7128,
          longitude: -74.0060
        },
        urgency: UrgencyLevel.URGENT,
        maxRadius: 25
      };

      const response = await request(app)
        .post('/api/matching/find-donors')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(matchingParams)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      
      // Should not include the unavailable donor
      const unavailableDonor = response.body.matches.find(
        (match: any) => match.donor.id === donorId
      );
      expect(unavailableDonor).toBeUndefined();
    });

    it('should restore donor availability', async () => {
      const availabilityData = {
        isAvailable: true,
        unavailableReason: null,
        availableAgainAt: null
      };

      const response = await request(app)
        .put('/api/users/availability')
        .set('Authorization', `Bearer ${donorToken}`)
        .send(availabilityData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.user.profile).toHaveProperty('isAvailable', true);
    });
  });

  describe('Notification Preferences', () => {
    it('should update donor notification preferences', async () => {
      const preferencesData = {
        pushEnabled: true,
        maxDistance: 15, // Reduced from default
        urgencyLevels: ['critical'], // Only critical requests
        quietHours: {
          enabled: true,
          start: '22:00',
          end: '08:00'
        }
      };

      const response = await request(app)
        .put('/api/notifications/preferences')
        .set('Authorization', `Bearer ${donorToken}`)
        .send(preferencesData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.preferences).toHaveProperty('maxDistance', 15);
      expect(response.body.preferences.urgencyLevels).toContain('critical');
    });

    it('should respect notification preferences in matching', async () => {
      // Create a normal urgency request (should not notify donor who only wants critical)
      const requestData = {
        bloodType: BloodType.A_POSITIVE,
        quantity: '1 unit',
        urgency: UrgencyLevel.NORMAL,
        location: {
          coordinates: {
            latitude: 40.7589,
            longitude: -73.9851
          },
          address: 'Manhattan, NY'
        },
        contactInfo: {
          name: 'Test Requester',
          phone: '+1555987654',
          preferredContact: ContactPreference.CALL
        },
        description: 'Routine blood transfusion'
      };

      const response = await request(app)
        .post('/api/requests')
        .set('Authorization', `Bearer ${recipientToken}`)
        .send(requestData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      
      // The donor should not be notified for normal urgency requests
      // based on their preferences
    });
  });

  describe('Request History and Analytics', () => {
    it('should get user request history', async () => {
      const response = await request(app)
        .get('/api/requests/history')
        .set('Authorization', `Bearer ${recipientToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('requests');
      expect(Array.isArray(response.body.requests)).toBe(true);
    });

    it('should get donor response history', async () => {
      const response = await request(app)
        .get('/api/users/donation-history')
        .set('Authorization', `Bearer ${donorToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('donations');
      expect(response.body).toHaveProperty('stats');
    });

    it('should get matching statistics', async () => {
      const response = await request(app)
        .get('/api/matching/stats')
        .set('Authorization', `Bearer ${recipientToken}`)
        .query({
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          endDate: new Date().toISOString()
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('stats');
      expect(response.body.stats).toHaveProperty('totalRequests');
      expect(response.body.stats).toHaveProperty('averageResponseTime');
      expect(response.body.stats).toHaveProperty('fulfillmentRate');
    });
  });
});