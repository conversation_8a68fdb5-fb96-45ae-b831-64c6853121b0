/**
 * Integration tests for UBLOOD Clone API
 * Tests complete user flows and API endpoint interactions
 */

import request from 'supertest';
import app from '../../server';
import { BloodType, UrgencyLevel, UserType, ContactPreference } from '../../types/enums';

describe('API Integration Tests', () => {
  let authToken: string;
  let userId: string;
  let bloodRequestId: string;

  // Mock Firebase token for testing
  const mockFirebaseToken = 'mock-firebase-token';

  beforeAll(async () => {
    // Setup test environment
    process.env.NODE_ENV = 'test';
  });

  afterAll(async () => {
    // Cleanup test data if needed
  });

  describe('System Health and Info', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'OK');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
      expect(response.body).toHaveProperty('environment');
    });

    it('should return API information', async () => {
      const response = await request(app)
        .get('/api')
        .expect(200);

      expect(response.body).toHaveProperty('message', 'UBLOOD Clone API');
      expect(response.body).toHaveProperty('version', '1.0.0');
      expect(response.body).toHaveProperty('status', 'running');
      expect(response.body).toHaveProperty('endpoints');
      expect(response.body.endpoints).toHaveProperty('auth', '/api/auth');
      expect(response.body.endpoints).toHaveProperty('users', '/api/users');
      expect(response.body.endpoints).toHaveProperty('requests', '/api/requests');
    });
  });

  describe('Authentication Flow', () => {
    it('should handle phone number verification request', async () => {
      const phoneNumber = '+1234567890';

      const response = await request(app)
        .post('/api/auth/firebase/verify-phone')
        .send({ phoneNumber })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
    });

    it('should handle OTP verification', async () => {
      const verificationData = {
        phoneNumber: '+1234567890',
        otp: '123456'
      };

      const response = await request(app)
        .post('/api/auth/firebase/verify-otp')
        .send(verificationData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('token');
      
      // Store token for subsequent tests
      authToken = response.body.token;
    });

    it('should reject requests without authentication', async () => {
      await request(app)
        .get('/api/users/profile')
        .expect(401);
    });

    it('should reject requests with invalid token', async () => {
      await request(app)
        .get('/api/users/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('User Profile Management', () => {
    it('should create user profile', async () => {
      const profileData = {
        name: 'John Doe',
        bloodType: BloodType.O_POSITIVE,
        userType: UserType.BOTH,
        location: {
          coordinates: {
            latitude: 40.7128,
            longitude: -74.0060
          },
          address: 'New York, NY'
        },
        isAvailable: true
      };

      const response = await request(app)
        .post('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(profileData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('id');
      expect(response.body.user.profile).toHaveProperty('name', 'John Doe');
      expect(response.body.user.profile).toHaveProperty('bloodType', BloodType.O_POSITIVE);

      userId = response.body.user.id;
    });

    it('should get user profile', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('id', userId);
      expect(response.body.user.profile).toHaveProperty('name', 'John Doe');
    });

    it('should update user profile', async () => {
      const updateData = {
        name: 'John Smith',
        isAvailable: false
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.user.profile).toHaveProperty('name', 'John Smith');
      expect(response.body.user.profile).toHaveProperty('isAvailable', false);
    });

    it('should update user location', async () => {
      const locationData = {
        coordinates: {
          latitude: 40.7589,
          longitude: -73.9851
        },
        address: 'Manhattan, NY'
      };

      const response = await request(app)
        .put('/api/users/location')
        .set('Authorization', `Bearer ${authToken}`)
        .send(locationData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.user.profile.location).toHaveProperty('address', 'Manhattan, NY');
    });
  });

  describe('Blood Request Management', () => {
    it('should create blood request', async () => {
      const requestData = {
        bloodType: BloodType.A_POSITIVE,
        quantity: '2 units',
        urgency: UrgencyLevel.URGENT,
        location: {
          coordinates: {
            latitude: 40.7128,
            longitude: -74.0060
          },
          address: 'New York, NY'
        },
        contactInfo: {
          name: 'Jane Doe',
          phone: '+1987654321',
          preferredContact: ContactPreference.CALL
        },
        description: 'Emergency surgery needed'
      };

      const response = await request(app)
        .post('/api/requests')
        .set('Authorization', `Bearer ${authToken}`)
        .send(requestData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('request');
      expect(response.body.request).toHaveProperty('id');
      expect(response.body.request).toHaveProperty('bloodType', BloodType.A_POSITIVE);
      expect(response.body.request).toHaveProperty('urgency', UrgencyLevel.URGENT);

      bloodRequestId = response.body.request.id;
    });

    it('should get blood request by ID', async () => {
      const response = await request(app)
        .get(`/api/requests/${bloodRequestId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('request');
      expect(response.body.request).toHaveProperty('id', bloodRequestId);
      expect(response.body.request).toHaveProperty('bloodType', BloodType.A_POSITIVE);
    });

    it('should list blood requests', async () => {
      const response = await request(app)
        .get('/api/requests')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('requests');
      expect(Array.isArray(response.body.requests)).toBe(true);
      expect(response.body.requests.length).toBeGreaterThan(0);
    });

    it('should update blood request', async () => {
      const updateData = {
        description: 'Updated: Emergency surgery needed urgently',
        urgency: UrgencyLevel.CRITICAL
      };

      const response = await request(app)
        .put(`/api/requests/${bloodRequestId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.request).toHaveProperty('urgency', UrgencyLevel.CRITICAL);
      expect(response.body.request).toHaveProperty('description', updateData.description);
    });

    it('should handle blood request response', async () => {
      const responseData = {
        response: 'yes',
        message: 'I can help with the blood donation'
      };

      const response = await request(app)
        .post(`/api/requests/${bloodRequestId}/respond`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(responseData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
    });
  });

  describe('Donor Matching', () => {
    it('should find compatible donors', async () => {
      const matchingParams = {
        bloodType: BloodType.A_POSITIVE,
        location: {
          latitude: 40.7128,
          longitude: -74.0060
        },
        urgency: UrgencyLevel.URGENT,
        maxRadius: 25
      };

      const response = await request(app)
        .post('/api/matching/find-donors')
        .set('Authorization', `Bearer ${authToken}`)
        .send(matchingParams)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('matches');
      expect(Array.isArray(response.body.matches)).toBe(true);
      expect(response.body).toHaveProperty('stats');
    });

    it('should validate blood type compatibility', async () => {
      const compatibilityData = {
        donorBloodType: BloodType.O_NEGATIVE,
        recipientBloodType: BloodType.A_POSITIVE
      };

      const response = await request(app)
        .post('/api/matching/validate-compatibility')
        .set('Authorization', `Bearer ${authToken}`)
        .send(compatibilityData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('compatible', true);
    });

    it('should get matching statistics', async () => {
      const response = await request(app)
        .get('/api/matching/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('stats');
      expect(response.body.stats).toHaveProperty('totalMatches');
      expect(response.body.stats).toHaveProperty('averageDistance');
    });
  });

  describe('Notification System', () => {
    it('should send blood request notification', async () => {
      const notificationData = {
        requestId: bloodRequestId,
        donorTokens: ['mock-fcm-token-1', 'mock-fcm-token-2']
      };

      const response = await request(app)
        .post('/api/notifications/blood-request')
        .set('Authorization', `Bearer ${authToken}`)
        .send(notificationData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('notificationsSent');
      expect(response.body).toHaveProperty('notificationsFailed');
    });

    it('should get notification history', async () => {
      const response = await request(app)
        .get('/api/notifications/history')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('notifications');
      expect(Array.isArray(response.body.notifications)).toBe(true);
    });

    it('should update notification preferences', async () => {
      const preferencesData = {
        pushEnabled: true,
        maxDistance: 30,
        urgencyLevels: ['critical', 'urgent']
      };

      const response = await request(app)
        .put('/api/notifications/preferences')
        .set('Authorization', `Bearer ${authToken}`)
        .send(preferencesData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('preferences');
      expect(response.body.preferences).toHaveProperty('pushEnabled', true);
      expect(response.body.preferences).toHaveProperty('maxDistance', 30);
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/api/non-existent-endpoint')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'NOT_FOUND');
    });

    it('should handle validation errors', async () => {
      const invalidRequestData = {
        bloodType: 'INVALID_BLOOD_TYPE',
        quantity: '',
        urgency: 'INVALID_URGENCY'
      };

      const response = await request(app)
        .post('/api/requests')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidRequestData)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    it('should handle server errors gracefully', async () => {
      // This would test error handling for server errors
      // In a real scenario, you might mock a service to throw an error
      const response = await request(app)
        .get('/api/requests/invalid-id-format')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      // Make multiple requests quickly to test rate limiting
      const requests = Array(10).fill(null).map(() =>
        request(app)
          .get('/health')
          .expect(200)
      );

      const responses = await Promise.all(requests);
      
      // All requests should succeed within normal rate limits
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });
  });

  describe('CORS and Security', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      // Check for security headers set by helmet
      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-frame-options');
    });

    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/api/users/profile')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'GET')
        .set('Access-Control-Request-Headers', 'Authorization')
        .expect(204);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
      expect(response.headers).toHaveProperty('access-control-allow-methods');
    });
  });

  describe('Data Validation and Sanitization', () => {
    it('should validate required fields', async () => {
      const incompleteProfileData = {
        name: 'John Doe'
        // Missing required fields like bloodType, userType
      };

      const response = await request(app)
        .post('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(incompleteProfileData)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
    });

    it('should sanitize input data', async () => {
      const profileDataWithScript = {
        name: '<script>alert("xss")</script>John Doe',
        bloodType: BloodType.O_POSITIVE,
        userType: UserType.DONOR,
        location: {
          coordinates: {
            latitude: 40.7128,
            longitude: -74.0060
          },
          address: 'New York, NY'
        }
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(profileDataWithScript)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      // The name should be sanitized to remove script tags
      expect(response.body.user.profile.name).not.toContain('<script>');
    });
  });

  describe('Cleanup', () => {
    it('should cancel blood request', async () => {
      const response = await request(app)
        .delete(`/api/requests/${bloodRequestId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
    });

    it('should delete user profile', async () => {
      const response = await request(app)
        .delete('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
    });
  });
});