#!/usr/bin/env node

/**
 * Database Cleanup Script
 * 
 * This script cleans up test data from both Firebase and Appwrite databases.
 * It can be used to reset the database state during development and testing.
 * 
 * Usage:
 *   npm run cleanup:test-data
 *   or
 *   npx ts-node src/scripts/cleanupTestData.ts [options]
 * 
 * Options:
 *   --firebase-only    Clean only Firebase data
 *   --appwrite-only    Clean only Appwrite data
 *   --dry-run         Show what would be deleted without actually deleting
 *   --confirm         Skip confirmation prompt
 */

import { firebaseService } from '../services/firebaseService';
import { appwriteService } from '../services/appwriteService';
import { initializeFirebase } from '../config/firebase';
import { initializeAppwrite, APPWRITE_DATABASE_ID, COLLECTIONS } from '../config/appwrite';
import { getDatabases } from '../config/appwrite';

interface CleanupOptions {
  firebaseOnly?: boolean;
  appwriteOnly?: boolean;
  dryRun?: boolean;
  confirm?: boolean;
}

interface CleanupStats {
  firebase: {
    users: number;
    bloodRequests: number;
    notifications: number;
    errors: string[];
  };
  appwrite: {
    users: number;
    bloodRequests: number;
    notifications: number;
    userResponses: number;
    analytics: number;
    errors: string[];
  };
}

class DatabaseCleanup {
  private options: CleanupOptions;
  private stats: CleanupStats;

  constructor(options: CleanupOptions = {}) {
    this.options = options;
    this.stats = {
      firebase: { users: 0, bloodRequests: 0, notifications: 0, errors: [] },
      appwrite: { users: 0, bloodRequests: 0, notifications: 0, userResponses: 0, analytics: 0, errors: [] }
    };
  }

  async run(): Promise<void> {
    console.log('🧹 Database Cleanup Script');
    console.log('==========================\n');

    if (this.options.dryRun) {
      console.log('🔍 DRY RUN MODE - No data will be deleted\n');
    }

    try {
      // Initialize services
      await this.initializeServices();

      // Show confirmation unless --confirm flag is used
      if (!this.options.confirm && !this.options.dryRun) {
        await this.showConfirmation();
      }

      // Clean Firebase data
      if (!this.options.appwriteOnly) {
        await this.cleanFirebaseData();
      }

      // Clean Appwrite data
      if (!this.options.firebaseOnly) {
        await this.cleanAppwriteData();
      }

      // Show summary
      this.showSummary();

    } catch (error) {
      console.error('❌ Cleanup failed:', error);
      process.exit(1);
    }
  }

  private async initializeServices(): Promise<void> {
    console.log('🔧 Initializing database services...\n');

    try {
      if (!this.options.appwriteOnly) {
        initializeFirebase();
        console.log('✅ Firebase initialized');
      }

      if (!this.options.firebaseOnly) {
        initializeAppwrite();
        console.log('✅ Appwrite initialized');
      }
    } catch (error) {
      console.error('❌ Failed to initialize services:', error);
      throw error;
    }
  }

  private async showConfirmation(): Promise<void> {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve, reject) => {
      const services = [];
      if (!this.options.appwriteOnly) services.push('Firebase');
      if (!this.options.firebaseOnly) services.push('Appwrite');

      rl.question(
        `⚠️  This will delete ALL test data from ${services.join(' and ')}.\n` +
        `   Are you sure you want to continue? (yes/no): `,
        (answer: string) => {
          rl.close();
          if (answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y') {
            resolve();
          } else {
            console.log('❌ Cleanup cancelled');
            process.exit(0);
          }
        }
      );
    });
  }

  private async cleanFirebaseData(): Promise<void> {
    console.log('\n🔥 Cleaning Firebase data...');
    console.log('----------------------------');

    try {
      // Clean notifications first (they may reference other documents)
      await this.cleanFirebaseNotifications();
      
      // Clean blood requests
      await this.cleanFirebaseBloodRequests();
      
      // Clean users last
      await this.cleanFirebaseUsers();

      console.log('✅ Firebase cleanup completed');
    } catch (error) {
      console.error('❌ Firebase cleanup failed:', error);
      this.stats.firebase.errors.push(error instanceof Error ? error.message : String(error));
    }
  }

  private async cleanFirebaseNotifications(): Promise<void> {
    try {
      const collection = firebaseService.getNotificationsCollection();
      const snapshot = await collection.get();

      if (snapshot.empty) {
        console.log('📭 No Firebase notifications to clean');
        return;
      }

      console.log(`🗑️  Found ${snapshot.size} Firebase notifications to delete`);

      if (!this.options.dryRun) {
        const batch = collection.firestore.batch();
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
      }

      this.stats.firebase.notifications = snapshot.size;
      console.log(`✅ Deleted ${snapshot.size} Firebase notifications`);
    } catch (error) {
      console.error('❌ Failed to clean Firebase notifications:', error);
      this.stats.firebase.errors.push(`Notifications: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async cleanFirebaseBloodRequests(): Promise<void> {
    try {
      const collection = firebaseService.getBloodRequestsCollection();
      const snapshot = await collection.get();

      if (snapshot.empty) {
        console.log('📭 No Firebase blood requests to clean');
        return;
      }

      console.log(`🗑️  Found ${snapshot.size} Firebase blood requests to delete`);

      if (!this.options.dryRun) {
        const batch = collection.firestore.batch();
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
      }

      this.stats.firebase.bloodRequests = snapshot.size;
      console.log(`✅ Deleted ${snapshot.size} Firebase blood requests`);
    } catch (error) {
      console.error('❌ Failed to clean Firebase blood requests:', error);
      this.stats.firebase.errors.push(`Blood Requests: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async cleanFirebaseUsers(): Promise<void> {
    try {
      const collection = firebaseService.getUsersCollection();
      const snapshot = await collection.get();

      if (snapshot.empty) {
        console.log('📭 No Firebase users to clean');
        return;
      }

      console.log(`🗑️  Found ${snapshot.size} Firebase users to delete`);

      if (!this.options.dryRun) {
        const batch = collection.firestore.batch();
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
      }

      this.stats.firebase.users = snapshot.size;
      console.log(`✅ Deleted ${snapshot.size} Firebase users`);
    } catch (error) {
      console.error('❌ Failed to clean Firebase users:', error);
      this.stats.firebase.errors.push(`Users: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async cleanAppwriteData(): Promise<void> {
    console.log('\n📱 Cleaning Appwrite data...');
    console.log('----------------------------');

    try {
      // Clean in order: analytics, notifications, user_responses, blood_requests, users
      await this.cleanAppwriteAnalytics();
      await this.cleanAppwriteNotifications();
      await this.cleanAppwriteUserResponses();
      await this.cleanAppwriteBloodRequests();
      await this.cleanAppwriteUsers();

      console.log('✅ Appwrite cleanup completed');
    } catch (error) {
      console.error('❌ Appwrite cleanup failed:', error);
      this.stats.appwrite.errors.push(error instanceof Error ? error.message : String(error));
    }
  }

  private async cleanAppwriteCollection(
    collectionName: string, 
    collectionId: string,
    statKey: keyof typeof this.stats.appwrite
  ): Promise<void> {
    try {
      const databases = getDatabases();
      const response = await databases.listDocuments(APPWRITE_DATABASE_ID, collectionId);

      if (response.documents.length === 0) {
        console.log(`📭 No Appwrite ${collectionName} to clean`);
        return;
      }

      console.log(`🗑️  Found ${response.documents.length} Appwrite ${collectionName} to delete`);

      if (!this.options.dryRun) {
        // Delete documents in batches to avoid rate limits
        const batchSize = 10;
        for (let i = 0; i < response.documents.length; i += batchSize) {
          const batch = response.documents.slice(i, i + batchSize);
          await Promise.all(
            batch.map(doc => 
              databases.deleteDocument(APPWRITE_DATABASE_ID, collectionId, doc.$id)
            )
          );
        }
      }

      if (typeof statKey === 'string' && statKey !== 'errors') {
        (this.stats.appwrite as any)[statKey] = response.documents.length;
      }
      console.log(`✅ Deleted ${response.documents.length} Appwrite ${collectionName}`);
    } catch (error) {
      console.error(`❌ Failed to clean Appwrite ${collectionName}:`, error);
      this.stats.appwrite.errors.push(`${collectionName}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async cleanAppwriteAnalytics(): Promise<void> {
    await this.cleanAppwriteCollection('analytics', COLLECTIONS.ANALYTICS, 'analytics');
  }

  private async cleanAppwriteNotifications(): Promise<void> {
    await this.cleanAppwriteCollection('notifications', COLLECTIONS.NOTIFICATIONS, 'notifications');
  }

  private async cleanAppwriteUserResponses(): Promise<void> {
    await this.cleanAppwriteCollection('user responses', COLLECTIONS.USER_RESPONSES, 'userResponses');
  }

  private async cleanAppwriteBloodRequests(): Promise<void> {
    await this.cleanAppwriteCollection('blood requests', COLLECTIONS.BLOOD_REQUESTS, 'bloodRequests');
  }

  private async cleanAppwriteUsers(): Promise<void> {
    await this.cleanAppwriteCollection('users', COLLECTIONS.USERS, 'users');
  }

  private showSummary(): void {
    console.log('\n📊 Cleanup Summary');
    console.log('==================');

    if (!this.options.appwriteOnly) {
      console.log('\n🔥 Firebase:');
      console.log(`   Users: ${this.stats.firebase.users}`);
      console.log(`   Blood Requests: ${this.stats.firebase.bloodRequests}`);
      console.log(`   Notifications: ${this.stats.firebase.notifications}`);
      if (this.stats.firebase.errors.length > 0) {
        console.log('   Errors:');
        this.stats.firebase.errors.forEach(error => console.log(`     - ${error}`));
      }
    }

    if (!this.options.firebaseOnly) {
      console.log('\n📱 Appwrite:');
      console.log(`   Users: ${this.stats.appwrite.users}`);
      console.log(`   Blood Requests: ${this.stats.appwrite.bloodRequests}`);
      console.log(`   Notifications: ${this.stats.appwrite.notifications}`);
      console.log(`   User Responses: ${this.stats.appwrite.userResponses}`);
      console.log(`   Analytics: ${this.stats.appwrite.analytics}`);
      if (this.stats.appwrite.errors.length > 0) {
        console.log('   Errors:');
        this.stats.appwrite.errors.forEach(error => console.log(`     - ${error}`));
      }
    }

    const totalDeleted = 
      this.stats.firebase.users + this.stats.firebase.bloodRequests + this.stats.firebase.notifications +
      this.stats.appwrite.users + this.stats.appwrite.bloodRequests + this.stats.appwrite.notifications +
      this.stats.appwrite.userResponses + this.stats.appwrite.analytics;

    console.log(`\n🎉 Total records ${this.options.dryRun ? 'found' : 'deleted'}: ${totalDeleted}`);

    if (this.options.dryRun) {
      console.log('\n💡 Run without --dry-run to actually delete the data');
    }
  }
}

// Parse command line arguments
function parseArgs(): CleanupOptions {
  const args = process.argv.slice(2);
  const options: CleanupOptions = {};

  args.forEach(arg => {
    switch (arg) {
      case '--firebase-only':
        options.firebaseOnly = true;
        break;
      case '--appwrite-only':
        options.appwriteOnly = true;
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--confirm':
        options.confirm = true;
        break;
      case '--help':
      case '-h':
        console.log(`
Database Cleanup Script

Usage: npx ts-node src/scripts/cleanupTestData.ts [options]

Options:
  --firebase-only    Clean only Firebase data
  --appwrite-only    Clean only Appwrite data
  --dry-run         Show what would be deleted without actually deleting
  --confirm         Skip confirmation prompt
  --help, -h        Show this help message
        `);
        process.exit(0);
        break;
    }
  });

  return options;
}

// Main execution
if (require.main === module) {
  const options = parseArgs();
  const cleanup = new DatabaseCleanup(options);
  cleanup.run().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

export { DatabaseCleanup };