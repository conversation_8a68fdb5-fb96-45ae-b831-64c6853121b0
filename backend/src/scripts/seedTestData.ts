#!/usr/bin/env node

/**
 * Test Data Seeding Script
 * 
 * This script seeds test data into both Firebase and Appwrite databases.
 * Useful for development and testing purposes.
 * 
 * Usage:
 *   npm run seed:test-data
 *   or
 *   npx ts-node src/scripts/seedTestData.ts [options]
 * 
 * Options:
 *   --firebase-only    Seed only Firebase data
 *   --appwrite-only    Seed only Appwrite data
 *   --count <number>   Number of test records to create (default: 5)
 */

import { firebaseService } from '../services/firebaseService';
import { appwriteService } from '../services/appwriteService';
import { initializeFirebase } from '../config/firebase';
import { initializeAppwrite } from '../config/appwrite';

interface SeedOptions {
  firebaseOnly?: boolean;
  appwriteOnly?: boolean;
  count?: number;
}

interface SeedStats {
  firebase: {
    users: number;
    bloodRequests: number;
    notifications: number;
  };
  appwrite: {
    users: number;
    bloodRequests: number;
    notifications: number;
  };
}

class TestDataSeeder {
  private options: SeedOptions;
  private stats: SeedStats;

  constructor(options: SeedOptions = {}) {
    this.options = { count: 5, ...options };
    this.stats = {
      firebase: { users: 0, bloodRequests: 0, notifications: 0 },
      appwrite: { users: 0, bloodRequests: 0, notifications: 0 }
    };
  }

  async run(): Promise<void> {
    console.log('🌱 Test Data Seeding Script');
    console.log('============================\n');

    try {
      // Initialize services
      await this.initializeServices();

      // Seed Firebase data
      if (!this.options.appwriteOnly) {
        await this.seedFirebaseData();
      }

      // Seed Appwrite data
      if (!this.options.firebaseOnly) {
        await this.seedAppwriteData();
      }

      // Show summary
      this.showSummary();

    } catch (error) {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    }
  }

  private async initializeServices(): Promise<void> {
    console.log('🔧 Initializing database services...\n');

    try {
      if (!this.options.appwriteOnly) {
        initializeFirebase();
        console.log('✅ Firebase initialized');
      }

      if (!this.options.firebaseOnly) {
        initializeAppwrite();
        console.log('✅ Appwrite initialized');
      }
    } catch (error) {
      console.error('❌ Failed to initialize services:', error);
      throw error;
    }
  }

  private async seedFirebaseData(): Promise<void> {
    console.log('\n🔥 Seeding Firebase data...');
    console.log('----------------------------');

    try {
      const userIds = await this.seedFirebaseUsers();
      await this.seedFirebaseBloodRequests(userIds);
      await this.seedFirebaseNotifications(userIds);

      console.log('✅ Firebase seeding completed');
    } catch (error) {
      console.error('❌ Firebase seeding failed:', error);
      throw error;
    }
  }

  private async seedFirebaseUsers(): Promise<string[]> {
    console.log(`👥 Creating ${this.options.count} Firebase test users...`);
    
    const userIds: string[] = [];
    const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    const userTypes = ['donor', 'recipient', 'both'];

    for (let i = 0; i < this.options.count!; i++) {
      const userData = {
        phoneNumber: `+1555000${String(i).padStart(4, '0')}`,
        name: `Test User ${i + 1}`,
        profile: {
          bloodType: bloodTypes[i % bloodTypes.length],
          userType: userTypes[i % userTypes.length],
          isAvailable: Math.random() > 0.3, // 70% available
          location: {
            coordinates: {
              latitude: 40.7128 + (Math.random() - 0.5) * 0.1, // NYC area
              longitude: -74.0060 + (Math.random() - 0.5) * 0.1
            },
            address: `${100 + i} Test Street, New York, NY`
          },
          joinedAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000), // Random date in past year
          lastActive: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random date in past week
          donationCount: Math.floor(Math.random() * 10),
          rating: 4 + Math.random(),
          reviewCount: Math.floor(Math.random() * 20)
        }
      };

      const userId = await firebaseService.createUser(userData);
      userIds.push(userId);
    }

    this.stats.firebase.users = userIds.length;
    console.log(`✅ Created ${userIds.length} Firebase users`);
    return userIds;
  }

  private async seedFirebaseBloodRequests(userIds: string[]): Promise<string[]> {
    console.log(`🩸 Creating ${Math.ceil(this.options.count! / 2)} Firebase blood requests...`);
    
    const requestIds: string[] = [];
    const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    const urgencies = ['normal', 'urgent', 'critical'];
    const quantities = ['1 unit', '2 units', '3 units', '1 pint', '2 pints'];

    const requestCount = Math.ceil(this.options.count! / 2);
    for (let i = 0; i < requestCount; i++) {
      const requestData = {
        requesterId: userIds[i % userIds.length],
        bloodType: bloodTypes[i % bloodTypes.length],
        quantity: quantities[i % quantities.length],
        urgency: urgencies[i % urgencies.length],
        location: {
          coordinates: {
            latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
            longitude: -74.0060 + (Math.random() - 0.5) * 0.1
          },
          address: `${200 + i} Hospital Street, New York, NY`
        },
        contactInfo: {
          name: `Emergency Contact ${i + 1}`,
          phone: `+1555100${String(i).padStart(4, '0')}`,
          preferredContact: i % 2 === 0 ? 'call' : 'text'
        },
        description: `Test blood request ${i + 1} - ${urgencies[i % urgencies.length]} need`,
        expiresAt: new Date(Date.now() + (24 + Math.random() * 48) * 60 * 60 * 1000) // 1-3 days from now
      };

      const requestId = await firebaseService.createBloodRequest(requestData);
      requestIds.push(requestId);
    }

    this.stats.firebase.bloodRequests = requestIds.length;
    console.log(`✅ Created ${requestIds.length} Firebase blood requests`);
    return requestIds;
  }

  private async seedFirebaseNotifications(userIds: string[]): Promise<void> {
    console.log(`🔔 Creating ${this.options.count} Firebase notifications...`);
    
    // Note: Firebase notifications are typically sent via FCM, not stored as documents
    // This is a placeholder for notification history/logs
    const notificationCount = this.options.count!;
    
    for (let i = 0; i < notificationCount; i++) {
      // In a real app, you'd store notification logs/history
      // For now, we'll just increment the counter
    }

    this.stats.firebase.notifications = notificationCount;
    console.log(`✅ Created ${notificationCount} Firebase notification records`);
  }

  private async seedAppwriteData(): Promise<void> {
    console.log('\n📱 Seeding Appwrite data...');
    console.log('----------------------------');

    try {
      const userIds = await this.seedAppwriteUsers();
      await this.seedAppwriteBloodRequests(userIds);
      await this.seedAppwriteNotifications(userIds);

      console.log('✅ Appwrite seeding completed');
    } catch (error) {
      console.error('❌ Appwrite seeding failed:', error);
      throw error;
    }
  }

  private async seedAppwriteUsers(): Promise<string[]> {
    console.log(`👥 Creating ${this.options.count} Appwrite test users...`);
    
    const userIds: string[] = [];
    const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    const userTypes = ['donor', 'recipient', 'both'];

    for (let i = 0; i < this.options.count!; i++) {
      const userData = {
        userId: `test_user_${i + 1}`,
        phoneNumber: `+1555000${String(i).padStart(4, '0')}`,
        name: `Test User ${i + 1}`,
        bloodType: bloodTypes[i % bloodTypes.length],
        userType: userTypes[i % userTypes.length],
        isAvailable: Math.random() > 0.3,
        latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
        longitude: -74.0060 + (Math.random() - 0.5) * 0.1,
        address: `${100 + i} Test Street, New York, NY`,
        joinedAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        lastActive: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        donationCount: Math.floor(Math.random() * 10),
        rating: 4 + Math.random(),
        reviewCount: Math.floor(Math.random() * 20)
      };

      const userId = await appwriteService.createUser(userData);
      userIds.push(userId);
    }

    this.stats.appwrite.users = userIds.length;
    console.log(`✅ Created ${userIds.length} Appwrite users`);
    return userIds;
  }

  private async seedAppwriteBloodRequests(userIds: string[]): Promise<string[]> {
    console.log(`🩸 Creating ${Math.ceil(this.options.count! / 2)} Appwrite blood requests...`);
    
    const requestIds: string[] = [];
    const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    const urgencies = ['normal', 'urgent', 'critical'];
    const quantities = ['1 unit', '2 units', '3 units', '1 pint', '2 pints'];

    const requestCount = Math.ceil(this.options.count! / 2);
    for (let i = 0; i < requestCount; i++) {
      const requestData = {
        requesterId: `test_user_${(i % userIds.length) + 1}`,
        bloodType: bloodTypes[i % bloodTypes.length],
        quantity: quantities[i % quantities.length],
        urgency: urgencies[i % urgencies.length],
        latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
        longitude: -74.0060 + (Math.random() - 0.5) * 0.1,
        address: `${200 + i} Hospital Street, New York, NY`,
        contactName: `Emergency Contact ${i + 1}`,
        contactPhone: `+1555100${String(i).padStart(4, '0')}`,
        preferredContact: i % 2 === 0 ? 'call' : 'text',
        description: `Test blood request ${i + 1} - ${urgencies[i % urgencies.length]} need`,
        expiresAt: new Date(Date.now() + (24 + Math.random() * 48) * 60 * 60 * 1000).toISOString()
      };

      const requestId = await appwriteService.createBloodRequest(requestData);
      requestIds.push(requestId);
    }

    this.stats.appwrite.bloodRequests = requestIds.length;
    console.log(`✅ Created ${requestIds.length} Appwrite blood requests`);
    return requestIds;
  }

  private async seedAppwriteNotifications(userIds: string[]): Promise<void> {
    console.log(`🔔 Creating ${this.options.count} Appwrite notifications...`);
    
    const notificationCount = this.options.count!;
    
    for (let i = 0; i < notificationCount; i++) {
      const notificationData = {
        userId: `test_user_${(i % userIds.length) + 1}`,
        title: `Test Notification ${i + 1}`,
        body: `This is a test notification for blood request matching`,
        data: {
          requestId: `test_request_${i + 1}`,
          bloodType: ['A+', 'B+', 'O+'][i % 3],
          distance: String(Math.floor(Math.random() * 10) + 1),
          urgency: ['normal', 'urgent'][i % 2]
        }
      };

      await appwriteService.sendNotification(notificationData);
    }

    this.stats.appwrite.notifications = notificationCount;
    console.log(`✅ Created ${notificationCount} Appwrite notifications`);
  }

  private showSummary(): void {
    console.log('\n📊 Seeding Summary');
    console.log('==================');

    if (!this.options.appwriteOnly) {
      console.log('\n🔥 Firebase:');
      console.log(`   Users: ${this.stats.firebase.users}`);
      console.log(`   Blood Requests: ${this.stats.firebase.bloodRequests}`);
      console.log(`   Notifications: ${this.stats.firebase.notifications}`);
    }

    if (!this.options.firebaseOnly) {
      console.log('\n📱 Appwrite:');
      console.log(`   Users: ${this.stats.appwrite.users}`);
      console.log(`   Blood Requests: ${this.stats.appwrite.bloodRequests}`);
      console.log(`   Notifications: ${this.stats.appwrite.notifications}`);
    }

    const totalCreated = 
      this.stats.firebase.users + this.stats.firebase.bloodRequests + this.stats.firebase.notifications +
      this.stats.appwrite.users + this.stats.appwrite.bloodRequests + this.stats.appwrite.notifications;

    console.log(`\n🎉 Total records created: ${totalCreated}`);
  }
}

// Parse command line arguments
function parseArgs(): SeedOptions {
  const args = process.argv.slice(2);
  const options: SeedOptions = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--firebase-only':
        options.firebaseOnly = true;
        break;
      case '--appwrite-only':
        options.appwriteOnly = true;
        break;
      case '--count':
        const count = parseInt(args[i + 1]);
        if (!isNaN(count) && count > 0) {
          options.count = count;
          i++; // Skip next argument as it's the count value
        }
        break;
      case '--help':
      case '-h':
        console.log(`
Test Data Seeding Script

Usage: npx ts-node src/scripts/seedTestData.ts [options]

Options:
  --firebase-only    Seed only Firebase data
  --appwrite-only    Seed only Appwrite data
  --count <number>   Number of test records to create (default: 5)
  --help, -h         Show this help message
        `);
        process.exit(0);
        break;
    }
  }

  return options;
}

// Main execution
if (require.main === module) {
  const options = parseArgs();
  const seeder = new TestDataSeeder(options);
  seeder.run().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

export { TestDataSeeder };