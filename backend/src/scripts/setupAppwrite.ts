import dotenv from 'dotenv';
dotenv.config();
import { initializeAppwrite, getDatabases, APPWRITE_DATABASE_ID, COLLECTIONS } from '../config/appwrite';
import { ID, Permission, Role } from 'node-appwrite';


/**
 * <PERSON><PERSON><PERSON> to set up Appwrite database collections and attributes
 * Run this script after creating your Appwrite project and database
 */

// Initialize Appwrite first
initializeAppwrite();
const databases = getDatabases();

const setupCollections = async () => {
  try {
    console.log('🚀 Setting up Appwrite database and collections...');

    // First, try to create the database if it doesn't exist
    await createDatabaseIfNeeded();

    // Create Users Collection
    await createUsersCollection();
    
    // Create Blood Requests Collection
    await createBloodRequestsCollection();
    
    // Create Notifications Collection
    await createNotificationsCollection();
    
    // Create User Responses Collection
    await createUserResponsesCollection();
    
    // Create Analytics Collection
    await createAnalyticsCollection();

    console.log('✅ All collections created successfully!');
    console.log('📝 Don\'t forget to set up indexes in Appwrite Console for better performance');
    
  } catch (error) {
    console.error('❌ Error setting up collections:', error);
  }
};

const createDatabaseIfNeeded = async () => {
  try {
    console.log('Checking if database exists...');
    
    // Try to get the database
    const database = await databases.get(APPWRITE_DATABASE_ID);
    console.log(`✅ Database "${database.name}" (${database.$id}) already exists`);
    
  } catch (error) {
    if (error && typeof error === 'object' && 'code' in error && error.code === 404) {
      console.log('Database not found, attempting to create...');
      
      try {
        const database = await databases.create(
          APPWRITE_DATABASE_ID,
          'UBLOOD Database'
        );
        console.log(`✅ Database "${database.name}" (${database.$id}) created successfully`);
      } catch (createError) {
        console.error('❌ Failed to create database:', createError);
        if (createError && typeof createError === 'object' && 'code' in createError && createError.code === 401) {
          console.log('💡 Your API key may not have "databases.write" permission');
          console.log('   Please create the database manually in Appwrite Console');
        }
        throw createError;
      }
    } else {
      console.error('❌ Error checking database:', error);
      throw error;
    }
  }
};

const createUsersCollection = async () => {
  try {
    console.log('Creating Users collection...');
    
    const collection = await databases.createCollection(
      APPWRITE_DATABASE_ID,
      COLLECTIONS.USERS,
      'Users',
      [
        Permission.read(Role.any()),
        Permission.create(Role.users()),
        Permission.update(Role.users()),
        Permission.delete(Role.users())
      ]
    );

    // Add attributes (using flat naming convention for Appwrite)
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'phoneNumber', 20, true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'name', 100, true);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'bloodType', 
      ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'], true);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'userType', 
      ['donor', 'recipient', 'both'], true);
    await databases.createBooleanAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'isAvailable', true);
    await databases.createFloatAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'latitude', true);
    await databases.createFloatAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'longitude', true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'address', 255, false);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'locationUpdated', false);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'joinedAt', true);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'lastActive', false);
    await databases.createIntegerAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'totalDonations', false);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'lastDonationDate', false);
    await databases.createFloatAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'rating', false);
    await databases.createIntegerAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'reviewCount', false);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'fcmToken', 255, false);
    await databases.createBooleanAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'pushEnabled', false);
    await databases.createIntegerAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'maxDistance', false);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'createdAt', true);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USERS, 'updatedAt', true);

    console.log('✅ Users collection created');
  } catch (error) {
    console.error('❌ Error creating Users collection:', error);
  }
};

const createBloodRequestsCollection = async () => {
  try {
    console.log('Creating Blood Requests collection...');
    
    const collection = await databases.createCollection(
      APPWRITE_DATABASE_ID,
      COLLECTIONS.BLOOD_REQUESTS,
      'Blood Requests',
      [
        Permission.read(Role.any()),
        Permission.create(Role.users()),
        Permission.update(Role.users()),
        Permission.delete(Role.users())
      ]
    );

    // Add attributes (using flat naming convention for Appwrite)
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'requesterId', 50, true);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'bloodType', 
      ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'], true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'quantity', 50, true);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'urgency', 
      ['normal', 'urgent', 'critical'], true);
    await databases.createFloatAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'latitude', true);
    await databases.createFloatAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'longitude', true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'address', 255, true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'contactName', 100, true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'contactPhone', 20, true);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'preferredContact', 
      ['call', 'text'], false);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'description', 500, false);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'status', 
      ['active', 'fulfilled', 'cancelled', 'expired'], true);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'expiresAt', true);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'createdAt', true);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.BLOOD_REQUESTS, 'updatedAt', true);

    console.log('✅ Blood Requests collection created');
  } catch (error) {
    console.error('❌ Error creating Blood Requests collection:', error);
  }
};

const createNotificationsCollection = async () => {
  try {
    console.log('Creating Notifications collection...');
    
    const collection = await databases.createCollection(
      APPWRITE_DATABASE_ID,
      COLLECTIONS.NOTIFICATIONS,
      'Notifications',
      [
        Permission.read(Role.users()),
        Permission.create(Role.users()),
        Permission.update(Role.users())
      ]
    );

    // Add attributes (using flat naming convention for Appwrite)
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'userId', 50, true);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'type', 
      ['blood_request', 'donor_response', 'request_fulfilled', 'request_cancelled'], true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'title', 100, true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'message', 255, true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'requestId', 50, false);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'bloodType', 10, false);
    await databases.createIntegerAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'distance', false);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'urgency', 20, false);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'status', 
      ['sent', 'delivered', 'read', 'responded'], true);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'readAt', false);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'respondedAt', false);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.NOTIFICATIONS, 'createdAt', true);

    console.log('✅ Notifications collection created');
  } catch (error) {
    console.error('❌ Error creating Notifications collection:', error);
  }
};

const createUserResponsesCollection = async () => {
  try {
    console.log('Creating User Responses collection...');
    
    const collection = await databases.createCollection(
      APPWRITE_DATABASE_ID,
      COLLECTIONS.USER_RESPONSES,
      'User Responses',
      [
        Permission.read(Role.users()),
        Permission.create(Role.users()),
        Permission.update(Role.users())
      ]
    );

    // Add attributes (using flat naming convention for Appwrite)
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USER_RESPONSES, 'requestId', 50, true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USER_RESPONSES, 'donorId', 50, true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USER_RESPONSES, 'requesterId', 50, true);
    await databases.createEnumAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USER_RESPONSES, 'response', 
      ['yes', 'no'], true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USER_RESPONSES, 'donorContactName', 100, false);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USER_RESPONSES, 'donorContactPhone', 20, false);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USER_RESPONSES, 'respondedAt', true);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.USER_RESPONSES, 'createdAt', true);

    console.log('✅ User Responses collection created');
  } catch (error) {
    console.error('❌ Error creating User Responses collection:', error);
  }
};

const createAnalyticsCollection = async () => {
  try {
    console.log('Creating Analytics collection...');
    
    const collection = await databases.createCollection(
      APPWRITE_DATABASE_ID,
      COLLECTIONS.ANALYTICS,
      'Analytics',
      [
        Permission.read(Role.team('admin')),
        Permission.create(Role.team('admin')),
        Permission.update(Role.team('admin'))
      ]
    );

    // Add attributes
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.ANALYTICS, 'eventType', 50, true);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.ANALYTICS, 'userId', 50, false);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.ANALYTICS, 'requestId', 50, false);
    await databases.createStringAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.ANALYTICS, 'metadata', 1000, false);
    await databases.createDatetimeAttribute(APPWRITE_DATABASE_ID, COLLECTIONS.ANALYTICS, 'timestamp', true);

    console.log('✅ Analytics collection created');
  } catch (error) {
    console.error('❌ Error creating Analytics collection:', error);
  }
};

// Run the setup if this script is executed directly
if (require.main === module) {
  setupCollections();
}

export { setupCollections };