import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

import { getDatabases, getUsers, APPWRITE_DATABASE_ID, COLLECTIONS } from '../config/appwrite';
import { initializeAppwrite } from '../config/appwrite';


/**
 * <PERSON><PERSON>t to verify Appwrite setup and test connectivity
 * Run this script to ensure Appwrite is properly configured
 */

const verifyAppwriteSetup = async () => {
  try {
    console.log('🔍 Verifying Appwrite setup...\n');

    // Initialize Appwrite
    console.log('1. Initializing Appwrite client...');
    const client = initializeAppwrite();
    console.log('✅ Appwrite client initialized successfully\n');

    // Test database connection
    console.log('2. Testing database connection...');
    const databases = getDatabases();
    
    try {
      // Try to get our specific database instead of listing all
      const database = await databases.get(APPWRITE_DATABASE_ID);
      console.log('✅ Database connection successful');
      console.log(`📊 Connected to database: "${database.name}" (${database.$id})\n`);
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      if (error && typeof error === 'object' && 'code' in error && error.code === 401) {
        console.log('💡 This might be due to insufficient API key permissions');
        console.log('   Ensure your API key has "databases.read" scope\n');
      }
      return;
    }

    // Check if our database exists
    console.log('3. Checking if target database exists...');
    try {
      const database = await databases.get(APPWRITE_DATABASE_ID);
      console.log(`✅ Database "${database.name}" (${database.$id}) found\n`);
    } catch (error) {
      console.error(`❌ Database "${APPWRITE_DATABASE_ID}" not found:`, error);
      console.log('💡 Please create the database in Appwrite Console first\n');
      return;
    }

    // Check collections
    console.log('4. Checking collections...');
    const requiredCollections = Object.entries(COLLECTIONS);
    const collectionStatus: { [key: string]: boolean } = {};

    for (const [name, id] of requiredCollections) {
      try {
        const collection = await databases.getCollection(APPWRITE_DATABASE_ID, id);
        console.log(`✅ Collection "${name}" (${id}) exists`);
        collectionStatus[name] = true;
      } catch (error) {
        console.log(`❌ Collection "${name}" (${id}) not found`);
        collectionStatus[name] = false;
      }
    }

    console.log('\n5. Collection Summary:');
    const existingCollections = Object.values(collectionStatus).filter(Boolean).length;
    const totalCollections = Object.keys(collectionStatus).length;
    
    console.log(`📊 ${existingCollections}/${totalCollections} collections exist`);
    
    if (existingCollections === 0) {
      console.log('\n🚀 To create collections, run:');
      console.log('npm run setup-appwrite-collections');
    } else if (existingCollections < totalCollections) {
      console.log('\n⚠️  Some collections are missing. You may need to:');
      console.log('1. Create them manually in Appwrite Console');
      console.log('2. Or run the setup script: npm run setup-appwrite-collections');
    } else {
      console.log('✅ All collections are present!');
    }

    // Test user management
    console.log('\n6. Testing user management...');
    const users = getUsers();
    
    try {
      const usersList = await users.list();
      console.log(`✅ User management working - ${usersList.total} users found`);
    } catch (error) {
      console.error('❌ User management test failed:', error);
    }

    console.log('\n🎉 Appwrite setup verification complete!');
    console.log('\n📋 Next Steps:');
    console.log('1. Ensure all collections exist (create missing ones)');
    console.log('2. Set up proper indexes for better performance');
    console.log('3. Configure authentication providers if needed');
    console.log('4. Set up push notification providers for messaging');

  } catch (error) {
    console.error('❌ Appwrite setup verification failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check your environment variables in .env file');
    console.log('2. Verify your API key has the correct permissions');
    console.log('3. Ensure your project ID is correct');
    console.log('4. Check if your Appwrite endpoint is accessible');
  }
};

// Run verification if this script is executed directly
if (require.main === module) {
  verifyAppwriteSetup();
}

export { verifyAppwriteSetup };