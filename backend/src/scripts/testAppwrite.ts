import dotenv from 'dotenv';
// Load environment variables
dotenv.config();
import { initializeAppwrite } from '../config/appwrite';
import { appwriteService } from '../services/appwriteService';



// Initialize Appwrite
initializeAppwrite();

/**
 * Simple Appwrite connectivity test
 * Tests basic operations without requiring admin permissions
 */

const testAppwriteConnectivity = async () => {
  try {
    console.log('🧪 Testing Appwrite connectivity...\n');

    console.log('📋 Configuration:');
    console.log(`   Endpoint: ${process.env.APPWRITE_ENDPOINT}`);
    console.log(`   Project ID: ${process.env.APPWRITE_PROJECT_ID}`);
    console.log(`   Database ID: ${process.env.APPWRITE_DATABASE_ID}`);
    console.log(`   API Key: ${process.env.APPWRITE_API_KEY ? '✅ Set' : '❌ Missing'}\n`);

    // Test 1: Try to create a test user document (this will fail if collections don't exist)
    console.log('1. Testing user creation...');
    try {
      const testUserData = {
        phoneNumber: '+1234567890',
        name: 'Test User',
        bloodType: 'O+',
        userType: 'donor',
        isAvailable: true,
        latitude: 40.7128,
        longitude: -74.0060,
        joinedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const userId = await appwriteService.createUser(testUserData);
      console.log(`✅ User creation successful - ID: ${userId}`);

      // Clean up - delete the test user
      try {
        await appwriteService.deleteUser(userId);
        console.log('🧹 Test user cleaned up successfully');
      } catch (cleanupError) {
        console.log('⚠️  Test user cleanup failed:', cleanupError);
        console.log('💡 You may need to manually delete test user ID:', userId);
      }

    } catch (error) {
      console.error('❌ User creation failed:', error);
      if (error && typeof error === 'object' && 'message' in error) {
        console.log(`   Error: ${error.message}`);
      }
      
      if (error && typeof error === 'object' && 'code' in error) {
        if (error.code === 404) {
          console.log('💡 This likely means the "users" collection doesn\'t exist yet');
          console.log('   Create it in the Appwrite Console or run: npm run setup-appwrite');
        } else if (error.code === 401) {
          console.log('💡 This means your API key lacks the required permissions');
          console.log('   Ensure your API key has "documents.write" scope');
        }
      }
    }

    console.log('\n2. Testing blood request query...');
    try {
      const activeRequests = await appwriteService.getActiveBloodRequests({ limit: 1 });
      console.log(`✅ Blood request query successful - Found ${activeRequests.length} active requests`);
    } catch (error) {
      console.error('❌ Blood request query failed:', error);
      if (error && typeof error === 'object' && 'code' in error && error.code === 404) {
        console.log('💡 This likely means the "blood_requests" collection doesn\'t exist yet');
      }
    }

    console.log('\n3. Testing notification creation...');
    let notificationId: string | null = null;
    try {
      const notificationResult = await appwriteService.sendNotification({
        userId: 'test-user-123',
        title: 'Test Notification',
        body: 'This is a test notification from the setup script',
        data: { requestId: 'test-request-123' }
      });
      notificationId = notificationResult.notification.$id;
      console.log(`✅ Notification creation successful - ID: ${notificationId}`);

      // Clean up - delete the test notification
      if (notificationId) {
        try {
          await appwriteService.deleteNotification(notificationId);
          console.log('🧹 Test notification cleaned up successfully');
        } catch (cleanupError) {
          console.log('⚠️  Test notification cleanup failed:', cleanupError);
          console.log('💡 You may need to manually delete test notification ID:', notificationId);
        }
      }
    } catch (error) {
      console.error('❌ Notification creation failed:', error);
      if (error && typeof error === 'object' && 'code' in error && error.code === 404) {
        console.log('💡 This likely means the "notifications" collection doesn\'t exist yet');
      }
    }

    console.log('\n🎉 Appwrite connectivity test complete!');
    console.log('\n📋 Summary:');
    console.log('- If all tests passed: Your Appwrite setup is working correctly');
    console.log('- If tests failed with 404 errors: You need to create collections');
    console.log('- If tests failed with 401 errors: Your API key needs more permissions');
    console.log('\n📖 For detailed setup instructions, see: backend/APPWRITE_SETUP.md');

  } catch (error) {
    console.error('❌ Appwrite connectivity test failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check your .env file has all required Appwrite variables');
    console.log('2. Verify your API key has the correct permissions');
    console.log('3. Ensure your project ID and endpoint are correct');
    console.log('4. Check if your Appwrite instance is accessible');
  }
};

// Run test if this script is executed directly
if (require.main === module) {
  testAppwriteConnectivity();
}

export { testAppwriteConnectivity };