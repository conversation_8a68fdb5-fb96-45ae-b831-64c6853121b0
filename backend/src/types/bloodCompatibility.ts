/**
 * Blood type compatibility validation
 */

import { BloodType } from './enums';

/**
 * Blood compatibility matrix - defines which blood types can donate to which recipients
 */
const BLOOD_COMPATIBILITY_MATRIX: Record<BloodType, BloodType[]> = {
  [BloodType.O_NEGATIVE]: [
    BloodType.O_NEGATIVE,
    BloodType.O_POSITIVE,
    BloodType.A_NEGATIVE,
    BloodType.A_POSITIVE,
    BloodType.B_NEGATIVE,
    BloodType.B_POSITIVE,
    BloodType.AB_NEGATIVE,
    BloodType.AB_POSITIVE
  ],
  [BloodType.O_POSITIVE]: [
    BloodType.O_POSITIVE,
    BloodType.A_POSITIVE,
    BloodType.B_POSITIVE,
    BloodType.AB_POSITIVE
  ],
  [BloodType.A_NEGATIVE]: [
    BloodType.A_NEGATIVE,
    BloodType.A_POSITIVE,
    BloodType.AB_NEGATIVE,
    BloodType.AB_POSITIVE
  ],
  [BloodType.A_POSITIVE]: [
    BloodType.A_POSITIVE,
    BloodType.AB_POSITIVE
  ],
  [BloodType.B_NEGATIVE]: [
    BloodType.B_NEGATIVE,
    BloodType.B_POSITIVE,
    BloodType.AB_NEGATIVE,
    BloodType.AB_POSITIVE
  ],
  [BloodType.B_POSITIVE]: [
    BloodType.B_POSITIVE,
    BloodType.AB_POSITIVE
  ],
  [BloodType.AB_NEGATIVE]: [
    BloodType.AB_NEGATIVE,
    BloodType.AB_POSITIVE
  ],
  [BloodType.AB_POSITIVE]: [
    BloodType.AB_POSITIVE
  ]
};

/**
 * Validates if a donor blood type is compatible with a recipient blood type
 * @param donorBloodType - The donor's blood type
 * @param recipientBloodType - The recipient's blood type
 * @returns boolean indicating if donation is compatible
 */
export function isBloodTypeCompatible(
  donorBloodType: BloodType,
  recipientBloodType: BloodType
): boolean {
  if (!donorBloodType || !recipientBloodType) {
    return false;
  }
  
  const compatibleRecipients = BLOOD_COMPATIBILITY_MATRIX[donorBloodType];
  if (!compatibleRecipients) {
    return false;
  }
  
  return compatibleRecipients.includes(recipientBloodType);
}

/**
 * Gets all blood types that can donate to a specific recipient blood type
 * @param recipientBloodType - The recipient's blood type
 * @returns Array of compatible donor blood types
 */
export function getCompatibleDonorTypes(recipientBloodType: BloodType): BloodType[] {
  if (!recipientBloodType) {
    return [];
  }
  
  const compatibleDonors: BloodType[] = [];
  
  for (const [donorType, recipientTypes] of Object.entries(BLOOD_COMPATIBILITY_MATRIX)) {
    if (recipientTypes.includes(recipientBloodType)) {
      compatibleDonors.push(donorType as BloodType);
    }
  }
  
  return compatibleDonors;
}

/**
 * Gets all blood types that a specific donor blood type can donate to
 * @param donorBloodType - The donor's blood type
 * @returns Array of compatible recipient blood types
 */
export function getCompatibleRecipientTypes(donorBloodType: BloodType): BloodType[] {
  return BLOOD_COMPATIBILITY_MATRIX[donorBloodType] || [];
}

/**
 * Validates if a blood type string is a valid BloodType enum value
 * @param bloodType - The blood type string to validate
 * @returns boolean indicating if blood type is valid
 */
export function validateBloodType(bloodType: string): bloodType is BloodType {
  return Object.values(BloodType).includes(bloodType as BloodType);
}