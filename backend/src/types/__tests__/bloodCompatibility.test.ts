/**
 * Unit tests for blood compatibility validation
 */

import {
  isBloodTypeCompatible,
  getCompatibleDonorTypes,
  getCompatibleRecipientTypes,
  validateBloodType
} from '../bloodCompatibility';
import { BloodType } from '../enums';

describe('Blood Compatibility Validation', () => {
  describe('isBloodTypeCompatible', () => {
    describe('O- Universal Donor', () => {
      it('should allow O- to donate to all blood types', () => {
        const allBloodTypes = Object.values(BloodType);
        
        allBloodTypes.forEach(recipientType => {
          expect(isBloodTypeCompatible(BloodType.O_NEGATIVE, recipientType)).toBe(true);
        });
      });
    });

    describe('AB+ Universal Recipient', () => {
      it('should allow all blood types to donate to AB+', () => {
        const allBloodTypes = Object.values(BloodType);
        
        allBloodTypes.forEach(donorType => {
          expect(isBloodTypeCompatible(donorType, BloodType.AB_POSITIVE)).toBe(true);
        });
      });
    });

    describe('Same Blood Type Compatibility', () => {
      it('should allow same blood types to be compatible', () => {
        const allBloodTypes = Object.values(BloodType);
        
        allBloodTypes.forEach(bloodType => {
          expect(isBloodTypeCompatible(bloodType, bloodType)).toBe(true);
        });
      });
    });

    describe('Positive/Negative Compatibility', () => {
      it('should allow negative types to donate to positive of same ABO group', () => {
        expect(isBloodTypeCompatible(BloodType.A_NEGATIVE, BloodType.A_POSITIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.B_NEGATIVE, BloodType.B_POSITIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.AB_NEGATIVE, BloodType.AB_POSITIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.O_NEGATIVE, BloodType.O_POSITIVE)).toBe(true);
      });

      it('should not allow positive types to donate to negative', () => {
        expect(isBloodTypeCompatible(BloodType.A_POSITIVE, BloodType.A_NEGATIVE)).toBe(false);
        expect(isBloodTypeCompatible(BloodType.B_POSITIVE, BloodType.B_NEGATIVE)).toBe(false);
        expect(isBloodTypeCompatible(BloodType.AB_POSITIVE, BloodType.AB_NEGATIVE)).toBe(false);
        expect(isBloodTypeCompatible(BloodType.O_POSITIVE, BloodType.O_NEGATIVE)).toBe(false);
      });
    });

    describe('ABO Group Compatibility', () => {
      it('should allow O to donate to A, B, AB', () => {
        expect(isBloodTypeCompatible(BloodType.O_POSITIVE, BloodType.A_POSITIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.O_POSITIVE, BloodType.B_POSITIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.O_POSITIVE, BloodType.AB_POSITIVE)).toBe(true);
        
        expect(isBloodTypeCompatible(BloodType.O_NEGATIVE, BloodType.A_NEGATIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.O_NEGATIVE, BloodType.B_NEGATIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.O_NEGATIVE, BloodType.AB_NEGATIVE)).toBe(true);
      });

      it('should allow A to donate to AB', () => {
        expect(isBloodTypeCompatible(BloodType.A_POSITIVE, BloodType.AB_POSITIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.A_NEGATIVE, BloodType.AB_NEGATIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.A_NEGATIVE, BloodType.AB_POSITIVE)).toBe(true);
      });

      it('should allow B to donate to AB', () => {
        expect(isBloodTypeCompatible(BloodType.B_POSITIVE, BloodType.AB_POSITIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.B_NEGATIVE, BloodType.AB_NEGATIVE)).toBe(true);
        expect(isBloodTypeCompatible(BloodType.B_NEGATIVE, BloodType.AB_POSITIVE)).toBe(true);
      });

      it('should not allow incompatible ABO groups', () => {
        // A cannot donate to B
        expect(isBloodTypeCompatible(BloodType.A_POSITIVE, BloodType.B_POSITIVE)).toBe(false);
        expect(isBloodTypeCompatible(BloodType.A_NEGATIVE, BloodType.B_NEGATIVE)).toBe(false);
        
        // B cannot donate to A
        expect(isBloodTypeCompatible(BloodType.B_POSITIVE, BloodType.A_POSITIVE)).toBe(false);
        expect(isBloodTypeCompatible(BloodType.B_NEGATIVE, BloodType.A_NEGATIVE)).toBe(false);
        
        // AB cannot donate to A, B, or O
        expect(isBloodTypeCompatible(BloodType.AB_POSITIVE, BloodType.A_POSITIVE)).toBe(false);
        expect(isBloodTypeCompatible(BloodType.AB_POSITIVE, BloodType.B_POSITIVE)).toBe(false);
        expect(isBloodTypeCompatible(BloodType.AB_POSITIVE, BloodType.O_POSITIVE)).toBe(false);
      });
    });
  });

  describe('getCompatibleDonorTypes', () => {
    it('should return all blood types for AB+ recipient', () => {
      const donors = getCompatibleDonorTypes(BloodType.AB_POSITIVE);
      const allBloodTypes = Object.values(BloodType);
      
      expect(donors).toHaveLength(allBloodTypes.length);
      expect(donors.sort()).toEqual(allBloodTypes.sort());
    });

    it('should return only O- for O- recipient', () => {
      const donors = getCompatibleDonorTypes(BloodType.O_NEGATIVE);
      
      expect(donors).toHaveLength(1);
      expect(donors).toContain(BloodType.O_NEGATIVE);
    });

    it('should return correct donors for A+ recipient', () => {
      const donors = getCompatibleDonorTypes(BloodType.A_POSITIVE);
      const expectedDonors = [
        BloodType.O_NEGATIVE,
        BloodType.O_POSITIVE,
        BloodType.A_NEGATIVE,
        BloodType.A_POSITIVE
      ];
      
      expect(donors.sort()).toEqual(expectedDonors.sort());
    });

    it('should return correct donors for B- recipient', () => {
      const donors = getCompatibleDonorTypes(BloodType.B_NEGATIVE);
      const expectedDonors = [
        BloodType.O_NEGATIVE,
        BloodType.B_NEGATIVE
      ];
      
      expect(donors.sort()).toEqual(expectedDonors.sort());
    });
  });

  describe('getCompatibleRecipientTypes', () => {
    it('should return all blood types for O- donor', () => {
      const recipients = getCompatibleRecipientTypes(BloodType.O_NEGATIVE);
      const allBloodTypes = Object.values(BloodType);
      
      expect(recipients).toHaveLength(allBloodTypes.length);
      expect(recipients.sort()).toEqual(allBloodTypes.sort());
    });

    it('should return only AB+ for AB+ donor', () => {
      const recipients = getCompatibleRecipientTypes(BloodType.AB_POSITIVE);
      
      expect(recipients).toHaveLength(1);
      expect(recipients).toContain(BloodType.AB_POSITIVE);
    });

    it('should return correct recipients for A+ donor', () => {
      const recipients = getCompatibleRecipientTypes(BloodType.A_POSITIVE);
      const expectedRecipients = [
        BloodType.A_POSITIVE,
        BloodType.AB_POSITIVE
      ];
      
      expect(recipients.sort()).toEqual(expectedRecipients.sort());
    });

    it('should return correct recipients for B- donor', () => {
      const recipients = getCompatibleRecipientTypes(BloodType.B_NEGATIVE);
      const expectedRecipients = [
        BloodType.B_NEGATIVE,
        BloodType.B_POSITIVE,
        BloodType.AB_NEGATIVE,
        BloodType.AB_POSITIVE
      ];
      
      expect(recipients.sort()).toEqual(expectedRecipients.sort());
    });
  });

  describe('validateBloodType', () => {
    it('should validate correct blood type strings', () => {
      const validBloodTypes = ['O+', 'O-', 'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-'];
      
      validBloodTypes.forEach(bloodType => {
        expect(validateBloodType(bloodType)).toBe(true);
      });
    });

    it('should reject invalid blood type strings', () => {
      const invalidBloodTypes = ['X+', 'O', 'A', 'B', 'AB', 'C+', 'D-', '', 'invalid'];
      
      invalidBloodTypes.forEach(bloodType => {
        expect(validateBloodType(bloodType)).toBe(false);
      });
    });

    it('should be case sensitive', () => {
      expect(validateBloodType('o+')).toBe(false);
      expect(validateBloodType('a-')).toBe(false);
      expect(validateBloodType('ab+')).toBe(false);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle undefined blood types gracefully', () => {
      expect(isBloodTypeCompatible(undefined as any, BloodType.A_POSITIVE)).toBe(false);
      expect(isBloodTypeCompatible(BloodType.A_POSITIVE, undefined as any)).toBe(false);
    });

    it('should handle invalid blood type enums', () => {
      const invalidBloodType = 'INVALID_TYPE' as BloodType;
      
      expect(isBloodTypeCompatible(invalidBloodType, BloodType.A_POSITIVE)).toBe(false);
      expect(getCompatibleDonorTypes(invalidBloodType)).toEqual([]);
      expect(getCompatibleRecipientTypes(invalidBloodType)).toEqual([]);
    });
  });

  describe('Performance and Consistency', () => {
    it('should be consistent with reciprocal operations', () => {
      const allBloodTypes = Object.values(BloodType);
      
      allBloodTypes.forEach(donorType => {
        allBloodTypes.forEach(recipientType => {
          const canDonate = isBloodTypeCompatible(donorType, recipientType);
          const donorList = getCompatibleDonorTypes(recipientType);
          const recipientList = getCompatibleRecipientTypes(donorType);
          
          // If donor can donate to recipient, donor should be in recipient's donor list
          if (canDonate) {
            expect(donorList).toContain(donorType);
            expect(recipientList).toContain(recipientType);
          } else {
            expect(donorList).not.toContain(donorType);
            expect(recipientList).not.toContain(recipientType);
          }
        });
      });
    });

    it('should handle large numbers of compatibility checks efficiently', () => {
      const startTime = Date.now();
      const iterations = 1000;
      
      for (let i = 0; i < iterations; i++) {
        Object.values(BloodType).forEach(donorType => {
          Object.values(BloodType).forEach(recipientType => {
            isBloodTypeCompatible(donorType, recipientType);
          });
        });
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete 64,000 compatibility checks in under 100ms
      expect(duration).toBeLessThan(100);
    });
  });
});