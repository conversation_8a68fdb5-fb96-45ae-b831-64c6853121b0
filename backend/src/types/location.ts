/**
 * Location-related types and interfaces
 */

/**
 * Geographic coordinates interface
 */
export interface Coordinates {
  latitude: number;
  longitude: number;
}

/**
 * Location interface with coordinates and address
 */
export interface Location {
  coordinates: Coordinates;
  address: string;
  lastUpdated?: string;
}

/**
 * Validates if coordinates are within valid ranges
 * @param coordinates - The coordinates to validate
 * @returns boolean indicating if coordinates are valid
 */
export function validateCoordinates(coordinates: Coordinates): boolean {
  const { latitude, longitude } = coordinates;
  
  // Latitude must be between -90 and 90
  if (latitude < -90 || latitude > 90) {
    return false;
  }
  
  // Longitude must be between -180 and 180
  if (longitude < -180 || longitude > 180) {
    return false;
  }
  
  return true;
}

/**
 * Validates if a location object is properly formatted
 * @param location - The location to validate
 * @returns boolean indicating if location is valid
 */
export function validateLocation(location: Location): boolean {
  if (!location.coordinates || !location.address) {
    return false;
  }
  
  if (!validateCoordinates(location.coordinates)) {
    return false;
  }
  
  if (typeof location.address !== 'string' || location.address.trim().length === 0) {
    return false;
  }
  
  return true;
}

/**
 * Calculates distance between two coordinates using Haversine formula
 * @param coord1 - First coordinate
 * @param coord2 - Second coordinate
 * @returns Distance in kilometers
 */
export function calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(coord2.latitude - coord1.latitude);
  const dLon = toRadians(coord2.longitude - coord1.longitude);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return Math.round(distance * 100) / 100; // Round to 2 decimal places
}

/**
 * Converts degrees to radians
 * @param degrees - Degrees to convert
 * @returns Radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}