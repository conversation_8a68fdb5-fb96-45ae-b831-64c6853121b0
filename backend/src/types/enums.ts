/**
 * Core enums for the UBLOOD Clone application
 */

/**
 * Blood type enum with all standard blood types
 */
export enum BloodType {
  O_NEGATIVE = 'O-',
  O_POSITIVE = 'O+',
  A_NEGATIVE = 'A-',
  A_POSITIVE = 'A+',
  B_NEGATIVE = 'B-',
  B_POSITIVE = 'B+',
  AB_NEGATIVE = 'AB-',
  AB_POSITIVE = 'AB+'
}

/**
 * Request status enum for blood requests
 */
export enum RequestStatus {
  ACTIVE = 'active',
  FULFILLED = 'fulfilled',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired'
}

/**
 * Urgency level enum for blood requests
 */
export enum UrgencyLevel {
  CRITICAL = 'critical',
  URGENT = 'urgent',
  NORMAL = 'normal'
}

/**
 * User type enum to distinguish between donors and recipients
 */
export enum UserType {
  DONOR = 'donor',
  RECIPIENT = 'recipient',
  BOTH = 'both'
}

/**
 * Response type enum for donor responses to blood requests
 */
export enum ResponseType {
  YES = 'yes',
  NO = 'no'
}

/**
 * Notification status enum
 */
export enum NotificationStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  RESPONDED = 'responded'
}

/**
 * Contact preference enum
 */
export enum ContactPreference {
  CALL = 'call',
  TEXT = 'text'
}