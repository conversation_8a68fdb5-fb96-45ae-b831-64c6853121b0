# Appwrite Setup Guide

This guide will help you set up Appwrite for the UBLOOD Clone project.

## 🚀 Quick Setup

### 1. Create Appwrite Project

#### Option A: Appwrite Cloud (Recommended)
1. Go to [Appwrite Cloud](https://cloud.appwrite.io/)
2. Sign up or log in
3. Create a new project named "ublood-clone"
4. Note your Project ID

#### Option B: Self-hosted Appwrite
```bash
docker run -it --rm \
    --volume /var/run/docker.sock:/var/run/docker.sock \
    --volume "$(pwd)"/appwrite:/usr/src/code/appwrite:rw \
    --entrypoint="install" \
    appwrite/appwrite:1.4.13
```

### 2. Create API Key

1. Go to your project dashboard
2. Navigate to **Overview > Integrations**
3. Click **API Keys**
4. Create a new API Key with these scopes:
   - `databases.read`
   - `databases.write`
   - `collections.read`
   - `collections.write`
   - `attributes.read`
   - `attributes.write`
   - `documents.read`
   - `documents.write`
   - `users.read`
   - `users.write`
   - `messaging.read`
   - `messaging.write`

### 3. Create Database

1. Go to **Databases**
2. Create a new database named "primary"
3. Note the Database ID (should be "primary")

### 4. Update Environment Variables

Your `.env` file should already have the correct Appwrite configuration:

```env
APPWRITE_ENDPOINT=https://fra.cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=672a7af7002536ef2137
APPWRITE_API_KEY=your-api-key-here
APPWRITE_DATABASE_ID=primary
```

## 📊 Create Collections

### Option 1: Manual Creation (Recommended)

Create these collections in the Appwrite Console:

#### Users Collection
- **Collection ID**: `users`
- **Name**: Users
- **Permissions**: 
  - Read: Any
  - Create: Users
  - Update: Users
  - Delete: Users

**Attributes:**
- `phoneNumber` (String, 20 chars, required)
- `profile` (Object, required)
  - `name` (String, 100 chars, required)
  - `bloodType` (Enum: A+, A-, B+, B-, AB+, AB-, O+, O-, required)
  - `userType` (Enum: donor, recipient, both, required)
  - `isAvailable` (Boolean, required)
  - `location` (Object, required)
    - `coordinates` (Object, required)
      - `latitude` (Float, required)
      - `longitude` (Float, required)
    - `address` (String, 255 chars, optional)
  - `joinedAt` (DateTime, required)
  - `lastActive` (DateTime, optional)
- `donorStats` (Object, optional)
  - `totalDonations` (Integer, optional)
  - `lastDonationDate` (DateTime, optional)
  - `rating` (Float, optional)
  - `reviewCount` (Integer, optional)
- `notificationSettings` (Object, optional)
  - `fcmToken` (String, 255 chars, optional)
  - `pushEnabled` (Boolean, optional)
  - `maxDistance` (Integer, optional)
- `createdAt` (DateTime, required)
- `updatedAt` (DateTime, required)

#### Blood Requests Collection
- **Collection ID**: `blood_requests`
- **Name**: Blood Requests
- **Permissions**:
  - Read: Any
  - Create: Users
  - Update: Users
  - Delete: Users

**Attributes:**
- `requesterId` (String, 50 chars, required)
- `bloodType` (Enum: A+, A-, B+, B-, AB+, AB-, O+, O-, required)
- `quantity` (String, 50 chars, required)
- `urgency` (Enum: normal, urgent, critical, required)
- `location` (Object, required)
  - `coordinates` (Object, required)
    - `latitude` (Float, required)
    - `longitude` (Float, required)
  - `address` (String, 255 chars, required)
- `contactInfo` (Object, required)
  - `name` (String, 100 chars, required)
  - `phone` (String, 20 chars, required)
  - `preferredContact` (Enum: call, text, optional)
- `description` (String, 500 chars, optional)
- `status` (Enum: active, fulfilled, cancelled, expired, required)
- `expiresAt` (DateTime, required)
- `createdAt` (DateTime, required)
- `updatedAt` (DateTime, required)

#### Notifications Collection
- **Collection ID**: `notifications`
- **Name**: Notifications
- **Permissions**:
  - Read: Users
  - Create: Users
  - Update: Users

**Attributes:**
- `userId` (String, 50 chars, required)
- `type` (Enum: blood_request, donor_response, request_fulfilled, request_cancelled, required)
- `title` (String, 100 chars, required)
- `message` (String, 255 chars, required)
- `data` (Object, optional)
  - `requestId` (String, 50 chars, optional)
  - `bloodType` (String, 10 chars, optional)
  - `distance` (Integer, optional)
  - `urgency` (String, 20 chars, optional)
- `status` (Enum: sent, delivered, read, responded, required)
- `readAt` (DateTime, optional)
- `respondedAt` (DateTime, optional)
- `createdAt` (DateTime, required)

#### User Responses Collection
- **Collection ID**: `user_responses`
- **Name**: User Responses
- **Permissions**:
  - Read: Users
  - Create: Users
  - Update: Users

**Attributes:**
- `requestId` (String, 50 chars, required)
- `donorId` (String, 50 chars, required)
- `requesterId` (String, 50 chars, required)
- `response` (Enum: yes, no, required)
- `donorContact` (Object, optional)
  - `name` (String, 100 chars, optional)
  - `phone` (String, 20 chars, optional)
- `respondedAt` (DateTime, required)
- `createdAt` (DateTime, required)

#### Analytics Collection
- **Collection ID**: `analytics`
- **Name**: Analytics
- **Permissions**:
  - Read: Team "admin"
  - Create: Team "admin"
  - Update: Team "admin"

**Attributes:**
- `eventType` (String, 50 chars, required)
- `userId` (String, 50 chars, optional)
- `requestId` (String, 50 chars, optional)
- `metadata` (String, 1000 chars, optional)
- `timestamp` (DateTime, required)

### Option 2: Automated Creation (Advanced)

If your API key has sufficient permissions, you can run:

```bash
npm run setup-appwrite
```

## 🔍 Verify Setup

Run the verification script to check your Appwrite setup:

```bash
npm run verify-appwrite
```

## 📈 Create Indexes (Important for Performance)

Create these indexes in the Appwrite Console for better query performance:

### Users Collection Indexes
1. **phone_availability**
   - Attributes: `phoneNumber` (ASC), `profile.isAvailable` (ASC)
   
2. **blood_type_available**
   - Attributes: `profile.bloodType` (ASC), `profile.isAvailable` (ASC)
   
3. **user_type_available**
   - Attributes: `profile.userType` (ASC), `profile.isAvailable` (ASC)

### Blood Requests Collection Indexes
1. **status_urgency**
   - Attributes: `status` (ASC), `urgency` (ASC)
   
2. **blood_type_status**
   - Attributes: `bloodType` (ASC), `status` (ASC)
   
3. **created_desc**
   - Attributes: `createdAt` (DESC)
   
4. **expires_status**
   - Attributes: `expiresAt` (ASC), `status` (ASC)

### Notifications Collection Indexes
1. **user_status**
   - Attributes: `userId` (ASC), `status` (ASC)
   
2. **user_created**
   - Attributes: `userId` (ASC), `createdAt` (DESC)

### User Responses Collection Indexes
1. **request_responses**
   - Attributes: `requestId` (ASC), `createdAt` (DESC)
   
2. **donor_responses**
   - Attributes: `donorId` (ASC), `createdAt` (DESC)

## 🔐 Authentication Setup

### Enable Phone Authentication
1. Go to **Auth > Settings**
2. Enable **Phone** authentication
3. Configure your SMS provider (Twilio, etc.)

### Configure OAuth Providers (Optional)
1. Go to **Auth > Settings**
2. Enable desired providers (Google, Facebook, etc.)
3. Configure provider credentials

## 📱 Push Notifications Setup

### Configure FCM (Firebase Cloud Messaging)
1. Go to **Messaging > Providers**
2. Add **FCM** provider
3. Enter your Firebase Server Key

### Configure APNS (Apple Push Notifications)
1. Go to **Messaging > Providers**
2. Add **APNS** provider
3. Upload your Apple certificates

## 🧪 Testing

### Test Database Connection
```bash
npm run verify-appwrite
```

### Test API Endpoints
```bash
# Start the server
npm run dev

# Test health endpoint
curl http://localhost:3000/health

# Test API endpoint
curl http://localhost:3000/api
```

## 🚨 Troubleshooting

### Common Issues

1. **"User unauthorized" error**
   - Check API key permissions
   - Ensure all required scopes are enabled

2. **"Database not found" error**
   - Create the database in Appwrite Console
   - Verify the database ID in your .env file

3. **"Collection not found" error**
   - Create collections manually or run setup script
   - Check collection IDs match your configuration

4. **Connection timeout**
   - Check your internet connection
   - Verify the Appwrite endpoint URL

### API Key Permissions Checklist

Ensure your API key has these scopes:
- ✅ `databases.read`
- ✅ `databases.write`
- ✅ `collections.read`
- ✅ `collections.write`
- ✅ `attributes.read`
- ✅ `attributes.write`
- ✅ `documents.read`
- ✅ `documents.write`
- ✅ `users.read`
- ✅ `users.write`
- ✅ `messaging.read`
- ✅ `messaging.write`

## 🎯 Next Steps

1. ✅ Verify Appwrite connection
2. ✅ Create all required collections
3. ✅ Set up indexes for performance
4. ✅ Configure authentication providers
5. ✅ Set up push notification providers
6. ✅ Test API endpoints
7. ✅ Deploy to production

Your Appwrite backend is now ready for the UBLOOD Clone application!