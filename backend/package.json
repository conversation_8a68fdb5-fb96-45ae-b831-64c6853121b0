{"name": "ublood-backend", "version": "1.0.0", "description": "Backend API for UBLOOD Clone - Blood donation platform", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "setup-appwrite": "ts-node src/scripts/setupAppwrite.ts", "verify-appwrite": "ts-node src/scripts/verifyAppwrite.ts", "test-appwrite": "ts-node src/scripts/testAppwrite.ts", "list-appwrite-databases": "ts-node src/scripts/listAppwriteDatabases.ts", "check-appwrite-database": "ts-node src/scripts/checkAppwriteDatabase.ts", "verify-collection-attributes": "ts-node src/scripts/verifyCollectionAttributes.ts", "fix-collection-attributes": "ts-node src/scripts/fixCollectionAttributes.ts", "cleanup:test-data": "ts-node src/scripts/cleanupTestData.ts", "cleanup:dry-run": "ts-node src/scripts/cleanupTestData.ts --dry-run", "cleanup:firebase": "ts-node src/scripts/cleanupTestData.ts --firebase-only", "cleanup:appwrite": "ts-node src/scripts/cleanupTestData.ts --appwrite-only", "seed:test-data": "ts-node src/scripts/seedTestData.ts", "seed:firebase": "ts-node src/scripts/seedTestData.ts --firebase-only", "seed:appwrite": "ts-node src/scripts/seedTestData.ts --appwrite-only"}, "keywords": ["blood-donation", "express", "typescript", "firebase"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "firebase-admin": "^12.0.0", "helmet": "^7.1.0", "morgan": "^1.10.0", "node-appwrite": "^17.1.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}