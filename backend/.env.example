# Server Configuration
PORT=3000
NODE_ENV=development

# Firebase Configuration
# Option 1: Use service account file path (recommended for development)
GOOGLE_APPLICATION_CREDENTIALS=./backend/credentials/firebase-service-account.json

# Option 2: Use service account JSON string (recommended for production)
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your-project-id","private_key_id":"key-id","private_key":"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n","client_email":"<EMAIL>","client_id":"*********","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":

# Appwrite Configuration
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=your-project-id
APPWRITE_API_KEY=your-api-key
APPWRITE_DATABASE_ID=ublood_db
APPWRITE_USERS_COLLECTION_ID=users
APPWRITE_BLOOD_REQUESTS_COLLECTION_ID=blood_requests
APPWRITE_NOTIFICATIONS_COLLECTION_ID=notifications
APPWRITE_USER_RESPONSES_COLLECTION_ID=user_responses
APPWRITE_ANALYTICS_COLLECTION_ID=analytics

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100