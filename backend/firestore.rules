rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             request.auth.token.phone_number != null;
    }
    
    function isValidBloodType(bloodType) {
      return bloodType in ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    }
    
    function isValidUrgency(urgency) {
      return urgency in ['normal', 'urgent', 'critical'];
    }
    
    function isValidUserType(userType) {
      return userType in ['donor', 'recipient', 'both'];
    }

    // Users collection
    match /users/{userId} {
      // Users can read their own profile and profiles of users they're matched with
      allow read: if isAuthenticated() && (
        isOwner(userId) ||
        // Allow reading donor profiles when responding to blood requests
        exists(/databases/$(database)/documents/blood_requests/$(resource.id)) ||
        // Allow reading recipient profiles when they've requested blood
        resource.data.profile.userType in ['recipient', 'both']
      );
      
      // Users can only create/update their own profile
      allow create: if isAuthenticated() && 
                   isOwner(userId) &&
                   isValidUser() &&
                   request.resource.data.keys().hasAll(['phoneNumber', 'profile']) &&
                   isValidBloodType(request.resource.data.profile.bloodType) &&
                   isValidUserType(request.resource.data.profile.userType) &&
                   request.resource.data.phoneNumber == request.auth.token.phone_number;
      
      allow update: if isAuthenticated() && 
                   isOwner(userId) &&
                   // Prevent changing critical fields
                   request.resource.data.phoneNumber == resource.data.phoneNumber &&
                   request.resource.data.createdAt == resource.data.createdAt &&
                   // Validate blood type if being updated
                   (!request.resource.data.diff(resource.data).affectedKeys().hasAny(['profile.bloodType']) ||
                    isValidBloodType(request.resource.data.profile.bloodType)) &&
                   // Validate user type if being updated
                   (!request.resource.data.diff(resource.data).affectedKeys().hasAny(['profile.userType']) ||
                    isValidUserType(request.resource.data.profile.userType));
      
      // Users cannot delete their profiles (soft delete only)
      allow delete: if false;
    }

    // Blood requests collection
    match /blood_requests/{requestId} {
      // Anyone can read active blood requests (for donor matching)
      allow read: if isAuthenticated() && 
                 resource.data.status == 'active';
      
      // Only authenticated users can create blood requests
      allow create: if isAuthenticated() &&
                   isValidUser() &&
                   request.resource.data.keys().hasAll(['requesterId', 'bloodType', 'urgency', 'location']) &&
                   request.resource.data.requesterId == request.auth.uid &&
                   isValidBloodType(request.resource.data.bloodType) &&
                   isValidUrgency(request.resource.data.urgency) &&
                   request.resource.data.status == 'active';
      
      // Only request owner can update their requests
      allow update: if isAuthenticated() && 
                   resource.data.requesterId == request.auth.uid &&
                   // Prevent changing critical fields
                   request.resource.data.requesterId == resource.data.requesterId &&
                   request.resource.data.createdAt == resource.data.createdAt &&
                   // Allow status updates and responses
                   request.resource.data.status in ['active', 'fulfilled', 'cancelled'];
      
      // Only request owner can delete (cancel) their requests
      allow delete: if isAuthenticated() && 
                   resource.data.requesterId == request.auth.uid;
    }

    // Notifications collection
    match /notifications/{notificationId} {
      // Users can only read their own notifications
      allow read: if isAuthenticated() && 
                 resource.data.userId == request.auth.uid;
      
      // Only system can create notifications (server-side only)
      allow create: if false;
      
      // Users can update notification status (mark as read)
      allow update: if isAuthenticated() && 
                   resource.data.userId == request.auth.uid &&
                   request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'readAt', 'respondedAt']);
      
      // Users cannot delete notifications
      allow delete: if false;
    }

    // User responses collection (for tracking donor responses)
    match /user_responses/{responseId} {
      // Users can read responses to their own requests or their own responses
      allow read: if isAuthenticated() && (
        resource.data.donorId == request.auth.uid ||
        resource.data.requesterId == request.auth.uid
      );
      
      // Donors can create responses to blood requests
      allow create: if isAuthenticated() &&
                   request.resource.data.donorId == request.auth.uid &&
                   request.resource.data.response in ['yes', 'no'] &&
                   exists(/databases/$(database)/documents/blood_requests/$(request.resource.data.requestId));
      
      // Donors can update their own responses
      allow update: if isAuthenticated() && 
                   resource.data.donorId == request.auth.uid &&
                   request.resource.data.donorId == resource.data.donorId &&
                   request.resource.data.requestId == resource.data.requestId;
      
      // No deletion of responses (for audit trail)
      allow delete: if false;
    }

    // Analytics collection (admin only)
    match /analytics/{analyticsId} {
      // Only admin can read analytics
      allow read: if isAuthenticated() && 
                 request.auth.token.admin == true;
      
      // Only system can write analytics
      allow write: if false;
    }

    // Default deny all other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}