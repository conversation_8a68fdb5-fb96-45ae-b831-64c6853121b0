# Testing Environment Setup

This document describes the testing environment setup for the UBLOOD backend API, including database cleanup and test data seeding scripts.

## Overview

The testing environment includes:
- **Database cleanup scripts** for removing test data
- **Test data seeding scripts** for populating databases with sample data
- **Support for both Firebase and Appwrite** backends
- **Flexible command-line options** for different testing scenarios

## Scripts

### Database Cleanup Script

The cleanup script removes all test data from both Firebase and Appwrite databases.

#### Usage

```bash
# Clean all test data from both databases
npm run cleanup:test-data

# Dry run - see what would be deleted without actually deleting
npm run cleanup:dry-run

# Clean only Firebase data
npm run cleanup:firebase

# Clean only Appwrite data
npm run cleanup:appwrite

# Skip confirmation prompt
npx ts-node src/scripts/cleanupTestData.ts --confirm
```

#### What Gets Cleaned

**Firebase Collections:**
- `users` - All user documents
- `blood_requests` - All blood request documents  
- `notifications` - All notification documents

**Appwrite Collections:**
- `users` - All user documents
- `blood_requests` - All blood request documents
- `notifications` - All notification documents
- `user_responses` - All user response documents
- `analytics` - All analytics documents

#### Safety Features

- **Confirmation prompt** - Asks for confirmation before deleting (unless `--confirm` flag is used)
- **Dry run mode** - Shows what would be deleted without actually deleting (`--dry-run`)
- **Error handling** - Continues cleanup even if some operations fail
- **Batch processing** - Deletes documents in batches to avoid rate limits

### Test Data Seeding Script

The seeding script populates databases with realistic test data for development and testing.

#### Usage

```bash
# Seed test data to both databases (default: 5 records each)
npm run seed:test-data

# Seed only Firebase data
npm run seed:firebase

# Seed only Appwrite data
npm run seed:appwrite

# Seed custom number of records
npx ts-node src/scripts/seedTestData.ts --count 10
```

#### Generated Test Data

**Users:**
- Random phone numbers in format `+1555000XXXX`
- Names like "Test User 1", "Test User 2", etc.
- Random blood types (A+, A-, B+, B-, AB+, AB-, O+, O-)
- Random user types (donor, recipient, both)
- Random availability status (70% available)
- NYC area coordinates with slight randomization
- Random join dates within past year
- Random donation counts and ratings

**Blood Requests:**
- Linked to created test users
- Random blood types and quantities
- Random urgency levels (normal, urgent, critical)
- NYC area hospital locations
- Emergency contact information
- Expiration dates 1-3 days in the future

**Notifications:**
- Linked to created test users
- Sample notification content for blood request matching
- Includes metadata like blood type, distance, urgency

## Environment Setup

### Prerequisites

1. **Firebase Setup** (if using Firebase):
   ```bash
   # Set environment variables
   export FIREBASE_SERVICE_ACCOUNT_KEY="your-service-account-json"
   export FIREBASE_DATABASE_URL="your-firebase-database-url"
   
   # Or use service account file
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account.json"
   ```

2. **Appwrite Setup** (if using Appwrite):
   ```bash
   # Set environment variables
   export APPWRITE_ENDPOINT="your-appwrite-endpoint"
   export APPWRITE_PROJECT_ID="your-project-id"
   export APPWRITE_API_KEY="your-api-key"
   export APPWRITE_DATABASE_ID="your-database-id"
   ```

### Running the Scripts

1. **Install dependencies:**
   ```bash
   cd backend
   npm install
   ```

2. **Set up environment variables** (see prerequisites above)

3. **Run cleanup or seeding:**
   ```bash
   # Clean existing data
   npm run cleanup:dry-run  # Check what will be deleted
   npm run cleanup:test-data  # Actually delete

   # Seed new test data
   npm run seed:test-data
   ```

## Testing Workflows

### Development Workflow

```bash
# 1. Clean existing test data
npm run cleanup:test-data

# 2. Seed fresh test data
npm run seed:test-data

# 3. Run your tests or manual testing
npm run dev

# 4. Clean up when done
npm run cleanup:test-data
```

### CI/CD Workflow

```bash
# 1. Check what would be cleaned (safety check)
npm run cleanup:dry-run

# 2. Clean test environment
npm run cleanup:test-data --confirm

# 3. Seed test data
npm run seed:test-data --count 10

# 4. Run automated tests
npm test

# 5. Clean up after tests
npm run cleanup:test-data --confirm
```

### Database-Specific Testing

```bash
# Test Firebase only
npm run cleanup:firebase
npm run seed:firebase
# ... run Firebase-specific tests ...
npm run cleanup:firebase

# Test Appwrite only
npm run cleanup:appwrite
npm run seed:appwrite
# ... run Appwrite-specific tests ...
npm run cleanup:appwrite
```

## Script Options

### Cleanup Script Options

- `--firebase-only` - Clean only Firebase data
- `--appwrite-only` - Clean only Appwrite data
- `--dry-run` - Show what would be deleted without deleting
- `--confirm` - Skip confirmation prompt
- `--help` - Show help message

### Seeding Script Options

- `--firebase-only` - Seed only Firebase data
- `--appwrite-only` - Seed only Appwrite data
- `--count <number>` - Number of records to create (default: 5)
- `--help` - Show help message

## Error Handling

Both scripts include comprehensive error handling:

- **Service initialization errors** - Clear messages if database connections fail
- **Permission errors** - Helpful messages for authentication issues
- **Rate limiting** - Batch processing to avoid API rate limits
- **Partial failures** - Continue processing even if some operations fail
- **Detailed logging** - Clear success/error messages with counts

## Security Considerations

- **Environment separation** - Use different databases for test/dev/prod
- **Credential management** - Never commit credentials to version control
- **Confirmation prompts** - Prevent accidental data deletion
- **Dry run mode** - Safe way to preview operations

## Troubleshooting

### Common Issues

1. **"Services not initialized" error:**
   - Check environment variables are set correctly
   - Verify database credentials and connectivity

2. **Permission denied errors:**
   - Verify API keys have sufficient permissions
   - Check Firestore security rules allow admin access

3. **Rate limiting errors:**
   - Scripts use batch processing, but you may need to reduce batch sizes
   - Add delays between operations if needed

4. **Partial cleanup/seeding:**
   - Check the summary output for error details
   - Some operations may succeed while others fail

### Getting Help

Run any script with `--help` to see usage information:

```bash
npx ts-node src/scripts/cleanupTestData.ts --help
npx ts-node src/scripts/seedTestData.ts --help
```

## Integration with Testing Frameworks

These scripts can be integrated with testing frameworks like Jest:

```javascript
// In your test setup
import { DatabaseCleanup } from '../scripts/cleanupTestData';
import { TestDataSeeder } from '../scripts/seedTestData';

beforeAll(async () => {
  // Clean and seed before tests
  const cleanup = new DatabaseCleanup({ confirm: true });
  await cleanup.run();
  
  const seeder = new TestDataSeeder({ count: 3 });
  await seeder.run();
});

afterAll(async () => {
  // Clean up after tests
  const cleanup = new DatabaseCleanup({ confirm: true });
  await cleanup.run();
});
```