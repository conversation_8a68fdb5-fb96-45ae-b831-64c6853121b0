# UBLOOD Clone API Documentation

## Overview

The UBLOOD Clone API provides comprehensive blood donation platform functionality with support for both Firebase and Appwrite backends. This API enables blood request management, donor matching, and real-time communication between blood recipients and donors.

## Interactive API Documentation

### Swagger UI
Access the interactive API documentation at: **http://localhost:3000/api-docs/**

The Swagger UI provides:
- **Interactive API testing** - Test endpoints directly from the browser
- **Complete API reference** - All endpoints, parameters, and response schemas
- **Authentication support** - Built-in authorization for protected endpoints
- **Request/Response examples** - Sample data for all operations
- **Schema documentation** - Detailed data models and validation rules

### OpenAPI Specification
Raw OpenAPI 3.0 specification available at: **http://localhost:3000/api-docs.json**

## Authentication

The API supports multiple authentication methods:

### Firebase Authentication
```http
Authorization: Bearer <firebase-id-token>
```

### Appwrite Authentication (Alternative)
```http
x-appwrite-session: <session-token>
x-appwrite-user-id: <user-id>
```

## API Endpoints Overview

### System Endpoints
- `GET /health` - Server health check
- `GET /api` - API information and available endpoints
- `GET /api/requests/health` - Blood request service health check

### Blood Request Management

#### Firebase Backend
- `POST /api/requests/firebase` - Create blood request
- `GET /api/requests/firebase` - List blood requests (with filtering)
- `GET /api/requests/firebase/{id}` - Get specific blood request
- `PUT /api/requests/firebase/{id}` - Update blood request
- `DELETE /api/requests/firebase/{id}` - Cancel blood request

#### Appwrite Backend
- `POST /api/requests/appwrite` - Create blood request
- `GET /api/requests/appwrite` - List blood requests (with filtering)
- `GET /api/requests/appwrite/{id}` - Get specific blood request
- `PUT /api/requests/appwrite/{id}` - Update blood request
- `DELETE /api/requests/appwrite/{id}` - Cancel blood request

#### Donor Response Management (Both Backends)
- `POST /api/requests/{backend}/{id}/respond` - Respond to blood request
- `GET /api/requests/{backend}/{id}/responses` - Get all responses (requester only)
- `PUT /api/requests/{backend}/{id}/fulfill` - Mark request as fulfilled

## Quick Start Guide

### 1. Start the Server
```bash
cd backend
npm install
npm start
```

### 2. Access Swagger UI
Open your browser and navigate to: http://localhost:3000/api-docs/

### 3. Authenticate
1. Click the "Authorize" button in Swagger UI
2. Enter your Firebase ID token in the BearerAuth field
3. Click "Authorize"

### 4. Test Endpoints
1. Navigate to any endpoint in the Swagger UI
2. Click "Try it out"
3. Fill in the required parameters
4. Click "Execute" to test the API

## Example Usage

### Creating a Blood Request

**Endpoint:** `POST /api/requests/firebase`

**Request Body:**
```json
{
  "bloodType": "O+",
  "quantity": "2 units",
  "urgency": "urgent",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "address": "123 Main St, New York, NY 10001",
    "city": "New York",
    "state": "NY",
    "country": "USA",
    "postalCode": "10001"
  },
  "contactInfo": {
    "name": "John Doe",
    "phone": "+1234567890",
    "preferredContact": "call"
  },
  "description": "Urgent blood needed for surgery"
}
```

### Responding to a Blood Request

**Endpoint:** `POST /api/requests/firebase/{id}/respond`

**Request Body:**
```json
{
  "response": "yes"
}
```

### Filtering Blood Requests

**Endpoint:** `GET /api/requests/firebase`

**Query Parameters:**
- `bloodType=O+` - Filter by blood type
- `urgency=critical` - Filter by urgency level
- `status=active` - Filter by request status
- `limit=20` - Number of results (max 50)
- `offset=0` - Pagination offset

## Data Models

### BloodRequest
- **id**: Unique identifier
- **requesterId**: ID of the requester
- **bloodType**: Required blood type (A+, A-, B+, B-, AB+, AB-, O+, O-)
- **quantity**: Amount needed (e.g., "2 units", "500ml")
- **urgency**: Level (critical, urgent, normal)
- **location**: Geographic location with coordinates
- **contactInfo**: Contact details for the requester
- **status**: Current status (active, fulfilled, cancelled, expired)
- **responses**: Array of donor responses
- **fulfillmentDetails**: Details when request is completed

### Location
- **latitude**: Latitude coordinate (-90 to 90)
- **longitude**: Longitude coordinate (-180 to 180)
- **address**: Human-readable address
- **city**: City name
- **state**: State or province
- **country**: Country name
- **postalCode**: Postal or ZIP code

### ContactInfo
- **name**: Contact person name (min 2 characters)
- **phone**: International format phone number
- **preferredContact**: Preferred method (call, text)

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": ["Additional error details"]
  }
}
```

Common error codes:
- `UNAUTHORIZED` - Authentication required
- `FORBIDDEN` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `VALIDATION_ERROR` - Invalid request data
- `INTERNAL_ERROR` - Server error

## Rate Limiting

- **Limit**: 100 requests per 15 minutes per IP
- **Headers**: Rate limit information included in response headers
- **Exceeded**: Returns 429 status code when limit exceeded

## Development

### Adding New Endpoints
1. Add route handlers in appropriate files
2. Include Swagger JSDoc comments above each endpoint
3. Define request/response schemas in swagger.ts
4. Test endpoints in Swagger UI

### Swagger Documentation Format
```javascript
/**
 * @swagger
 * /api/endpoint:
 *   post:
 *     summary: Brief description
 *     description: Detailed description
 *     tags: [Tag Name]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SchemaName'
 *     responses:
 *       200:
 *         description: Success response
 */
```

## Support

For API support and questions:
- **Documentation**: http://localhost:3000/api-docs/
- **Health Check**: http://localhost:3000/health
- **OpenAPI Spec**: http://localhost:3000/api-docs.json

## Security Notes

- Always use HTTPS in production
- Validate and sanitize all input data
- Implement proper rate limiting
- Use secure authentication tokens
- Follow OWASP security guidelines