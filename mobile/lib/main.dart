import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'config/firebase_options.dart';
import 'services/appwrite_service.dart';
import 'services/auth_service.dart';
import 'services/profile_provider.dart';
import 'utils/app_theme.dart';
import 'utils/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  // Initialize Appwrite
  await AppwriteService.initialize();
  
  runApp(const UBloodApp());
}

class UBloodApp extends StatelessWidget {
  const UBloodApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthService()),
        ChangeNotifierProxyProvider<AuthService, ProfileProvider>(
          create: (context) => ProfileProvider(context.read<AuthService>()),
          update: (context, authService, previous) => 
              previous ?? ProfileProvider(authService),
        ),
      ],
      child: MaterialApp.router(
        title: 'UBlood Clone',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        routerConfig: AppRouter.router,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}