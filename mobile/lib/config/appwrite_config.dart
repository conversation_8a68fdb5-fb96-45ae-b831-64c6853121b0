import 'package:appwrite/appwrite.dart';

class AppwriteConfig {
  // Appwrite Configuration
  static const String endpoint = 'https://cloud.appwrite.io/v1';
  static const String projectId = 'your-appwrite-project-id';
  static const String databaseId = 'ublood-database';
  
  // Collections
  static const String usersCollectionId = 'users';
  static const String bloodRequestsCollectionId = 'blood_requests';
  static const String responsesCollectionId = 'responses';
  static const String notificationsCollectionId = 'notifications';
  
  // Appwrite Client Instance
  static late Client _client;
  static late Account _account;
  static late Databases _databases;
  
  static Client get client => _client;
  static Account get account => _account;
  static Databases get databases => _databases;
  
  static Future<void> initialize() async {
    _client = Client()
        .setEndpoint(endpoint)
        .setProject(projectId)
        .setSelfSigned(status: true); // Only for development
    
    _account = Account(_client);
    _databases = Databases(_client);
  }
}