import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class FirebaseAuthService extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;
  String? _verificationId;
  int? _resendToken;
  
  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;
  String? get verificationId => _verificationId;
  
  FirebaseAuthService() {
    // Listen to auth state changes
    _auth.authStateChanges().listen((User? user) {
      _user = user;
      notifyListeners();
    });
  }
  
  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // Set error message
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }
  
  // Validate phone number format
  bool isValidPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Check if it starts with + and has at least 10 digits
    if (cleanNumber.startsWith('+') && cleanNumber.length >= 11) {
      return true;
    }
    
    // Check if it's a valid length without country code
    if (!cleanNumber.startsWith('+') && cleanNumber.length >= 10) {
      return true;
    }
    
    return false;
  }
  
  // Format phone number for Firebase
  String formatPhoneNumber(String phoneNumber) {
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // If it doesn't start with +, assume it's a US number
    if (!cleanNumber.startsWith('+')) {
      cleanNumber = '+1$cleanNumber';
    }
    
    return cleanNumber;
  }
  
  // Send OTP to phone number
  Future<bool> sendOTP(String phoneNumber) async {
    if (!isValidPhoneNumber(phoneNumber)) {
      _setError('Please enter a valid phone number');
      return false;
    }
    
    _setLoading(true);
    clearError();
    
    try {
      String formattedNumber = formatPhoneNumber(phoneNumber);
      
      await _auth.verifyPhoneNumber(
        phoneNumber: formattedNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          await _signInWithCredential(credential);
        },
        verificationFailed: (FirebaseAuthException e) {
          _setError(_getErrorMessage(e));
        },
        codeSent: (String verificationId, int? resendToken) {
          _verificationId = verificationId;
          _resendToken = resendToken;
          _setLoading(false);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
          _setLoading(false);
        },
        timeout: const Duration(seconds: 60),
        forceResendingToken: _resendToken,
      );
      
      return true;
    } catch (e) {
      _setError('Failed to send OTP: ${e.toString()}');
      return false;
    }
  }
  
  // Verify OTP and sign in
  Future<bool> verifyOTP(String otp) async {
    if (_verificationId == null) {
      _setError('Verification ID not found. Please request OTP again.');
      return false;
    }
    
    if (otp.length != 6) {
      _setError('Please enter a valid 6-digit OTP');
      return false;
    }
    
    _setLoading(true);
    clearError();
    
    try {
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: _verificationId!,
        smsCode: otp,
      );
      
      await _signInWithCredential(credential);
      return true;
    } catch (e) {
      _setError('Invalid OTP. Please try again.');
      return false;
    }
  }
  
  // Sign in with credential
  Future<void> _signInWithCredential(PhoneAuthCredential credential) async {
    try {
      UserCredential userCredential = await _auth.signInWithCredential(credential);
      _user = userCredential.user;
      _setLoading(false);
    } on FirebaseAuthException catch (e) {
      _setError(_getErrorMessage(e));
    } catch (e) {
      _setError('Authentication failed: ${e.toString()}');
    }
  }
  
  // Resend OTP
  Future<bool> resendOTP(String phoneNumber) async {
    return await sendOTP(phoneNumber);
  }
  
  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      _user = null;
      _verificationId = null;
      _resendToken = null;
      clearError();
    } catch (e) {
      _setError('Failed to sign out: ${e.toString()}');
    }
  }
  
  // Get current user token
  Future<String?> getCurrentUserToken() async {
    if (_user != null) {
      try {
        return await _user!.getIdToken();
      } catch (e) {
        _setError('Failed to get user token: ${e.toString()}');
        return null;
      }
    }
    return null;
  }
  
  // Refresh user token
  Future<String?> refreshToken() async {
    if (_user != null) {
      try {
        return await _user!.getIdToken(true);
      } catch (e) {
        _setError('Failed to refresh token: ${e.toString()}');
        return null;
      }
    }
    return null;
  }
  
  // Delete user account
  Future<bool> deleteAccount() async {
    if (_user == null) {
      _setError('No user signed in');
      return false;
    }
    
    _setLoading(true);
    
    try {
      await _user!.delete();
      _user = null;
      _verificationId = null;
      _resendToken = null;
      _setLoading(false);
      return true;
    } on FirebaseAuthException catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Failed to delete account: ${e.toString()}');
      return false;
    }
  }
  
  // Get user-friendly error messages
  String _getErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'invalid-phone-number':
        return 'The phone number is not valid.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'invalid-verification-code':
        return 'The verification code is invalid.';
      case 'invalid-verification-id':
        return 'The verification ID is invalid.';
      case 'quota-exceeded':
        return 'SMS quota exceeded. Please try again later.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'operation-not-allowed':
        return 'Phone authentication is not enabled.';
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      default:
        return e.message ?? 'An unknown error occurred.';
    }
  }
}