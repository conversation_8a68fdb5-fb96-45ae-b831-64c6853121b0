import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart' as models;
import 'package:flutter/foundation.dart';
import '../services/appwrite_service.dart';

class AppwriteAuthService extends ChangeNotifier {
  models.User? _user;
  bool _isLoading = false;
  String? _errorMessage;
  String? _userId;
  String? _secret;
  
  // Getters
  models.User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;
  String? get userId => _userId;
  
  AppwriteAuthService() {
    _checkCurrentUser();
  }
  
  // Check if user is already authenticated
  Future<void> _checkCurrentUser() async {
    try {
      _user = await AppwriteService.account.get();
      notifyListeners();
    } catch (e) {
      // User not authenticated
      _user = null;
    }
  }
  
  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // Set error message
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }
  
  // Validate phone number format
  bool isValidPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Check if it starts with + and has at least 10 digits
    if (cleanNumber.startsWith('+') && cleanNumber.length >= 11) {
      return true;
    }
    
    // Check if it's a valid length without country code
    if (!cleanNumber.startsWith('+') && cleanNumber.length >= 10) {
      return true;
    }
    
    return false;
  }
  
  // Format phone number for Appwrite
  String formatPhoneNumber(String phoneNumber) {
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // If it doesn't start with +, assume it's a US number
    if (!cleanNumber.startsWith('+')) {
      cleanNumber = '+1$cleanNumber';
    }
    
    return cleanNumber;
  }
  
  // Send OTP to phone number
  Future<bool> sendOTP(String phoneNumber) async {
    if (!isValidPhoneNumber(phoneNumber)) {
      _setError('Please enter a valid phone number');
      return false;
    }
    
    _setLoading(true);
    clearError();
    
    try {
      String formattedNumber = formatPhoneNumber(phoneNumber);
      
      // Create phone session
      models.Token token = await AppwriteService.account.createPhoneToken(
        userId: ID.unique(),
        phone: formattedNumber,
      );
      
      _userId = token.userId;
      _secret = token.secret;
      _setLoading(false);
      
      return true;
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Failed to send OTP: ${e.toString()}');
      return false;
    }
  }
  
  // Verify OTP and sign in
  Future<bool> verifyOTP(String otp) async {
    if (_userId == null || _secret == null) {
      _setError('Session not found. Please request OTP again.');
      return false;
    }
    
    if (otp.length != 6) {
      _setError('Please enter a valid 6-digit OTP');
      return false;
    }
    
    _setLoading(true);
    clearError();
    
    try {
      // Create session with phone verification
      await AppwriteService.account.createSession(
        userId: _userId!,
        secret: otp,
      );
      
      // Get user details
      _user = await AppwriteService.account.get();
      _setLoading(false);
      
      return true;
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Invalid OTP. Please try again.');
      return false;
    }
  }
  
  // Resend OTP
  Future<bool> resendOTP(String phoneNumber) async {
    return await sendOTP(phoneNumber);
  }
  
  // Sign out
  Future<void> signOut() async {
    try {
      await AppwriteService.account.deleteSession(sessionId: 'current');
      _user = null;
      _userId = null;
      _secret = null;
      clearError();
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
    } catch (e) {
      _setError('Failed to sign out: ${e.toString()}');
    }
  }
  
  // Get current user session
  Future<models.Session?> getCurrentSession() async {
    try {
      return await AppwriteService.account.getSession(sessionId: 'current');
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return null;
    } catch (e) {
      _setError('Failed to get session: ${e.toString()}');
      return null;
    }
  }
  
  // Create JWT token for API authentication
  Future<String?> createJWT() async {
    try {
      models.Jwt jwt = await AppwriteService.account.createJWT();
      return jwt.jwt;
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return null;
    } catch (e) {
      _setError('Failed to create JWT: ${e.toString()}');
      return null;
    }
  }
  
  // Update phone number
  Future<bool> updatePhone(String phoneNumber, String password) async {
    if (!isValidPhoneNumber(phoneNumber)) {
      _setError('Please enter a valid phone number');
      return false;
    }
    
    _setLoading(true);
    clearError();
    
    try {
      String formattedNumber = formatPhoneNumber(phoneNumber);
      
      _user = await AppwriteService.account.updatePhone(
        phone: formattedNumber,
        password: password,
      );
      
      _setLoading(false);
      return true;
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Failed to update phone: ${e.toString()}');
      return false;
    }
  }
  
  // Update user name
  Future<bool> updateName(String name) async {
    if (name.trim().isEmpty) {
      _setError('Name cannot be empty');
      return false;
    }
    
    _setLoading(true);
    clearError();
    
    try {
      _user = await AppwriteService.account.updateName(name: name.trim());
      _setLoading(false);
      return true;
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Failed to update name: ${e.toString()}');
      return false;
    }
  }
  
  // Delete user account
  Future<bool> deleteAccount() async {
    if (_user == null) {
      _setError('No user signed in');
      return false;
    }
    
    _setLoading(true);
    
    try {
      // Note: Account deletion might not be available in all Appwrite versions
      // For now, we'll just sign out the user
      await signOut();
      _setLoading(false);
      return true;
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Failed to delete account: ${e.toString()}');
      return false;
    }
  }
  
  // Get user preferences
  Future<models.Preferences?> getPreferences() async {
    try {
      return await AppwriteService.account.getPrefs();
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return null;
    } catch (e) {
      _setError('Failed to get preferences: ${e.toString()}');
      return null;
    }
  }
  
  // Update user preferences
  Future<bool> updatePreferences(Map<String, dynamic> preferences) async {
    _setLoading(true);
    clearError();
    
    try {
      await AppwriteService.account.updatePrefs(prefs: preferences);
      _setLoading(false);
      return true;
    } on AppwriteException catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } catch (e) {
      _setError('Failed to update preferences: ${e.toString()}');
      return false;
    }
  }
  
  // Get user-friendly error messages
  String _getErrorMessage(AppwriteException e) {
    switch (e.code) {
      case 400:
        if (e.message?.contains('phone') == true) {
          return 'Invalid phone number format.';
        }
        return 'Invalid request. Please check your input.';
      case 401:
        return 'Authentication failed. Please try again.';
      case 404:
        return 'User not found.';
      case 409:
        return 'User already exists with this phone number.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      case 501:
        return 'Phone authentication is not configured.';
      default:
        return e.message ?? 'An unknown error occurred.';
    }
  }
}