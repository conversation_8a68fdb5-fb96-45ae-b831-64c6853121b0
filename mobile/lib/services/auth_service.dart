import 'package:flutter/foundation.dart';
import 'firebase_auth_service.dart';
import 'appwrite_auth_service.dart';

enum AuthProvider { firebase, appwrite }

class AuthService extends ChangeNotifier {
  final FirebaseAuthService _firebaseAuth = FirebaseAuthService();
  final AppwriteAuthService _appwriteAuth = AppwriteAuthService();
  
  AuthProvider _currentProvider = AuthProvider.firebase; // Default to Firebase
  
  // Getters that delegate to the current provider
  bool get isLoading => _currentProvider == AuthProvider.firebase 
      ? _firebaseAuth.isLoading 
      : _appwriteAuth.isLoading;
      
  String? get errorMessage => _currentProvider == AuthProvider.firebase 
      ? _firebaseAuth.errorMessage 
      : _appwriteAuth.errorMessage;
      
  bool get isAuthenticated => _currentProvider == AuthProvider.firebase 
      ? _firebaseAuth.isAuthenticated 
      : _appwriteAuth.isAuthenticated;
      
  AuthProvider get currentProvider => _currentProvider;
  
  // Get current user info
  String? get userPhone => _currentProvider == AuthProvider.firebase 
      ? _firebaseAuth.user?.phoneNumber 
      : _appwriteAuth.user?.phone;
      
  String? get userId => _currentProvider == AuthProvider.firebase 
      ? _firebaseAuth.user?.uid 
      : _appwriteAuth.user?.$id;
      
  String? get userName => _currentProvider == AuthProvider.firebase 
      ? _firebaseAuth.user?.displayName 
      : _appwriteAuth.user?.name;
  
  AuthService() {
    // Listen to changes from both providers
    _firebaseAuth.addListener(_onAuthStateChanged);
    _appwriteAuth.addListener(_onAuthStateChanged);
  }
  
  void _onAuthStateChanged() {
    notifyListeners();
  }
  
  // Switch between providers
  void switchProvider(AuthProvider provider) {
    _currentProvider = provider;
    notifyListeners();
  }
  
  // Clear error message
  void clearError() {
    _firebaseAuth.clearError();
    _appwriteAuth.clearError();
    notifyListeners();
  }
  
  // Validate phone number
  bool isValidPhoneNumber(String phoneNumber) {
    return _currentProvider == AuthProvider.firebase 
        ? _firebaseAuth.isValidPhoneNumber(phoneNumber)
        : _appwriteAuth.isValidPhoneNumber(phoneNumber);
  }
  
  // Format phone number
  String formatPhoneNumber(String phoneNumber) {
    return _currentProvider == AuthProvider.firebase 
        ? _firebaseAuth.formatPhoneNumber(phoneNumber)
        : _appwriteAuth.formatPhoneNumber(phoneNumber);
  }
  
  // Send OTP
  Future<bool> sendOTP(String phoneNumber) async {
    return _currentProvider == AuthProvider.firebase 
        ? await _firebaseAuth.sendOTP(phoneNumber)
        : await _appwriteAuth.sendOTP(phoneNumber);
  }
  
  // Verify OTP
  Future<bool> verifyOTP(String otp) async {
    return _currentProvider == AuthProvider.firebase 
        ? await _firebaseAuth.verifyOTP(otp)
        : await _appwriteAuth.verifyOTP(otp);
  }
  
  // Resend OTP
  Future<bool> resendOTP(String phoneNumber) async {
    return _currentProvider == AuthProvider.firebase 
        ? await _firebaseAuth.resendOTP(phoneNumber)
        : await _appwriteAuth.resendOTP(phoneNumber);
  }
  
  // Sign out
  Future<void> signOut() async {
    if (_currentProvider == AuthProvider.firebase) {
      await _firebaseAuth.signOut();
    } else {
      await _appwriteAuth.signOut();
    }
  }
  
  // Get authentication token
  Future<String?> getAuthToken() async {
    if (_currentProvider == AuthProvider.firebase) {
      return await _firebaseAuth.getCurrentUserToken();
    } else {
      return await _appwriteAuth.createJWT();
    }
  }
  
  // Delete account
  Future<bool> deleteAccount() async {
    return _currentProvider == AuthProvider.firebase 
        ? await _firebaseAuth.deleteAccount()
        : await _appwriteAuth.deleteAccount();
  }
  
  @override
  void dispose() {
    _firebaseAuth.removeListener(_onAuthStateChanged);
    _appwriteAuth.removeListener(_onAuthStateChanged);
    _firebaseAuth.dispose();
    _appwriteAuth.dispose();
    super.dispose();
  }
}