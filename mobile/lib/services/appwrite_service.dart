import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart' as models;
import '../config/appwrite_config.dart';

class AppwriteService {
  static bool _initialized = false;
  
  static Future<void> initialize() async {
    if (!_initialized) {
      await AppwriteConfig.initialize();
      _initialized = true;
    }
  }
  
  static Client get client => AppwriteConfig.client;
  static Account get account => AppwriteConfig.account;
  static Databases get databases => AppwriteConfig.databases;
  
  // Helper method to check if user is authenticated
  static Future<bool> isAuthenticated() async {
    try {
      await account.get();
      return true;
    } catch (e) {
      return false;
    }
  }
  
  // Helper method to get current user
  static Future<models.User?> getCurrentUser() async {
    try {
      return await account.get();
    } catch (e) {
      return null;
    }
  }
}