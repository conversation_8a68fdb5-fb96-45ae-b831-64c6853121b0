import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'profile_service.dart';
import 'auth_service.dart';

class ProfileProvider extends ChangeNotifier {
  final ProfileService _profileService;
  
  UserProfile? _userProfile;
  bool _isLoading = false;
  String? _errorMessage;

  ProfileProvider(AuthService authService) 
      : _profileService = ProfileService(authService);

  // Getters
  UserProfile? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasProfile => _userProfile != null;

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Load user profile
  Future<void> loadProfile() async {
    _setLoading(true);
    _clearError();

    try {
      _userProfile = await _profileService.getProfile();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create user profile
  Future<bool> createProfile(CreateUserData userData) async {
    _setLoading(true);
    _clearError();

    try {
      _userProfile = await _profileService.createProfile(userData);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to create profile: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? name,
    String? bloodType,
    String? userType,
    Location? location,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      _userProfile = await _profileService.updateProfile(
        name: name,
        bloodType: bloodType,
        userType: userType,
        location: location,
      );
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update profile: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update location
  Future<bool> updateLocation(Location location) async {
    _setLoading(true);
    _clearError();

    try {
      final updatedLocation = await _profileService.updateLocation(location);
      if (_userProfile != null) {
        _userProfile = UserProfile(
          name: _userProfile!.name,
          bloodType: _userProfile!.bloodType,
          location: updatedLocation,
          isAvailable: _userProfile!.isAvailable,
          userType: _userProfile!.userType,
          joinedAt: _userProfile!.joinedAt,
          lastActive: DateTime.now(),
        );
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('Failed to update location: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update availability
  Future<bool> updateAvailability(bool isAvailable) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _profileService.updateAvailability(isAvailable);
      if (success && _userProfile != null) {
        _userProfile = UserProfile(
          name: _userProfile!.name,
          bloodType: _userProfile!.bloodType,
          location: _userProfile!.location,
          isAvailable: isAvailable,
          userType: _userProfile!.userType,
          joinedAt: _userProfile!.joinedAt,
          lastActive: DateTime.now(),
        );
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to update availability: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update notification settings
  Future<bool> updateNotificationSettings({
    String? fcmToken,
    bool? pushEnabled,
    int? maxDistance,
    List<String>? urgencyLevels,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await _profileService.updateNotificationSettings(
        fcmToken: fcmToken,
        pushEnabled: pushEnabled,
        maxDistance: maxDistance,
        urgencyLevels: urgencyLevels,
      );
      return true;
    } catch (e) {
      _setError('Failed to update notification settings: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Reset profile data (for logout)
  void reset() {
    _userProfile = null;
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
}