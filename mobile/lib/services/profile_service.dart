import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import 'auth_service.dart';

class ProfileService {
  static const String baseUrl = 'http://localhost:3000/api';
  
  final AuthService _authService;
  
  ProfileService(this._authService);

  /// Get current user profile
  Future<UserProfile?> getProfile() async {
    try {
      final token = await _authService.getAuthToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/users/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return UserProfile.fromJson(data['data']['profile']);
        }
      } else if (response.statusCode == 404) {
        // User profile not found - this is expected for new users
        return null;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error']['message'] ?? 'Failed to get profile');
      }
    } catch (e) {
      throw Exception('Failed to get profile: $e');
    }
    return null;
  }

  /// Create user profile
  Future<UserProfile> createProfile(CreateUserData userData) async {
    try {
      final token = await _authService.getAuthToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      // First, create/register the user if needed
      await _registerUserIfNeeded(userData);

      // Then update the profile with complete information
      final response = await http.put(
        Uri.parse('$baseUrl/auth/firebase/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'name': userData.name,
          'bloodType': userData.bloodType,
          'userType': userData.userType,
          'location': userData.location.toJson(),
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return UserProfile.fromJson(data['data']['profile']);
        }
      }

      final errorData = json.decode(response.body);
      throw Exception(errorData['error']['message'] ?? 'Failed to create profile');
    } catch (e) {
      throw Exception('Failed to create profile: $e');
    }
  }

  /// Update user profile
  Future<UserProfile> updateProfile({
    String? name,
    String? bloodType,
    String? userType,
    Location? location,
  }) async {
    try {
      final token = await _authService.getAuthToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final Map<String, dynamic> updateData = {};
      
      if (name != null) updateData['name'] = name;
      if (bloodType != null) updateData['bloodType'] = bloodType;
      if (userType != null) updateData['userType'] = userType;
      if (location != null) updateData['location'] = location.toJson();

      final response = await http.put(
        Uri.parse('$baseUrl/users/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(updateData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return UserProfile.fromJson(data['data']['profile']);
        }
      }

      final errorData = json.decode(response.body);
      throw Exception(errorData['error']['message'] ?? 'Failed to update profile');
    } catch (e) {
      throw Exception('Failed to update profile: $e');
    }
  }

  /// Update user location
  Future<Location> updateLocation(Location location) async {
    try {
      final token = await _authService.getAuthToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.put(
        Uri.parse('$baseUrl/users/location'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'coordinates': location.coordinates.toJson(),
          'address': location.address,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return Location.fromJson(data['data']['location']);
        }
      }

      final errorData = json.decode(response.body);
      throw Exception(errorData['error']['message'] ?? 'Failed to update location');
    } catch (e) {
      throw Exception('Failed to update location: $e');
    }
  }

  /// Update availability status
  Future<bool> updateAvailability(bool isAvailable) async {
    try {
      final token = await _authService.getAuthToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.put(
        Uri.parse('$baseUrl/users/availability'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'isAvailable': isAvailable,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return data['data']['isAvailable'] ?? false;
        }
      }

      final errorData = json.decode(response.body);
      throw Exception(errorData['error']['message'] ?? 'Failed to update availability');
    } catch (e) {
      throw Exception('Failed to update availability: $e');
    }
  }

  /// Update notification settings
  Future<void> updateNotificationSettings({
    String? fcmToken,
    bool? pushEnabled,
    int? maxDistance,
    List<String>? urgencyLevels,
  }) async {
    try {
      final token = await _authService.getAuthToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final Map<String, dynamic> updateData = {};
      
      if (fcmToken != null) updateData['fcmToken'] = fcmToken;
      if (pushEnabled != null) updateData['pushEnabled'] = pushEnabled;
      if (maxDistance != null) updateData['maxDistance'] = maxDistance;
      if (urgencyLevels != null) updateData['urgencyLevels'] = urgencyLevels;

      final response = await http.put(
        Uri.parse('$baseUrl/users/notification-settings'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(updateData),
      );

      if (response.statusCode != 200) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error']['message'] ?? 'Failed to update notification settings');
      }
    } catch (e) {
      throw Exception('Failed to update notification settings: $e');
    }
  }

  /// Helper method to register user if needed
  Future<void> _registerUserIfNeeded(CreateUserData userData) async {
    try {
      final userPhone = _authService.userPhone;
      if (userPhone == null) {
        throw Exception('User phone number not available');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/auth/firebase/register'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'phoneNumber': userPhone,
          'displayName': userData.name,
        }),
      );

      // If user already exists (409), that's fine - we'll just update the profile
      if (response.statusCode != 201 && response.statusCode != 409) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error']['message'] ?? 'Failed to register user');
      }
    } catch (e) {
      // If registration fails, we'll still try to update the profile
      // This handles cases where the user might already exist
      print('Registration warning: $e');
    }
  }

  /// Validate profile data before submission
  static Map<String, String> validateProfileData({
    required String name,
    required String bloodType,
    required String userType,
    required String address,
    required Coordinates? coordinates,
  }) {
    final Map<String, String> errors = {};

    // Validate name
    if (name.trim().isEmpty) {
      errors['name'] = 'Name is required';
    } else if (name.trim().length < 2) {
      errors['name'] = 'Name must be at least 2 characters long';
    } else if (name.trim().length > 100) {
      errors['name'] = 'Name must be less than 100 characters';
    }

    // Validate blood type
    final validBloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    if (!validBloodTypes.contains(bloodType)) {
      errors['bloodType'] = 'Please select a valid blood type';
    }

    // Validate user type
    final validUserTypes = ['donor', 'recipient', 'both'];
    if (!validUserTypes.contains(userType)) {
      errors['userType'] = 'Please select a valid role';
    }

    // Validate address
    if (address.trim().isEmpty) {
      errors['address'] = 'Address is required';
    } else if (address.trim().length < 5) {
      errors['address'] = 'Please enter a more detailed address';
    }

    // Validate coordinates
    if (coordinates == null) {
      errors['location'] = 'Location coordinates are required';
    } else {
      if (coordinates.latitude < -90 || coordinates.latitude > 90) {
        errors['location'] = 'Invalid latitude coordinates';
      }
      if (coordinates.longitude < -180 || coordinates.longitude > 180) {
        errors['location'] = 'Invalid longitude coordinates';
      }
    }

    return errors;
  }
}