import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/blood_request_model.dart';
import '../config/app_config.dart';

class BloodRequestService {
  static const String _baseUrl = AppConfig.apiBaseUrl;

  // Headers for API requests
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Headers with authentication token
  Map<String, String> _headersWithAuth(String? token) => {
    ..._headers,
    if (token != null) 'Authorization': 'Bearer $token',
  };

  /// Create a new blood request
  Future<BloodRequest> createRequest(
    CreateBloodRequestData requestData, {
    String? authToken,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/requests'),
        headers: _headersWithAuth(authToken),
        body: jsonEncode(requestData.toJson()),
      );

      if (response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return BloodRequest.fromJson(responseData['data']);
        } else {
          throw BloodRequestException(
            responseData['error']['message'] ?? 'Failed to create request',
          );
        }
      } else if (response.statusCode == 400) {
        final errorData = jsonDecode(response.body);
        throw BloodRequestValidationException(
          errorData['error']['message'] ?? 'Validation failed',
          errorData['error']['details'] ?? {},
        );
      } else if (response.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else {
        throw BloodRequestException(
          'Failed to create request: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }

  /// Get user's blood requests
  Future<List<BloodRequest>> getUserRequests({
    String? authToken,
    String? status,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (status != null) queryParams['status'] = status;
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final uri = Uri.parse(
        '$_baseUrl/api/requests/my',
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await http.get(
        uri,
        headers: _headersWithAuth(authToken),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          final List<dynamic> requestsJson = responseData['data'];
          return requestsJson
              .map((json) => BloodRequest.fromJson(json))
              .toList();
        } else {
          throw BloodRequestException(
            responseData['error']['message'] ?? 'Failed to fetch requests',
          );
        }
      } else if (response.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else {
        throw BloodRequestException(
          'Failed to fetch requests: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }

  /// Get a specific blood request by ID
  Future<BloodRequest> getRequestById(
    String requestId, {
    String? authToken,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/requests/$requestId'),
        headers: _headersWithAuth(authToken),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return BloodRequest.fromJson(responseData['data']);
        } else {
          throw BloodRequestException(
            responseData['error']['message'] ?? 'Failed to fetch request',
          );
        }
      } else if (response.statusCode == 404) {
        throw BloodRequestNotFoundException('Request not found');
      } else if (response.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else {
        throw BloodRequestException(
          'Failed to fetch request: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }

  /// Update a blood request
  Future<BloodRequest> updateRequest(
    String requestId,
    Map<String, dynamic> updates, {
    String? authToken,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/api/requests/$requestId'),
        headers: _headersWithAuth(authToken),
        body: jsonEncode(updates),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return BloodRequest.fromJson(responseData['data']);
        } else {
          throw BloodRequestException(
            responseData['error']['message'] ?? 'Failed to update request',
          );
        }
      } else if (response.statusCode == 400) {
        final errorData = jsonDecode(response.body);
        throw BloodRequestValidationException(
          errorData['error']['message'] ?? 'Validation failed',
          errorData['error']['details'] ?? {},
        );
      } else if (response.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else if (response.statusCode == 403) {
        throw BloodRequestAuthException(
          'Not authorized to update this request',
        );
      } else if (response.statusCode == 404) {
        throw BloodRequestNotFoundException('Request not found');
      } else {
        throw BloodRequestException(
          'Failed to update request: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }

  /// Cancel a blood request
  Future<BloodRequest> cancelRequest(
    String requestId, {
    String? authToken,
    String? reason,
  }) async {
    try {
      final body = <String, dynamic>{'status': 'cancelled'};
      if (reason != null) {
        body['cancellationReason'] = reason;
      }

      final response = await http.put(
        Uri.parse('$_baseUrl/api/requests/$requestId/cancel'),
        headers: _headersWithAuth(authToken),
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return BloodRequest.fromJson(responseData['data']);
        } else {
          throw BloodRequestException(
            responseData['error']['message'] ?? 'Failed to cancel request',
          );
        }
      } else if (response.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else if (response.statusCode == 403) {
        throw BloodRequestAuthException(
          'Not authorized to cancel this request',
        );
      } else if (response.statusCode == 404) {
        throw BloodRequestNotFoundException('Request not found');
      } else {
        throw BloodRequestException(
          'Failed to cancel request: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }

  /// Mark a blood request as fulfilled
  Future<BloodRequest> fulfillRequest(
    String requestId, {
    String? authToken,
    String? donorId,
    String? notes,
  }) async {
    try {
      final body = <String, dynamic>{'status': 'fulfilled'};
      if (donorId != null) {
        body['fulfilledBy'] = donorId;
      }
      if (notes != null) {
        body['fulfillmentNotes'] = notes;
      }

      final response = await http.put(
        Uri.parse('$_baseUrl/api/requests/$requestId/fulfill'),
        headers: _headersWithAuth(authToken),
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return BloodRequest.fromJson(responseData['data']);
        } else {
          throw BloodRequestException(
            responseData['error']['message'] ?? 'Failed to fulfill request',
          );
        }
      } else if (response.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else if (response.statusCode == 403) {
        throw BloodRequestAuthException(
          'Not authorized to fulfill this request',
        );
      } else if (response.statusCode == 404) {
        throw BloodRequestNotFoundException('Request not found');
      } else {
        throw BloodRequestException(
          'Failed to fulfill request: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }

  /// Respond to a blood request (for donors)
  Future<void> respondToRequest(
    String requestId,
    String response, {
    String? authToken,
  }) async {
    try {
      final body = {
        'response': response, // 'yes' or 'no'
      };

      final httpResponse = await http.post(
        Uri.parse('$_baseUrl/api/requests/$requestId/respond'),
        headers: _headersWithAuth(authToken),
        body: jsonEncode(body),
      );

      if (httpResponse.statusCode == 200) {
        final responseData = jsonDecode(httpResponse.body);
        if (responseData['success'] != true) {
          throw BloodRequestException(
            responseData['error']['message'] ?? 'Failed to respond to request',
          );
        }
      } else if (httpResponse.statusCode == 400) {
        final errorData = jsonDecode(httpResponse.body);
        throw BloodRequestValidationException(
          errorData['error']['message'] ?? 'Invalid response',
          errorData['error']['details'] ?? {},
        );
      } else if (httpResponse.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else if (httpResponse.statusCode == 404) {
        throw BloodRequestNotFoundException('Request not found');
      } else {
        throw BloodRequestException(
          'Failed to respond to request: ${httpResponse.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }

  /// Get nearby blood requests (for donors)
  Future<List<BloodRequest>> getNearbyRequests({
    String? authToken,
    double? latitude,
    double? longitude,
    double? radiusKm,
    String? bloodType,
    String? urgency,
    int? limit,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (latitude != null) queryParams['latitude'] = latitude.toString();
      if (longitude != null) queryParams['longitude'] = longitude.toString();
      if (radiusKm != null) queryParams['radius'] = radiusKm.toString();
      if (bloodType != null) queryParams['bloodType'] = bloodType;
      if (urgency != null) queryParams['urgency'] = urgency;
      if (limit != null) queryParams['limit'] = limit.toString();

      final uri = Uri.parse(
        '$_baseUrl/api/requests/nearby',
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await http.get(
        uri,
        headers: _headersWithAuth(authToken),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          final List<dynamic> requestsJson = responseData['data'];
          return requestsJson
              .map((json) => BloodRequest.fromJson(json))
              .toList();
        } else {
          throw BloodRequestException(
            responseData['error']['message'] ??
                'Failed to fetch nearby requests',
          );
        }
      } else if (response.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else {
        throw BloodRequestException(
          'Failed to fetch nearby requests: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }

  /// Search blood requests with filters
  Future<List<BloodRequest>> searchRequests({
    String? authToken,
    String? bloodType,
    String? urgency,
    String? status,
    String? location,
    DateTime? createdAfter,
    DateTime? createdBefore,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (bloodType != null) queryParams['bloodType'] = bloodType;
      if (urgency != null) queryParams['urgency'] = urgency;
      if (status != null) queryParams['status'] = status;
      if (location != null) queryParams['location'] = location;
      if (createdAfter != null) {
        queryParams['createdAfter'] = createdAfter.toIso8601String();
      }
      if (createdBefore != null) {
        queryParams['createdBefore'] = createdBefore.toIso8601String();
      }
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final uri = Uri.parse(
        '$_baseUrl/api/requests/search',
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await http.get(
        uri,
        headers: _headersWithAuth(authToken),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          final List<dynamic> requestsJson = responseData['data'];
          return requestsJson
              .map((json) => BloodRequest.fromJson(json))
              .toList();
        } else {
          throw BloodRequestException(
            responseData['error']['message'] ?? 'Failed to search requests',
          );
        }
      } else if (response.statusCode == 401) {
        throw BloodRequestAuthException('Authentication required');
      } else {
        throw BloodRequestException(
          'Failed to search requests: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is BloodRequestException) {
        rethrow;
      }
      throw BloodRequestException('Network error: ${e.toString()}');
    }
  }
}

// Exception classes for better error handling
class BloodRequestException implements Exception {
  final String message;
  BloodRequestException(this.message);

  @override
  String toString() => message;
}

class BloodRequestValidationException extends BloodRequestException {
  final Map<String, dynamic> details;

  BloodRequestValidationException(super.message, this.details);
}

class BloodRequestAuthException extends BloodRequestException {
  BloodRequestAuthException(super.message);
}

class BloodRequestNotFoundException extends BloodRequestException {
  BloodRequestNotFoundException(super.message);
}
