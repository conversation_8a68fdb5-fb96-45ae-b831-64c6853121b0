import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../services/profile_provider.dart';
import '../../services/auth_service.dart';

class ProfileSetupScreen extends StatefulWidget {
  const ProfileSetupScreen({super.key});

  @override
  State<ProfileSetupScreen> createState() => _ProfileSetupScreenState();
}

class _ProfileSetupScreenState extends State<ProfileSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  
  BloodType? _selectedBloodType;
  UserType? _selectedUserType;
  Coordinates? _currentCoordinates;
  bool _isLoadingLocation = false;
  bool _isSubmitting = false;
  
  final Map<String, String> _errors = {};

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Profile'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              const Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.person_add,
                      size: 64,
                      color: Colors.red,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Set up your profile to connect with the blood donation community',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Name Field
              _buildNameField(),
              const SizedBox(height: 20),

              // Blood Type Field
              _buildBloodTypeField(),
              const SizedBox(height: 20),

              // User Type Field
              _buildUserTypeField(),
              const SizedBox(height: 20),

              // Location Section
              _buildLocationSection(),
              const SizedBox(height: 32),

              // Submit Button
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Full Name *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            hintText: 'Enter your full name',
            prefixIcon: const Icon(Icons.person),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            errorText: _errors['name'],
          ),
          validator: _validateName,
          onChanged: (value) {
            if (_errors.containsKey('name')) {
              setState(() {
                _errors.remove('name');
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildBloodTypeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Blood Type *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<BloodType>(
          value: _selectedBloodType,
          decoration: InputDecoration(
            hintText: 'Select your blood type',
            prefixIcon: const Icon(Icons.bloodtype),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            errorText: _errors['bloodType'],
          ),
          items: BloodType.values.map((BloodType type) {
            return DropdownMenuItem<BloodType>(
              value: type,
              child: Text(type.value),
            );
          }).toList(),
          onChanged: (BloodType? value) {
            setState(() {
              _selectedBloodType = value;
              _errors.remove('bloodType');
            });
          },
          validator: (value) {
            if (value == null) {
              return 'Please select your blood type';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildUserTypeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'I want to *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<UserType>(
          value: _selectedUserType,
          decoration: InputDecoration(
            hintText: 'Select your role',
            prefixIcon: const Icon(Icons.volunteer_activism),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            errorText: _errors['userType'],
          ),
          items: [
            const DropdownMenuItem<UserType>(
              value: UserType.donor,
              child: Text('Donate blood to help others'),
            ),
            const DropdownMenuItem<UserType>(
              value: UserType.recipient,
              child: Text('Find blood donors when needed'),
            ),
            const DropdownMenuItem<UserType>(
              value: UserType.both,
              child: Text('Both donate and receive blood'),
            ),
          ],
          onChanged: (UserType? value) {
            setState(() {
              _selectedUserType = value;
              _errors.remove('userType');
            });
          },
          validator: (value) {
            if (value == null) {
              return 'Please select your role';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Location *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _addressController,
          decoration: InputDecoration(
            hintText: 'Enter your address',
            prefixIcon: const Icon(Icons.location_on),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            errorText: _errors['address'],
          ),
          validator: _validateAddress,
          onChanged: (value) {
            if (_errors.containsKey('address')) {
              setState(() {
                _errors.remove('address');
              });
            }
          },
          maxLines: 2,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isLoadingLocation ? null : _getCurrentLocation,
                icon: _isLoadingLocation
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.my_location),
                label: Text(_isLoadingLocation ? 'Getting location...' : 'Use current location'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade50,
                  foregroundColor: Colors.blue.shade700,
                ),
              ),
            ),
          ],
        ),
        if (_currentCoordinates != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.green, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Location detected: ${_currentCoordinates!.latitude.toStringAsFixed(4)}, ${_currentCoordinates!.longitude.toStringAsFixed(4)}',
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        if (_errors.containsKey('location'))
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _errors['location']!,
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitProfile,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isSubmitting
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('Creating Profile...'),
                ],
              )
            : const Text(
                'Complete Profile',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters long';
    }
    if (value.trim().length > 100) {
      return 'Name must be less than 100 characters';
    }
    return null;
  }

  String? _validateAddress(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Address is required';
    }
    if (value.trim().length < 5) {
      return 'Please enter a more detailed address';
    }
    return null;
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
      _errors.remove('location');
    });

    try {
      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Check if location service is enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      setState(() {
        _currentCoordinates = Coordinates(
          latitude: position.latitude,
          longitude: position.longitude,
        );
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location detected successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errors['location'] = 'Failed to get location: ${e.toString()}';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Location error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _submitProfile() async {
    // Clear previous errors
    setState(() {
      _errors.clear();
    });

    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate location
    if (_currentCoordinates == null) {
      setState(() {
        _errors['location'] = 'Please get your current location or enter address manually';
      });
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authService = context.read<AuthService>();
      final profileProvider = context.read<ProfileProvider>();
      
      // Get phone number from auth service
      final phoneNumber = authService.userPhone;
      if (phoneNumber == null) {
        throw Exception('User phone number not available. Please login again.');
      }

      // Create user data
      final userData = CreateUserData(
        phoneNumber: phoneNumber,
        name: _nameController.text.trim(),
        bloodType: _selectedBloodType!.value,
        location: Location(
          coordinates: _currentCoordinates!,
          address: _addressController.text.trim(),
          lastUpdated: DateTime.now(),
        ),
        userType: _selectedUserType!.value,
      );

      // Call profile service to create profile
      final success = await profileProvider.createProfile(userData);

      if (success && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile created successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to appropriate dashboard based on user type
        if (_selectedUserType == UserType.donor) {
          context.go('/donor-dashboard');
        } else if (_selectedUserType == UserType.recipient) {
          context.go('/recipient-dashboard');
        } else {
          context.go('/donor-dashboard'); // Default to donor dashboard for 'both'
        }
      } else if (mounted) {
        // Show error from provider
        final errorMessage = profileProvider.errorMessage ?? 'Failed to create profile';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }
}