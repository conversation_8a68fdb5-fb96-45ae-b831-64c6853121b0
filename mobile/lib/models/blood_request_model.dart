import 'package:flutter/material.dart';
import 'user_model.dart';

class BloodRequest {
  final String? id;
  final String requesterId;
  final String bloodType;
  final String quantity;
  final String urgency;
  final Location location;
  final ContactInfo contactInfo;
  final String description;
  final String status;
  final DateTime createdAt;
  final DateTime expiresAt;
  final List<DonorResponse> responses;

  BloodRequest({
    this.id,
    required this.requesterId,
    required this.bloodType,
    required this.quantity,
    required this.urgency,
    required this.location,
    required this.contactInfo,
    required this.description,
    this.status = 'active',
    required this.createdAt,
    required this.expiresAt,
    this.responses = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'requesterId': requesterId,
      'bloodType': bloodType,
      'quantity': quantity,
      'urgency': urgency,
      'location': location.toJson(),
      'contactInfo': contactInfo.toJson(),
      'description': description,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'responses': responses.map((r) => r.toJson()).toList(),
    };
  }

  factory BloodRequest.fromJson(Map<String, dynamic> json) {
    return BloodRequest(
      id: json['id'],
      requesterId: json['requesterId'] ?? '',
      bloodType: json['bloodType'] ?? '',
      quantity: json['quantity'] ?? '',
      urgency: json['urgency'] ?? 'normal',
      location: Location.fromJson(json['location'] ?? {}),
      contactInfo: ContactInfo.fromJson(json['contactInfo'] ?? {}),
      description: json['description'] ?? '',
      status: json['status'] ?? 'active',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      expiresAt: DateTime.parse(json['expiresAt'] ?? DateTime.now().add(const Duration(hours: 6)).toIso8601String()),
      responses: (json['responses'] as List<dynamic>?)
          ?.map((r) => DonorResponse.fromJson(r))
          .toList() ?? [],
    );
  }

  BloodRequest copyWith({
    String? id,
    String? requesterId,
    String? bloodType,
    String? quantity,
    String? urgency,
    Location? location,
    ContactInfo? contactInfo,
    String? description,
    String? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    List<DonorResponse>? responses,
  }) {
    return BloodRequest(
      id: id ?? this.id,
      requesterId: requesterId ?? this.requesterId,
      bloodType: bloodType ?? this.bloodType,
      quantity: quantity ?? this.quantity,
      urgency: urgency ?? this.urgency,
      location: location ?? this.location,
      contactInfo: contactInfo ?? this.contactInfo,
      description: description ?? this.description,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      responses: responses ?? this.responses,
    );
  }
}

class ContactInfo {
  final String name;
  final String phone;
  final String preferredContact;

  ContactInfo({
    required this.name,
    required this.phone,
    this.preferredContact = 'call',
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone': phone,
      'preferredContact': preferredContact,
    };
  }

  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      preferredContact: json['preferredContact'] ?? 'call',
    );
  }
}

class DonorResponse {
  final String donorId;
  final String response;
  final DateTime respondedAt;
  final DonorContact donorContact;

  DonorResponse({
    required this.donorId,
    required this.response,
    required this.respondedAt,
    required this.donorContact,
  });

  Map<String, dynamic> toJson() {
    return {
      'donorId': donorId,
      'response': response,
      'respondedAt': respondedAt.toIso8601String(),
      'donorContact': donorContact.toJson(),
    };
  }

  factory DonorResponse.fromJson(Map<String, dynamic> json) {
    return DonorResponse(
      donorId: json['donorId'] ?? '',
      response: json['response'] ?? '',
      respondedAt: DateTime.parse(json['respondedAt'] ?? DateTime.now().toIso8601String()),
      donorContact: DonorContact.fromJson(json['donorContact'] ?? {}),
    );
  }
}

class DonorContact {
  final String name;
  final String phone;

  DonorContact({
    required this.name,
    required this.phone,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone': phone,
    };
  }

  factory DonorContact.fromJson(Map<String, dynamic> json) {
    return DonorContact(
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
    );
  }
}

class CreateBloodRequestData {
  final String bloodType;
  final String quantity;
  final String urgency;
  final Location location;
  final ContactInfo contactInfo;
  final String description;

  CreateBloodRequestData({
    required this.bloodType,
    required this.quantity,
    required this.urgency,
    required this.location,
    required this.contactInfo,
    required this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'bloodType': bloodType,
      'quantity': quantity,
      'urgency': urgency,
      'location': location.toJson(),
      'contactInfo': contactInfo.toJson(),
      'description': description,
    };
  }
}

// Enums
enum RequestUrgency {
  normal('normal'),
  urgent('urgent'),
  critical('critical');

  const RequestUrgency(this.value);
  final String value;

  static RequestUrgency? fromString(String value) {
    for (RequestUrgency urgency in RequestUrgency.values) {
      if (urgency.value == value) return urgency;
    }
    return null;
  }

  String get displayName {
    switch (this) {
      case RequestUrgency.normal:
        return 'Normal';
      case RequestUrgency.urgent:
        return 'Urgent';
      case RequestUrgency.critical:
        return 'Critical';
    }
  }

  Color get color {
    switch (this) {
      case RequestUrgency.normal:
        return const Color(0xFF4CAF50); // Green
      case RequestUrgency.urgent:
        return const Color(0xFFFF9800); // Orange
      case RequestUrgency.critical:
        return const Color(0xFFF44336); // Red
    }
  }
}

enum RequestStatus {
  active('active'),
  fulfilled('fulfilled'),
  cancelled('cancelled'),
  expired('expired');

  const RequestStatus(this.value);
  final String value;

  static RequestStatus? fromString(String value) {
    for (RequestStatus status in RequestStatus.values) {
      if (status.value == value) return status;
    }
    return null;
  }

  String get displayName {
    switch (this) {
      case RequestStatus.active:
        return 'Active';
      case RequestStatus.fulfilled:
        return 'Fulfilled';
      case RequestStatus.cancelled:
        return 'Cancelled';
      case RequestStatus.expired:
        return 'Expired';
    }
  }
}