class UserProfile {
  final String name;
  final String bloodType;
  final Location location;
  final bool isAvailable;
  final String userType;
  final DateTime joinedAt;
  final DateTime lastActive;

  UserProfile({
    required this.name,
    required this.bloodType,
    required this.location,
    required this.isAvailable,
    required this.userType,
    required this.joinedAt,
    required this.lastActive,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'bloodType': bloodType,
      'location': location.toJson(),
      'isAvailable': isAvailable,
      'userType': userType,
      'joinedAt': joinedAt.toIso8601String(),
      'lastActive': lastActive.toIso8601String(),
    };
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      name: json['name'] ?? '',
      bloodType: json['bloodType'] ?? '',
      location: Location.fromJson(json['location'] ?? {}),
      isAvailable: json['isAvailable'] ?? true,
      userType: json['userType'] ?? 'donor',
      joinedAt: DateTime.parse(json['joinedAt'] ?? DateTime.now().toIso8601String()),
      lastActive: DateTime.parse(json['lastActive'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class Location {
  final Coordinates coordinates;
  final String address;
  final DateTime? lastUpdated;

  Location({
    required this.coordinates,
    required this.address,
    this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'coordinates': coordinates.toJson(),
      'address': address,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      coordinates: Coordinates.fromJson(json['coordinates'] ?? {}),
      address: json['address'] ?? '',
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated']) 
          : null,
    );
  }
}

class Coordinates {
  final double latitude;
  final double longitude;

  Coordinates({
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory Coordinates.fromJson(Map<String, dynamic> json) {
    return Coordinates(
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
    );
  }
}

class CreateUserData {
  final String phoneNumber;
  final String name;
  final String bloodType;
  final Location location;
  final String userType;
  final String? fcmToken;

  CreateUserData({
    required this.phoneNumber,
    required this.name,
    required this.bloodType,
    required this.location,
    required this.userType,
    this.fcmToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'phoneNumber': phoneNumber,
      'name': name,
      'bloodType': bloodType,
      'location': location.toJson(),
      'userType': userType,
      'fcmToken': fcmToken,
    };
  }
}

// Enums
enum BloodType {
  oNegative('O-'),
  oPositive('O+'),
  aNegative('A-'),
  aPositive('A+'),
  bNegative('B-'),
  bPositive('B+'),
  abNegative('AB-'),
  abPositive('AB+');

  const BloodType(this.value);
  final String value;

  static BloodType? fromString(String value) {
    for (BloodType type in BloodType.values) {
      if (type.value == value) return type;
    }
    return null;
  }
}

enum UserType {
  donor('donor'),
  recipient('recipient'),
  both('both');

  const UserType(this.value);
  final String value;

  static UserType? fromString(String value) {
    for (UserType type in UserType.values) {
      if (type.value == value) return type;
    }
    return null;
  }
}