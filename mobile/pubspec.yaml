name: ublood_clone
description: A blood donation platform connecting donors and recipients in real-time.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.1
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # Firebase dependencies
  firebase_core: ^3.15.1
  firebase_auth: ^5.6.2
  firebase_messaging: ^15.2.9
  cloud_firestore: ^5.6.11
  
  # Appwrite dependencies
  appwrite: ^17.0.2
  
  # State management
  provider: ^6.1.5
  
  # Navigation
  go_router: ^16.0.0
  
  # UI and utilities
  cupertino_icons: ^1.0.8
  geolocator: ^14.0.2
  permission_handler: ^12.0.1
  url_launcher: ^6.3.2
  intl: ^0.20.2
  http: ^1.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
