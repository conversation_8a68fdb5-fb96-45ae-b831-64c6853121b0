# Design Document

## Overview

The UBLOOD Clone is a community-driven blood donation platform built as a mobile-first application that connects blood donors and recipients in real-time. The system prioritizes speed, simplicity, and accessibility to enable rapid response to urgent blood needs. The architecture uses Flutter for cross-platform mobile development, Node.js/Express backend for API services, and Firebase for real-time data synchronization and push notifications.

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │   Web Admin     │    │  Push Notification│
│   (Mobile)      │    │   Dashboard     │    │   Service (FCM)  │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────────┐
                    │    Node.js/Express API      │
                    │   (REST + WebSocket)        │
                    └─────────────┬───────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────▼───────┐    ┌─────────▼───────┐    ┌─────────▼───────┐
│   Firebase      │    │   Firebase      │    │   Redis Cache   │
│   Firestore     │    │   Auth          │    │   (Sessions)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### System Components

1. **Flutter Mobile App**: Primary user interface for donors and recipients
2. **Node.js/Express API**: RESTful API with WebSocket support for real-time updates
3. **Firebase Firestore**: NoSQL database for user profiles, requests, and responses
4. **Firebase Authentication**: Phone number-based user authentication
5. **Firebase Cloud Messaging**: Push notifications for blood requests
6. **Redis Cache**: Session management and temporary data storage
7. **Web Admin Dashboard**: Basic analytics and platform monitoring

## Components and Interfaces

### Mobile App Components (Flutter)

#### Authentication Module

```dart
// auth_service.dart
class AuthService {
  Future<void> loginWithPhone(String phoneNumber) async {
    // Firebase phone authentication
    // OTP verification
    // User session management
  }

  Future<void> verifyOTP(String verificationId, String code) async {
    // Verify OTP and create user session
    // Navigate to profile setup if new user
  }
}
```

#### User Profile Module

```dart
// profile_service.dart
class ProfileService {
  Future<void> createProfile(Map<String, dynamic> userData) async {
    // Create donor/recipient profile
    // Store location and blood type
    // Set availability status
  }

  Future<void> updateLocation(LatLng coordinates) async {
    // Update user's current location
    // Trigger proximity recalculation
  }

  Future<void> toggleAvailability(bool isAvailable) async {
    // Update donor availability status
    // Enable/disable notifications
  }
}
```

#### Blood Request Module

```dart
// blood_request_service.dart
class BloodRequestService {
  Future<void> createRequest(BloodRequest requestData) async {
    // Create new blood request
    // Trigger donor matching
    // Send notifications to nearby donors
  }

  Future<void> cancelRequest(String requestId) async {
    // Cancel active request
    // Notify responding donors
  }

  Future<void> markFulfilled(String requestId) async {
    // Mark request as completed
    // Stop further notifications
  }
}
```

#### Notification Module

```dart
// notification_service.dart
class NotificationService {
  Future<void> handleIncomingNotification(RemoteMessage notification) async {
    // Process blood request notifications
    // Display in-app notification
    // Navigate to request details
  }

  Future<void> respondToRequest(String requestId, String response) async {
    // Send donor response (yes/no)
    // Notify recipient of response
  }
}
```

### Backend API Components (Node.js/Express)

#### API Endpoints

```javascript
// User Management
POST   /api/auth/login           // Phone login
POST   /api/auth/verify          // OTP verification
GET    /api/users/profile        // Get user profile
PUT    /api/users/profile        // Update profile
PUT    /api/users/location       // Update location
PUT    /api/users/availability   // Toggle availability

// Blood Requests
POST   /api/requests             // Create blood request
GET    /api/requests/my          // Get user's requests
PUT    /api/requests/:id/cancel  // Cancel request
PUT    /api/requests/:id/fulfill // Mark as fulfilled
POST   /api/requests/:id/respond // Respond to request

// Matching & Notifications
POST   /api/matching/trigger     // Trigger donor matching
GET    /api/notifications        // Get user notifications
```

#### Core Services

**GeoMatchingService**

```javascript
class GeoMatchingService {
  async findNearbyDonors(requestData) {
    const { bloodType, location, urgency } = requestData;
    const radius = urgency === "critical" ? 50 : 25; // km

    // Query donors within radius
    const nearbyDonors = await this.queryDonorsByLocation(
      location,
      radius,
      bloodType
    );

    // Filter by availability and compatibility
    return this.filterCompatibleDonors(nearbyDonors, bloodType);
  }

  calculateDistance(point1, point2) {
    // Haversine formula for distance calculation
    // Return distance in kilometers
  }

  isBloodTypeCompatible(donorType, recipientType) {
    // Blood compatibility matrix
    const compatibility = {
      "O-": ["O-", "O+", "A-", "A+", "B-", "B+", "AB-", "AB+"],
      "O+": ["O+", "A+", "B+", "AB+"],
      "A-": ["A-", "A+", "AB-", "AB+"],
      "A+": ["A+", "AB+"],
      "B-": ["B-", "B+", "AB-", "AB+"],
      "B+": ["B+", "AB+"],
      "AB-": ["AB-", "AB+"],
      "AB+": ["AB+"],
    };

    return compatibility[donorType]?.includes(recipientType) || false;
  }
}
```

**NotificationService**

```javascript
class NotificationService {
  async sendBloodRequestNotification(donors, requestData) {
    const notifications = donors.map((donor) => ({
      to: donor.fcmToken,
      notification: {
        title: `🩸 Blood Needed: ${requestData.bloodType}`,
        body: `Someone ${requestData.distance}km away needs your help!`,
      },
      data: {
        type: "blood_request",
        requestId: requestData.id,
        bloodType: requestData.bloodType,
        distance: requestData.distance.toString(),
        urgency: requestData.urgency,
      },
    }));

    return await this.sendBatchNotifications(notifications);
  }

  async notifyRecipientOfResponse(recipientId, donorResponse) {
    // Notify recipient when donor responds
    // Include donor contact information
  }
}
```

## Data Models

### User Model

```javascript
{
  id: "user_12345",
  phoneNumber: "+1234567890",
  profile: {
    name: "John Doe",
    bloodType: "O+",
    location: {
      coordinates: {
        latitude: 40.7128,
        longitude: -74.0060
      },
      address: "New York, NY",
      lastUpdated: "2024-01-15T10:30:00Z"
    },
    isAvailable: true,
    userType: "donor", // "donor", "recipient", "both"
    joinedAt: "2024-01-15T10:30:00Z",
    lastActive: "2024-01-15T15:30:00Z"
  },
  donorStats: {
    totalDonations: 5,
    lastDonationDate: "2024-01-01T00:00:00Z",
    rating: 4.8,
    reviewCount: 12,
    badges: ["lifesaver", "quick_responder"]
  },
  notificationSettings: {
    fcmToken: "fcm_token_here",
    pushEnabled: true,
    maxDistance: 25, // km
    urgencyLevels: ["critical", "urgent"]
  }
}
```

### Blood Request Model

```javascript
{
  id: "req_12345",
  requesterId: "user_67890",
  bloodType: "O+",
  quantity: "2 units",
  urgency: "critical", // "critical", "urgent", "normal"
  location: {
    coordinates: {
      latitude: 40.7128,
      longitude: -74.0060
    },
    address: "Manhattan, NY"
  },
  contactInfo: {
    name: "Jane Smith",
    phone: "+1234567890",
    preferredContact: "call" // "call", "text"
  },
  description: "Needed for emergency surgery",
  status: "active", // "active", "fulfilled", "cancelled", "expired"
  createdAt: "2024-01-15T10:30:00Z",
  expiresAt: "2024-01-15T16:30:00Z", // 6 hours for critical
  responses: [
    {
      donorId: "user_11111",
      response: "yes",
      respondedAt: "2024-01-15T10:35:00Z",
      donorContact: {
        name: "Mike Johnson",
        phone: "+1987654321"
      }
    }
  ],
  notifiedDonors: ["user_11111", "user_22222"], // Track who was notified
  fulfillmentDetails: {
    fulfilledAt: "2024-01-15T12:00:00Z",
    fulfilledBy: "user_11111",
    recipientFeedback: {
      rating: 5,
      comment: "Very helpful and quick response!"
    }
  }
}
```

### Notification Model

```javascript
{
  id: "notif_12345",
  userId: "user_11111",
  type: "blood_request",
  title: "🩸 Blood Needed: O+",
  message: "Someone 3km away needs your help!",
  data: {
    requestId: "req_12345",
    bloodType: "O+",
    distance: 3,
    urgency: "critical"
  },
  status: "delivered", // "sent", "delivered", "read", "responded"
  createdAt: "2024-01-15T10:30:00Z",
  readAt: "2024-01-15T10:32:00Z",
  respondedAt: "2024-01-15T10:35:00Z"
}
```

### Database Schema (Firestore)

#### Collections Structure

```
/users/{userId}
/blood_requests/{requestId}
/notifications/{notificationId}
/user_responses/{responseId}
/analytics/{analyticsId}
```

#### Firestore Indexes

```javascript
// Composite indexes for efficient queries
users: [
  ["profile.bloodType", "profile.isAvailable", "profile.location.coordinates"],
  ["profile.userType", "profile.lastActive", "profile.isAvailable"],
];

blood_requests: [
  ["status", "urgency", "createdAt"],
  ["bloodType", "status", "location.coordinates"],
  ["requesterId", "status", "createdAt"],
];

notifications: [
  ["userId", "status", "createdAt"],
  ["type", "status", "createdAt"],
];
```

## Error Handling

### Error Response Format

```javascript
{
  success: false,
  error: {
    code: "VALIDATION_ERROR",
    message: "Invalid blood type specified",
    details: {
      field: "bloodType",
      value: "X+",
      allowedValues: ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"]
    }
  },
  timestamp: "2024-01-15T10:30:00Z"
}
```

### Error Handling Strategy

1. **Network Errors**

   - Implement retry logic with exponential backoff
   - Cache requests for offline scenarios
   - Show user-friendly "connection lost" messages

2. **Location Errors**

   - Fallback to manual location entry
   - Use last known location with timestamp
   - Graceful degradation for location-based features

3. **Notification Failures**

   - Retry failed notifications up to 3 times
   - Log delivery failures for analytics
   - Provide in-app fallback for critical notifications

4. **Data Validation**
   - Client-side validation for immediate feedback
   - Server-side validation for security
   - Clear error messages for user corrections

## Testing Strategy

### Unit Testing

```javascript
// GeoMatchingService Tests
describe("GeoMatchingService", () => {
  test("should find donors within specified radius", async () => {
    const request = {
      bloodType: "O+",
      location: { latitude: 40.7128, longitude: -74.006 },
      urgency: "critical",
    };

    const donors = await geoMatchingService.findNearbyDonors(request);
    expect(donors.length).toBeGreaterThan(0);
    expect(donors[0].distance).toBeLessThanOrEqual(50);
  });

  test("should respect blood type compatibility", () => {
    expect(geoMatchingService.isBloodTypeCompatible("O-", "A+")).toBe(true);
    expect(geoMatchingService.isBloodTypeCompatible("A+", "O+")).toBe(false);
  });
});
```

### Integration Testing

```javascript
// Blood Request Flow Test
describe("Blood Request Integration", () => {
  test("complete request-response cycle", async () => {
    // Create blood request
    const request = await api.post("/api/requests", requestData);
    expect(request.status).toBe(201);

    // Verify notifications sent
    const notifications = await getNotificationsForDonors();
    expect(notifications.length).toBeGreaterThan(0);

    // Simulate donor response
    const response = await api.post(
      `/api/requests/${request.body.id}/respond`,
      {
        response: "yes",
      }
    );
    expect(response.status).toBe(200);

    // Verify recipient notification
    const recipientNotifs = await getNotificationsForUser(
      requestData.requesterId
    );
    expect(recipientNotifs).toContainEqual(
      expect.objectContaining({ type: "donor_response" })
    );
  });
});
```

### Performance Requirements

- **API Response Time**: < 500ms for 95th percentile
- **Notification Delivery**: < 30 seconds for critical requests
- **Geo-matching**: < 2 seconds for donor search within 50km
- **App Launch Time**: < 3 seconds on average mobile device
- **Offline Capability**: Core features work for 24 hours without connectivity

This design provides a solid foundation for building a fast, reliable, and user-friendly blood donation platform that prioritizes community connection and rapid response to urgent needs.
