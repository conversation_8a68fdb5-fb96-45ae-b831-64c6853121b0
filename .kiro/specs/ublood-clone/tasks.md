# Implementation Plan

- [x] 1. Set up monorepo project structure

  - Create root project directory with backend and mobile folders
  - Initialize package.json for workspace management
  - Set up .gitignore for Node.js and Flutter projects
  - Create README with project structure documentation
  - _Requirements: Project foundation_

- [x] 2. Initialize Express backend project

  - Create Node.js/Express server in backend folder
  - Set up package.json with required dependencies (express, firebase-admin, cors, helmet)
  - Configure TypeScript for better development experience
  - Create basic server structure with middleware setup
  - _Requirements: All backend operations_

- [x] 3. Configure backend database services (Firebase & Appwrite)
- [x] 3.1 Configure Firebase for backend

  - Set up Firebase project and obtain service account credentials
  - Initialize Firebase Admin SDK in Express server
  - Configure Firestore database with security rules
  - Set up Firebase Authentication for server-side verification
  - _Requirements: 2.1, 2.2, 8.1_

- [x] 3.2 Configure Appwrite for backend

  - Set up Appwrite project and obtain API keys
  - Initialize Appwrite SDK in Express server
  - Configure Appwrite database with collections and permissions
  - Set up Appwrite Authentication for server-side verification
  - _Requirements: 2.1, 2.2, 8.1_

- [x] 4. Create core data models and types

  - Define TypeScript interfaces for User, BloodRequest, and Response models
  - Create blood type enum and compatibility validation
  - Implement location coordinate types and validation
  - Add request status and urgency level enums
  - _Requirements: 1.1, 2.1, 4.1, 8.2_

- [x] 5. Implement user management API (Firebase & Appwrite)
- [x] 5.1 Create user registration and authentication endpoints (Firebase)

  - Build POST /api/auth/register endpoint for user creation with Firebase
  - Implement POST /api/auth/login endpoint with Firebase token verification
  - Add middleware for Firebase JWT token validation
  - Create user profile creation and update endpoints using Firestore
  - _Requirements: 2.1, 2.2, 2.4_

- [x] 5.2 Create user registration and authentication endpoints (Appwrite)

  - Build POST /api/auth/register endpoint for user creation with Appwrite
  - Implement POST /api/auth/login endpoint with Appwrite session verification
  - Add middleware for Appwrite session validation
  - Create user profile creation and update endpoints using Appwrite Database
  - _Requirements: 2.1, 2.2, 2.4_

- [x] 5.3 Add user profile management endpoints (both Firebase & Appwrite)

  - Implement GET /api/users/profile endpoint for both backends
  - Create PUT /api/users/profile endpoint for profile updates
  - Add PUT /api/users/location endpoint for location updates
  - Implement PUT /api/users/availability endpoint for donor status
  - _Requirements: 2.4, 6.1, 6.4_

- [x] 6. Build blood request management API (Firebase & Appwrite)
- [x] 6.1 Create blood request CRUD endpoints (Firebase)

  - Implement POST /api/requests endpoint for creating blood requests using Firestore
  - Add GET /api/requests endpoint with filtering and pagination using Firestore queries
  - Create PUT /api/requests/:id endpoint for request updates
  - Implement DELETE /api/requests/:id endpoint for cancellation
  - _Requirements: 1.1, 1.4, 5.4, 5.5_

- [x] 6.2 Create blood request CRUD endpoints (Appwrite)

  - Implement POST /api/requests endpoint for creating blood requests using Appwrite Database
  - Add GET /api/requests endpoint with filtering and pagination using Appwrite queries
  - Create PUT /api/requests/:id endpoint for request updates
  - Implement DELETE /api/requests/:id endpoint for cancellation
  - _Requirements: 1.1, 1.4, 5.4, 5.5_

- [x] 6.3 Add request response handling endpoints (both backends)

  - Create POST /api/requests/:id/respond endpoint for donor responses
  - Implement GET /api/requests/:id/responses endpoint to fetch responses
  - Add PUT /api/requests/:id/fulfill endpoint to mark requests as completed
  - _Requirements: 4.2, 4.3, 5.1, 5.3_

- [x] 7. Implement geo-matching service
- [x] 7.1 Create geo-matching algorithm

  - Implement Haversine formula for distance calculations
  - Create blood type compatibility matrix and validation
  - Build donor filtering by location, availability, and blood type
  - Add radius expansion logic for critical requests
  - _Requirements: 8.1, 8.2, 8.3, 8.5_

- [x] 7.2 Build matching API endpoint

  - Create POST /api/matching/find-donors endpoint
  - Implement donor ranking by distance and availability
  - Add GET /api/matching/:requestId endpoint for request matches
  - _Requirements: 8.1, 8.4_

- [x] 8. Set up notification system (Firebase & Appwrite)
- [x] 8.1 Configure Firebase Cloud Messaging

  - Set up FCM server configuration with service account
  - Create notification payload templates for blood requests
  - Implement batch notification sending functionality
  - Add notification delivery tracking and error handling
  - _Requirements: 3.1, 3.2, 8.4_

- [x] 8.2 Configure Appwrite Messaging (alternative)

  - Set up Appwrite Messaging service configuration
  - Create notification templates for blood requests using Appwrite
  - Implement batch notification sending with Appwrite SDK
  - Add notification delivery tracking and error handling
  - _Requirements: 3.1, 3.2, 8.4_

- [x] 8.3 Build notification API endpoints (both backends)

  - Create POST /api/notifications/send endpoint for manual notifications
  - Implement GET /api/notifications/:userId endpoint for notification history
  - Add notification preference management endpoints
  - Support both Firebase and Appwrite notification backends
  - _Requirements: 3.1, 3.5, 6.2, 6.3_

- [x] 9. Integrate matching with notifications

  - Connect blood request creation with automatic donor matching
  - Trigger notifications immediately when requests are created
  - Implement notification sending when donors respond to requests
  - Add notification for request cancellations and fulfillments
  - _Requirements: 1.5, 4.3, 4.5, 5.5, 8.4_

- [x] 10. Add backend testing and validation
- [x] 10.1 Write unit tests for core services

  - Test geo-matching algorithm with various distance scenarios
  - Test blood type compatibility validation logic
  - Test user authentication and authorization middleware
  - _Requirements: All core backend functionality_

- [x] 10.2 Create API integration tests

  - Test complete user registration and profile creation flow
  - Test blood request creation and donor matching flow
  - Test notification sending and response handling
  - _Requirements: End-to-end API workflows_

- [x] 10.3 Set up API testing environment

  - Create Postman collection or REST client tests
  - Add sample data seeding for testing
  - Implement test database cleanup and setup scripts
  - _Requirements: Backend testing and validation_

- [x] 11. Initialize Flutter mobile app (Firebase & Appwrite)
- [x] 11.1 Create Flutter project structure with Firebase

  - Initialize new Flutter project in mobile folder
  - Set up proper folder structure (lib/models, lib/services, lib/screens)
  - Add Firebase dependencies (firebase_core, firebase_auth, firebase_messaging)
  - Configure Firebase for iOS and Android platforms
  - _Requirements: 2.1, 3.1_

- [x] 11.2 Add Appwrite Flutter SDK setup

  - Add Appwrite Flutter SDK dependency
  - Configure Appwrite client initialization
  - Set up Appwrite project configuration for mobile platforms
  - Create Appwrite service wrapper classes
  - _Requirements: 2.1, 3.1_

- [x] 11.3 Set up state management and navigation

  - Configure Provider or Riverpod for state management
  - Set up navigation with named routes
  - Create app theme and basic UI components
  - _Requirements: Mobile app foundation_

- [x] 12. Implement Flutter authentication (Firebase & Appwrite)
- [x] 12.1 Create Firebase authentication service

  - Implement AuthService class with Firebase phone authentication
  - Add phone number validation and OTP verification methods
  - Create user session management and token handling
  - _Requirements: 2.2, 2.5_

- [x] 12.2 Create Appwrite authentication service (alternative)

  - Implement AuthService class with Appwrite phone authentication
  - Add phone number validation and OTP verification methods
  - Create user session management and token handling with Appwrite
  - _Requirements: 2.2, 2.5_

- [x] 12.3 Build authentication UI screens (works with both backends)

  - Create phone number input screen with country code picker
  - Build OTP verification screen with resend functionality
  - Implement loading states and error handling UI
  - Add navigation between authentication screens
  - _Requirements: 2.2_

- [x] 13. Create user profile system in Flutter
- [x] 13.1 Build profile registration screens

  - Create profile setup form with name, blood type, and location fields
  - Implement location picker with GPS and manual entry options
  - Add form validation and user-friendly error messages
  - _Requirements: 2.1, 2.4_

- [x] 13.2 Implement profile service and API integration

  - Create ProfileService for API communication
  - Implement profile creation, updates, and retrieval
  - Add location coordinate handling and storage
  - _Requirements: 2.1, 2.4, 2.5_

- [ ] 14. Build blood request functionality in Flutter
- [x] 14.1 Create blood request screens

  - Build request creation form with blood type, quantity, and urgency selection
  - Implement location detection and manual entry
  - Add contact information fields and description input
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 14.2 Implement blood request service

  - Create BloodRequestService for API communication
  - Implement request creation, updates, and cancellation
  - Add request listing and filtering functionality
  - _Requirements: 1.4, 1.5, 5.4_

- [ ] 15. Add notification handling in Flutter
- [ ] 15.1 Set up Firebase Cloud Messaging

  - Configure FCM for push notifications on iOS and Android
  - Implement notification token management and registration
  - Create notification payload handling and parsing
  - _Requirements: 3.1, 3.2, 8.4_

- [ ] 15.2 Build notification UI and response system

  - Create notification display screen with request details
  - Implement yes/no response buttons for donors
  - Add distance display and urgency indicators
  - _Requirements: 3.1, 3.2, 4.1, 4.2_

- [ ] 16. Create user dashboards
- [ ] 16.1 Build donor dashboard

  - Create main dashboard showing availability status
  - Display recent notifications and response history
  - Add quick availability toggle and profile access
  - _Requirements: 6.1, 6.5, 12.1_

- [ ] 16.2 Build recipient dashboard

  - Create dashboard showing active and past requests
  - Display request status and donor response counts
  - Add quick request creation and management options
  - _Requirements: 5.1, 5.5_

- [ ] 17. Add advanced features
- [ ] 17.1 Implement donation history and achievements

  - Create donation logging and history display
  - Add achievement badges and milestone tracking
  - Implement donor rating and feedback system
  - _Requirements: 7.1, 7.2, 7.3, 10.1, 10.2_

- [ ] 17.2 Add user settings and preferences

  - Build settings screen for notification preferences
  - Add availability management and location updates
  - Implement profile editing and privacy controls
  - _Requirements: 6.1, 6.2, 6.4, 12.2_

- [ ] 18. Final testing and optimization
- [ ] 18.1 Add offline capability and error handling

  - Implement local storage for critical data caching
  - Add network error detection and retry mechanisms
  - Create graceful degradation for connectivity issues
  - _Requirements: 9.1, 9.2, 9.4_

- [ ] 18.2 Comprehensive testing and monitoring
  - Add crash reporting with Firebase Crashlytics
  - Implement usage analytics and performance monitoring
  - Create end-to-end testing for critical user flows
  - _Requirements: 11.1, 11.4, 12.5_
