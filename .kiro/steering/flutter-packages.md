---
inclusion: fileMatch
fileMatchPattern: 'mobile/**'
---

# Flutter Package Management Guidelines

## Always Use Latest Stable Versions

When adding or updating Flutter/Dart packages, always check pub.dev for the latest stable version before adding to pubspec.yaml.

### Process for Adding New Packages

1. **Check pub.dev first**: Always visit https://pub.dev/packages/[package-name] to get the latest version
2. **Use latest stable version**: Avoid pre-release versions unless specifically needed
3. **Check compatibility**: Ensure the package supports the current Flutter/Dart SDK versions
4. **Review dependencies**: Check if the package has any conflicting dependencies

### Version Specification Rules

- Use caret notation (^) for automatic minor/patch updates: `package_name: ^1.2.3`
- Avoid exact version pinning unless there's a specific compatibility issue
- Always specify minimum SDK versions in pubspec.yaml environment section

### Before Adding Any Package

Run these commands to check current package status:
```bash
cd mobile
flutter pub outdated  # Check for outdated packages
flutter pub deps      # Show dependency tree
```

### Example Process

Instead of adding:
```yaml
some_package: ^1.0.0  # Old version
```

1. Check pub.dev for `some_package`
2. Find latest stable version (e.g., 2.1.5)
3. Add the latest version:
```yaml
some_package: ^2.1.5  # Latest stable version
```

### Package Categories to Always Update

- **Firebase packages**: firebase_core, firebase_auth, cloud_firestore, firebase_messaging
- **State management**: provider, riverpod, bloc
- **Navigation**: go_router, auto_route
- **HTTP clients**: http, dio
- **UI packages**: flutter_svg, cached_network_image
- **Utilities**: intl, path_provider, shared_preferences

### When to Pin Versions

Only pin exact versions when:
- There's a known breaking change in newer versions
- Working around a specific bug that's fixed in a particular version
- Integration testing requires a specific version

### Regular Maintenance

- Run `flutter pub outdated` weekly to check for updates
- Update packages in batches to test compatibility
- Always test the app after package updates
- Update pubspec.lock by running `flutter pub get` after changes