# Technology Stack

## Architecture
- **Monorepo Structure**: Single repository containing backend API and mobile application
- **Backend**: Node.js/Express REST API with TypeScript
- **Mobile**: Flutter cross-platform application (iOS/Android)
- **Database**: Firebase Firestore for real-time data + Appwrite as alternative backend
- **Authentication**: Firebase Authentication (phone-based OTP)
- **Notifications**: Firebase Cloud Messaging (FCM)

## Backend Stack
- **Runtime**: Node.js (>=18.0.0)
- **Framework**: Express.js with TypeScript
- **Database**: Firebase Firestore, Appwrite
- **Authentication**: Firebase Admin SDK, Appwrite SDK
- **Documentation**: Swagger/OpenAPI with swagger-jsdoc
- **Testing**: Jest with ts-jest
- **Security**: Helmet, CORS, express-rate-limit
- **Logging**: Morgan

## Mobile Stack
- **Framework**: Flutter (>=3.10.0)
- **Language**: Dart (>=3.8.1)
- **State Management**: Provider pattern
- **Navigation**: go_router
- **Backend Services**: Firebase SDK, Appwrite SDK
- **Location**: geolocator package
- **Permissions**: permission_handler

## Development Tools
- **TypeScript**: Strict mode enabled with comprehensive compiler options
- **ESLint**: Code linting for backend with @typescript-eslint
- **Flutter Lints**: Standard Flutter linting rules
- **Testing**: Jest for backend, Flutter test framework for mobile
- **Build**: TypeScript compiler for backend, Flutter build system

## Common Commands

### Workspace Level
```bash
npm run dev              # Start backend development server
npm run build            # Build all projects
npm run test             # Run all tests
npm run lint             # Lint all projects
```

### Backend Development
```bash
cd backend
npm run dev              # Start with nodemon + ts-node
npm run build            # Compile TypeScript to dist/
npm run start            # Run compiled JavaScript
npm run test             # Run Jest tests
npm run lint             # ESLint with TypeScript parser
```

### Mobile Development
```bash
cd mobile
flutter pub get          # Install dependencies
flutter run              # Run on connected device/emulator
flutter test             # Run unit tests
flutter build apk        # Build Android APK
flutter build ios        # Build iOS app
```

### Database Scripts
```bash
npm run setup-appwrite           # Initialize Appwrite collections
npm run verify-appwrite          # Verify Appwrite setup
npm run seed:test-data           # Seed test data
npm run cleanup:test-data        # Clean test data
```

## Environment Configuration
- **Backend**: `.env` file with Firebase credentials, Appwrite config
- **Mobile**: Firebase configuration files for iOS/Android platforms
- **Development**: Local development with hot reload
- **Production**: Environment-specific configurations

## Code Style & Standards
- **TypeScript**: Strict type checking, no implicit any
- **API**: RESTful endpoints with Swagger documentation
- **Error Handling**: Consistent error response format
- **Validation**: Input validation on all endpoints
- **Security**: Rate limiting, CORS, helmet security headers