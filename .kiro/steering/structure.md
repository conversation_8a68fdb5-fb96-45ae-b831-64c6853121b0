# Project Structure

## Repository Organization
```
ublood-clone/
├── backend/                 # Node.js/Express API server
├── mobile/                  # Flutter mobile application  
├── docs/                    # Project documentation
├── .kiro/                   # Kiro AI assistant configuration
└── node_modules/            # Workspace dependencies
```

## Backend Structure (`backend/`)
```
backend/
├── src/
│   ├── __tests__/           # Test files
│   │   ├── integration/     # Integration tests
│   │   └── setup/           # Test setup and utilities
│   ├── config/              # Configuration files
│   │   ├── firebase.ts      # Firebase Admin SDK setup
│   │   ├── appwrite.ts      # Appwrite SDK setup
│   │   └── swagger.ts       # API documentation setup
│   ├── middleware/          # Express middleware
│   │   ├── authMiddleware.ts
│   │   ├── errorHandler.ts
│   │   └── firebaseAuth.ts
│   ├── models/              # Data models and interfaces
│   │   ├── User.ts
│   │   ├── BloodRequest.ts
│   │   └── Response.ts
│   ├── routes/              # API route handlers
│   │   ├── auth/            # Authentication routes
│   │   │   ├── firebase.ts
│   │   │   └── appwrite.ts
│   │   ├── requests/        # Blood request routes
│   │   ├── matching/        # Donor matching routes
│   │   └── notifications/   # Notification routes
│   ├── services/            # Business logic services
│   │   ├── geoMatchingService.ts
│   │   ├── notificationService.ts
│   │   └── matchingNotificationService.ts
│   ├── types/               # TypeScript type definitions
│   │   ├── bloodCompatibility.ts
│   │   ├── location.ts
│   │   └── enums.ts
│   ├── scripts/             # Utility scripts
│   └── server.ts            # Main server entry point
├── dist/                    # Compiled JavaScript output
├── credentials/             # Service account keys (gitignored)
├── package.json
├── tsconfig.json
└── jest.config.js
```

## Mobile Structure (`mobile/`)
```
mobile/
├── lib/
│   ├── config/              # App configuration
│   │   ├── firebase_options.dart
│   │   └── appwrite_config.dart
│   ├── screens/             # UI screens
│   │   ├── auth/            # Authentication screens
│   │   ├── dashboard/       # Dashboard screens
│   │   ├── requests/        # Blood request screens
│   │   └── profile/         # Profile screens
│   ├── services/            # Backend service clients
│   │   └── appwrite_service.dart
│   ├── utils/               # Utilities and helpers
│   │   ├── app_router.dart  # Navigation configuration
│   │   └── app_theme.dart   # Theme configuration
│   ├── widgets/             # Reusable UI components
│   └── main.dart            # App entry point
├── android/                 # Android platform files
├── ios/                     # iOS platform files
├── test/                    # Unit and widget tests
├── assets/                  # Images, icons, etc.
├── pubspec.yaml             # Flutter dependencies
└── analysis_options.yaml   # Dart/Flutter linting rules
```

## Key Conventions

### Backend Patterns
- **Route Organization**: Separate files for Firebase and Appwrite implementations
- **Service Layer**: Business logic separated from route handlers
- **Model Validation**: Input validation functions in model files
- **Error Handling**: Consistent error response format across all endpoints
- **Testing**: `__tests__` folders alongside source code, integration tests separate

### Mobile Patterns  
- **Screen Organization**: Grouped by feature (auth, dashboard, requests, profile)
- **Service Layer**: Backend API clients in `services/` directory
- **Configuration**: Platform-specific config files in `config/`
- **Navigation**: Centralized routing in `utils/app_router.dart`
- **Theming**: Centralized theme configuration

### File Naming
- **Backend**: camelCase for TypeScript files (e.g., `geoMatchingService.ts`)
- **Mobile**: snake_case for Dart files (e.g., `app_router.dart`)
- **Tests**: `.test.ts` suffix for backend, `_test.dart` suffix for mobile
- **Types**: Descriptive names in `types/` directory

### Import Organization
- **Backend**: Relative imports for local modules, absolute for external packages
- **Mobile**: Flutter/Dart standard import ordering (dart:, flutter:, package:, relative)

### Documentation
- **API**: Swagger/OpenAPI documentation with route handlers
- **Code**: JSDoc comments for complex functions
- **README**: Separate README files for backend and mobile subdirectories

### Environment & Config
- **Backend**: `.env` files for environment variables, separate configs per service
- **Mobile**: Platform-specific configuration files, environment-based builds
- **Secrets**: All credentials in gitignored directories/files